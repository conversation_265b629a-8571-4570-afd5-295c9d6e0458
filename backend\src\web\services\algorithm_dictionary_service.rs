//src/web/services/algorithm_dictionary_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::algorithm_dictionary::{
    AlgorithmCreateRequest, AlgorithmDictionary, AlgorithmUpdateRequest, PageRequest, PageResponse,
};


#[derive(<PERSON>lone, Debug)]
pub struct AlgorithmDictionaryService {
    pool: Arc<Pool<Postgres>>,
}

impl AlgorithmDictionaryService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    // 创建算法
    pub async fn create(&self, req: AlgorithmCreateRequest) -> Result<()> {
        let id = Uuid::new_v4().simple().to_string();

        sqlx::query!(
            r#"
            INSERT INTO algorithm_dictionary (
                algorithm_id, algorithm_name, algorithm_description, 
                is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            "#,
            id,
            req.algorithm_name,
            req.algorithm_description,
            req.is_active,
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create algorithm")?;

        Ok(())
    }

    // 更新算法
    pub async fn update(&self, req: AlgorithmUpdateRequest) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE algorithm_dictionary
            SET algorithm_name = $1,
                algorithm_description = $2,
                is_active = $3,
                updated_at = $4
            WHERE algorithm_id = $5
            "#,
            req.algorithm_name,
            req.algorithm_description,
            req.is_active,
            Utc::now(),
            req.algorithm_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update algorithm")?;

        Ok(())
    }

    // 删除算法
    pub async fn delete(&self, id: &str) -> Result<()> {
        sqlx::query!(
            "DELETE FROM algorithm_dictionary WHERE algorithm_id = $1",
            id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to delete algorithm")?;

        Ok(())
    }

    // 分页获取算法列表
    pub async fn get_page(&self, req: PageRequest) -> Result<PageResponse<AlgorithmDictionary>> {
        let offset = (req.page - 1) * req.page_size;

        let mut query = String::from(
            "SELECT COUNT(0) FROM algorithm_dictionary WHERE 1=1"
        );
      
        let mut query_sql = String::from(
            r#"
            SELECT 
                algorithm_id, algorithm_name, algorithm_description,
                is_active, created_at, updated_at
            FROM algorithm_dictionary
            WHERE 1=1
            "#
        );


        if let Some(search_text) = &req.search_text {
            let search_condition = format!(" AND (algorithm_name ILIKE '%{}%' OR algorithm_description ILIKE '%{}%')", 
                search_text, search_text);
            query.push_str(&search_condition);
            query_sql.push_str(&search_condition);
        }

        query_sql.push_str(" ORDER BY created_at DESC LIMIT $1 OFFSET $2");

        let total: i64 = sqlx::query_scalar(&query)
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        let records = sqlx::query_as::<_, AlgorithmDictionary>(&query_sql)
            .bind(req.page_size)
            .bind(offset)
            .fetch_all(self.pool.as_ref())
            .await
            .context("获取算法列表失败")?;

        Ok(PageResponse {
            records,
            total,
            page: req.page,
            page_size: req.page_size,
        })
    }

    pub async fn list(&self) -> Result<Vec<AlgorithmDictionary>> {
        let records = sqlx::query_as::<_, AlgorithmDictionary>(
            "SELECT * FROM algorithm_dictionary"
        )
        .fetch_all(self.pool.as_ref())
        .await
        .context("获取算法列表失败")?;
        Ok(records)
    }
}
