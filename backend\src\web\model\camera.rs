//src/web/model/camera.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};


#[derive(Debug,<PERSON><PERSON>,Deserialize,Serialize,sqlx::FromRow)]
pub struct Camera {
    pub camera_id: String,
    pub camera_name: String,
    pub camera_description: Option<String>,
    pub camera_ip_address: Option<String>,
    pub camera_username: Option<String>,
    pub camera_password: Option<String>,
    pub camera_rtsp_stream_url: Option<String>,
    pub location_id: Option<String>,
    pub status: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct CameraQueryRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub location_id: Option<String>,
    pub camera_name: Option<String>,
    pub search_text: Option<String>,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CameraCreateRequest {
    pub camera_name: String,
    pub camera_description: Option<String>,
    pub camera_ip_address: String,
    pub camera_username: String,
    pub camera_password: String,
    pub camera_rtsp_stream_url: String,
    pub location_id: Option<String>,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CameraUpdateRequest {
    pub camera_id: String,
    pub camera_name: Option<String>,
    pub camera_description: Option<String>,
    pub camera_ip_address: Option<String>,
    pub camera_username: Option<String>,
    pub camera_password: Option<String>,
    pub camera_rtsp_stream_url: Option<String>,
    pub location_id: Option<String>,
    pub status: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct PageResponse<T> {
    pub total: i64,
    pub items: Vec<T>,
}