// src/web/api/building_api.rs
use axum::{
    extract::{Path, Query, State},
    routing::{delete, get, post, put},
    Json, Router,
};
use std::sync::Arc;
use log::error;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    building::{Building, BuildingCreateRequest, BuildingQueryRequest, BuildingUpdateRequest, PageResponse},
};

pub fn register_building_api() -> Router<Arc<AppState>> {
    Router::new()
        .route("/building/list", get(query_buildings))
        .route("/building/create", post(create_building))
        .route("/building/update", put(update_building))
        .route("/building/delete/:id", delete(delete_building))
}

async fn query_buildings(
    State(state): State<Arc<AppState>>,
    Query(query): Query<BuildingQueryRequest>,
) -> <PERSON><PERSON><ApiResponse<PageResponse<Building>>> {
    match state.service_manager.building_service.query_buildings(query).await {
        Ok(response) => Json(ApiResponse::success(Some(response))),
        Err(e) => {
            error!("Query buildings failed: {}", e);
            Json(ApiResponse::error(format!("查询楼宇失败: {:#}", e)))
        }
    }
}

async fn create_building(
    State(state): State<Arc<AppState>>,
    Json(req): Json<BuildingCreateRequest>,
) -> Json<ApiResponse<Building>> {
    let building = Building {
        building_id: "".to_string(),
        building_name: req.building_name,
        office_id: req.office_id,
        floors: req.floors,
        is_active: true,
        created_at: None,
        updated_at: None,
    };

    match state.service_manager.building_service.create_building(building).await {
        Ok(building) => Json(ApiResponse::success(Some(building))),
        Err(e) => {
            error!("Create building failed: {}", e);
            Json(ApiResponse::error(format!("创建楼宇失败: {:#}", e)))
        }
    }
}

async fn update_building(
    State(state): State<Arc<AppState>>,
    Json(req): Json<BuildingUpdateRequest>,
) -> Json<ApiResponse<Building>> {
    match state.service_manager.building_service.update_building(req).await {
        Ok(building) => Json(ApiResponse::success(Some(building))),
        Err(e) => {
            error!("Update building failed: {}", e);
            Json(ApiResponse::error(format!("更新楼宇失败: {:#}", e)))
        }
    }
}

async fn delete_building(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.building_service.delete_building(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => {
            error!("Delete building failed: {}", e);
            Json(ApiResponse::error(format!("删除楼宇失败: {:#}", e)))
        }
    }
}