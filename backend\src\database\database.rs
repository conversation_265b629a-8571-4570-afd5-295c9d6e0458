// database/database.rs
use once_cell::sync::OnceCell;
use sqlx::{postgres::PgPoolOptions, Pool, Postgres};
use anyhow::{Result,Context};
use std::sync::Arc;
static DB_POOL: OnceCell<Pool<Postgres>> = OnceCell::new();

#[derive(Clone, Debug)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
}

impl DatabaseConfig {
    pub fn new(url: String, max_connections: u32) -> Self {
        Self {
            url,
            max_connections,
        }
    }

    pub async fn initialize(&self) -> Result<()> {
        let pool = PgPoolOptions::new()
            .max_connections(self.max_connections)
            .connect(&self.url)
            .await?;

        // 验证连接
        sqlx::query("SELECT 1").execute(&pool).await.context("Failed to connect to database")?;
        
        DB_POOL.set(pool).expect("Failed to set database pool");
        Ok(())
    }

    pub fn get_pool(&self) -> Arc<Pool<Postgres>> {
        Arc::new(DB_POOL.get().expect("Database pool not initialized").clone())
    }
}

pub fn get_db_pool() -> &'static Pool<Postgres> {
    DB_POOL.get().expect("Database pool not initialized")
}