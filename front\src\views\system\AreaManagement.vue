<!-- src/views/system/AreaManagement.vue -->
<template>
  <div class="flex h-full p-4">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 pr-4 border-r">
      <div class="mb-4">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索区划"
          clearable
          prefix-icon="Search"
        />
      </div>

      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        :load="loadNode"
        lazy
        :filter-node-method="filterNode"
        node-key="code"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="flex items-center">
            <el-icon class="mr-1">
              <Location />
            </el-icon>
            <span>{{ data.name }}</span>
            <el-tag
              size="small"
              :type="data.is_active ? 'success' : 'danger'"
              class="ml-2"
            >
              {{ data.is_active ? "启用" : "禁用" }}
            </el-tag>
          </span>
        </template>
      </el-tree>      
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 pl-4">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleAdd">新增子区划</el-button>
          <el-button type="success" @click="handleImport">导入数据</el-button>
        </div>
        <el-input
          v-model="tableFilterText"
          placeholder="搜索当前级别区划"
          class="w-64"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 当前位置显示 -->
      <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>

      <!-- 数据表格 -->
      <el-table
        :data="filteredTableData"
        border
        stripe
        class="w-full"
        v-loading="loading"
      >
        <el-table-column prop="code" label="区划编码" width="150" />
        <el-table-column prop="name" label="区划名称" min-width="150">
          <template #default="{ row }">
            <span v-if="!row.editing">{{ row.name }}</span>
            <el-input
              v-else
              v-model="row.name"
              size="small"
              @blur="handleSave(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="level" label="级别" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                :type="row.editing ? 'success' : 'primary'"
                @click="handleEdit(row)"
              >
                {{ row.editing ? "保存" : "编辑" }}
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)"
                >删除</el-button
              >
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

    </div>

    <!-- 新增区划对话框 -->
    <el-dialog title="新增区划" v-model="dialogVisible" width="500px">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="区划编码" prop="code">
          <el-input v-model="formData.code" />
        </el-form-item>
        <el-form-item label="区划名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="父级编码">
          <el-input v-model="formData.parent_code" disabled />
        </el-form-item>
        <el-form-item label="级别">
          <el-input-number
            v-model="formData.level"
            :min="1"
            :max="5"
            disabled
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const treeProps = {
  label: 'name',
  children: 'children',
  isLeaf: 'leaf'
};

// 表格相关
const loading = ref(false);
const tableData = ref([]);
const tableFilterText = ref("");
const currentNode = ref(null);
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);

// 表单相关
const dialogVisible = ref(false);
const formRef = ref(null);
const formData = ref({
  code: "",
  name: "",
  parent_code: "",
  level: 1,
  is_active: true,
});

const formRules = {
  code: [{ required: true, message: "请输入区划编码", trigger: "blur" }],
  name: [{ required: true, message: "请输入区划名称", trigger: "blur" }],
};

// 计算属性
const currentPath = computed(() => {
  if (!currentNode.value) return "未选择区划";
  return ` ${getNodePath(currentNode.value)}`;
});

const filteredTableData = computed(() => {
  const data = tableData.value;
  if (!tableFilterText.value) return data;
  return data.filter(
    (item) =>
      item.name.includes(tableFilterText.value) ||
      item.code.includes(tableFilterText.value)
  );
});

// 监听器
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val);
});


const loadNode = async (node, resolve) => {
  const loading = ElLoading.service({
    target: treeRef.value?.$el,
  });
  
  try {
    if (node.level === 0) {
      // 加载省级数据
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map(item => ({
        ...item,
        id: item.code,
        leaf: false
      }));
      resolve(areas);
    } else {
      // 加载下级区划
      const { data } = node;
      const res = await axios.get("/sys_area_info/lazy_tree_node", {
        params: { parent_code: data.code }
      });
      const areas = res.data.data.map(item => ({
        ...item,
        id: item.code,
        // 根据级别判断是否还有子节点
        leaf: item.level >= 4  // 假设区划最多4级
      }));
      resolve(areas);
    }
  } catch (error) {
    ElMessage.error("加载数据失败");
    resolve([]);
  } finally {
    loading.close();
  }
};


const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value) || data.code.includes(value);
};

const getNodePath = (node) => {
  const path = [];
  let current = node;
  while (current) {
    path.unshift(current.name);
    current = current.parent;
  }
  return path.join(" / ");
};

const handleNodeClick = (data) => {
  currentNode.value = data;
  loadTableData(data.code);
};

const loadTableData = async (parentCode) => {
  loading.value = true;
  try {
    const res = await axios.get(`/sys_area_info/detail/${parentCode}`);
    tableData.value = res.data.data;
    totalCount.value = res.data.data.length;
  } catch (error) {
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

const handleAdd = () => {
  if (!currentNode.value) {
    ElMessage.warning("请先选择一个区划节点");
    return;
  }

  formData.value = {
    code: "",
    name: "",
    parent_code: currentNode.value.code,
    level: currentNode.value.level + 1,
    is_active: true,
  };
  dialogVisible.value = true;
};


const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    await axios.post("/sys_area_info/create", formData.value);
    ElMessage.success("创建成功");
    dialogVisible.value = false;
    // 刷新当前节点数据
    if (currentNode.value) {
      currentNode.value.loadData();
    }
    loadTableData(currentNode.value.code);
  } catch (error) {
    ElMessage.error("创建失败");
  }
};

const handleEdit = (row) => {
  if (row.editing) {
    handleSave(row);
  } else {
    row.editing = true;
  }
};

const handleSave = async (row) => {
  try {
    await axios.post("/sys_area_info/update", {
      code: row.code,
      name: row.name,
      is_active: row.is_active,
    });
    row.editing = false;
    ElMessage.success("更新成功");
    loadTreeData();
    loadTableData(currentNode.value.code);
  } catch (error) {
    ElMessage.error("更新失败");
  }
};


const handleStatusChange = async (row) => {
  try {
    await axios.post("/sys_area_info/update", {
      code: row.code,
      name: row.name,
      is_active: row.is_active,
    });
    ElMessage.success("状态更新成功");
    // 刷新当前节点的父节点数据
    if (currentNode.value?.parent) {
      currentNode.value.parent.loadData();
    }
  } catch (error) {
    row.is_active = !row.is_active;
    ElMessage.error("状态更新失败");
  }
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确定要删除该区划吗？", "提示", {
      type: "warning",
      appendTo: ".el-table",
    });
    await axios.delete(`/sys_area_info/delete/${row.code}`);
    ElMessage.success("删除成功");
    // 刷新当前节点的父节点数据
    if (currentNode.value?.parent) {
      currentNode.value.parent.loadData();
    }
    loadTableData(currentNode.value.code);
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

const handleImport = async () => {
  try {
    await axios.post("/sys_area_info/import");
    ElMessage.success("导入成功");
    // 刷新整个树
    treeRef.value?.store.setData([]);
    treeRef.value?.store.setData(await loadNode({ level: 0 }, (data) => data));
  } catch (error) {
    ElMessage.error("导入失败");
  }
};

// 生命周期钩子
onMounted(() => {
  //loadTreeData();
});
</script>

<style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

.el-message-box__wrapper {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}
</style>
