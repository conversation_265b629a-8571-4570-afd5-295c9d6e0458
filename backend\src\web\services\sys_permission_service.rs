//src/web/services/sys_permission_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use log::info;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::sys_permission::{
    PageRequest, PageResponse, PermissionCreateRequest, PermissionUpdateRequest, SysPermissionEntiry,
};

#[derive(Clone, Debug)]
pub struct SysPermissionService {
    pool: Arc<Pool<Postgres>>,
}

impl SysPermissionService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    pub async fn create(&self, req: PermissionCreateRequest) -> Result<()> {
        let id = Uuid::new_v4().simple().to_string();

        info!("Creating permission with id: {}", id);

        sqlx::query!(
            r#"
            INSERT INTO sys_permission (
                permission_id, parent_id, permission_name, permission_code,
                permission_type, component_path, path, redirect, icon,
                sort_order, is_hidden, permission_description, is_active,
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            "#,
            id,
            req.parent_id,
            req.permission_name,
            req.permission_code,
            req.permission_type,
            req.component_path,
            req.path,
            req.redirect,
            req.icon,
            req.sort_order,
            req.is_hidden,
            req.permission_description,
            req.is_active.unwrap_or(true),
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create permission")?;

        info!("Successfully created permission with id: {}", id);
        Ok(())
    }

    pub async fn update(&self, req: PermissionUpdateRequest) -> Result<()> {
        info!("Updating permission with id: {}", req.permission_id);

        sqlx::query!(
            r#"
            UPDATE sys_permission
            SET parent_id = $1,
                permission_name = $2,
                permission_code = $3,
                permission_type = $4,
                component_path = $5,
                path = $6,
                redirect = $7,
                icon = $8,
                sort_order = $9,
                is_hidden = $10,
                permission_description = $11,
                is_active = $12,
                updated_at = $13
            WHERE permission_id = $14
            "#,
            req.parent_id,
            req.permission_name,
            req.permission_code,
            req.permission_type,
            req.component_path,
            req.path,
            req.redirect,
            req.icon,
            req.sort_order,
            req.is_hidden,
            req.permission_description,
            req.is_active,
            Utc::now(),
            req.permission_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update permission")?;

        info!("Successfully updated permission with id: {}", req.permission_id);
        Ok(())
    }

    pub async fn delete(&self, id: &str) -> Result<()> {
        info!("Deleting permission with id: {}", id);

        sqlx::query!("DELETE FROM sys_permission WHERE permission_id = $1", id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete permission")?;

        info!("Successfully deleted permission with id: {}", id);
        Ok(())
    }

    pub async fn get_page(&self, req: PageRequest) -> Result<PageResponse<SysPermissionEntiry>> {
        let offset = (req.page - 1) * req.page_size;

        let mut query = String::from("SELECT COUNT(*) FROM sys_permission WHERE 1=1");
        let mut query_sql = String::from(
            r#"
            SELECT *
            FROM sys_permission
            WHERE 1=1
            "#,
        );

        if let Some(search_text) = &req.search_text {
            let search_condition = format!(
                " AND (permission_name ILIKE '%{}%' OR permission_code ILIKE '%{}%')",
                search_text, search_text
            );
            query.push_str(&search_condition);
            query_sql.push_str(&search_condition);
        }

        query_sql.push_str(" ORDER BY sort_order ASC, created_at DESC LIMIT $1 OFFSET $2");

        let total: i64 = sqlx::query_scalar(&query)
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        let records = sqlx::query_as::<_, SysPermissionEntiry>(&query_sql)
            .bind(req.page_size)
            .bind(offset)
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to get permissions")?;

        Ok(PageResponse {
            records,
            total,
            page: req.page,
            page_size: req.page_size,
        })
    }
}