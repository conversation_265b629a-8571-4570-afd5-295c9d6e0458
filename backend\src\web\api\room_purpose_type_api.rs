//src/web/api/room_purpose_type_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete, post},
    Json, Router,
};
use std::sync::Arc;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    room_purpose_type::{
        PageRequest, PageResponse, RoomPurposeType, RoomPurposeTypeCreateRequest,
        RoomPurposeTypeUpdateRequest,
    },
};



pub fn register_room_purpose_type_api() -> Router<Arc<AppState>> {
    Router::new().nest("/room_purpose_type", room_purpose_type_routes())
}

fn room_purpose_type_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create))
        .route("/update", post(update))
        .route("/delete/:id", delete(deleteRoom))
        .route("/page", post(get_page))
}

async fn create(
    State(state): State<Arc<AppState>>,
    Json(req): Json<RoomPurposeTypeCreateRequest>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .room_purpose_type_service
        .create(req)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("创建失败: {:#}", e))),
    }
}

async fn update(
    State(state): State<Arc<AppState>>,
    Json(req): Json<RoomPurposeTypeUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .room_purpose_type_service
        .update(req)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("更新失败: {:#}", e))),
    }
}

async fn deleteRoom(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .room_purpose_type_service
        .delete(&id)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除失败: {:#}", e))),
    }
}

async fn get_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<PageRequest>,
) -> Json<ApiResponse<PageResponse<RoomPurposeType>>> {
    match state
        .service_manager
        .room_purpose_type_service
        .get_page(req)
        .await
    {
        Ok(page) => Json(ApiResponse::success(Some(page))),
        Err(e) => Json(ApiResponse::error(format!("获取失败: {:#}", e))),
    }
}