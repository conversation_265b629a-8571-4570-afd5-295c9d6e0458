// src/webrtc/api/webrtc_stream_api.rs
use axum::{
    extract::{Path, State},
    response::IntoResponse,
    routing::post,
    <PERSON><PERSON>, Router,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use log::info;

use crate::web::model::appstate::AppState;
use crate::web::model::common_model::ApiResponse;
use crate::webrtc::model::webrtc_stream::OverlayInfo;

#[derive(Debug, Deserialize)]
pub struct WebrtcOfferRequest {
    pub camera_id: String,
    pub sdp: String,
    pub overlay_info: Option<OverlayInfo>,
}

#[derive(Debug, Serialize)]
pub struct WebrtcOfferResponse {
    stream_id: String,
    sdp: String,
}

#[derive(Debug, Deserialize)]
pub struct WebrtcAnswerRequest {
    sdp: String,
}

#[derive(Debug, Deserialize)]
pub struct IceCandidateRequest {
    candidate: String,
    sdp_mid: Option<String>,
    sdp_m_line_index: Option<u16>,
}

#[derive(Debug, Deserialize)]
pub struct DisconnectRequest {
    pub camera_id: String,
}


pub fn register_webrtc_stream_api() -> Router<Arc<AppState>> {
    Router::new()
    .route("/webrtc/stream/offer", post(handle_offer))
    .route("/webrtc/stream/answer/:id", post(handle_answer))
    .route("/webrtc/stream/ice/:id", post(handle_ice_candidate))
    .route("/webrtc/stream/disconnect", post(handle_disconnect))
}

async fn handle_offer(
    State(state): State<Arc<AppState>>,
    Json(request): Json<WebrtcOfferRequest>,
) -> impl IntoResponse {
    info!("Received offer request: {:?}", request);
    // 获取摄像头信息
    let camera_info = match state
        .service_manager
        .camera_service
        .get_camera_by_id(&request.camera_id)
        .await
    {
        Ok(info) => info,
        Err(e) => return Json(ApiResponse::error(e.to_string())),
    };

    info!("Camera info: {:?}", camera_info);

    // 创建 WebRTC 连接
    match state
        .service_manager
        .webrtc_stream_service
        .create_peer_connection(
            &request.camera_id,
            &camera_info.camera_rtsp_stream_url.unwrap_or_default(),
            request.sdp,
            request.overlay_info,
        )
        .await
    {
        Ok(sdp) => Json(ApiResponse::success(Some(WebrtcOfferResponse {
            stream_id: request.camera_id,
            sdp,
        }))),
        Err(e) => Json(ApiResponse::error(e.to_string())),
    }
}

async fn handle_answer(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
    Json(request): Json<WebrtcAnswerRequest>,
) -> impl IntoResponse {
    match state
        .service_manager
        .webrtc_stream_service
        .handle_answer(&id, request.sdp)
        .await
    {
        Ok(_) => Json(ApiResponse::<()>::success(None)),
        Err(e) => Json(ApiResponse::<()>::error(e.to_string())),
    }
}

async fn handle_ice_candidate(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
    Json(request): Json<IceCandidateRequest>,
) -> impl IntoResponse {
    match state
        .service_manager
        .webrtc_stream_service
        .handle_ice_candidate(&id, request.candidate, request.sdp_mid, request.sdp_m_line_index)
        .await
    {
        Ok(_) => Json(ApiResponse::<()>::success(None)),
        Err(e) => Json(ApiResponse::<()>::error(e.to_string())),
    }
}

async fn handle_disconnect(
    State(state): State<Arc<AppState>>,
    Json(request): Json<DisconnectRequest>,
) -> impl IntoResponse {
    match state
        .service_manager
        .webrtc_stream_service
        .disconnect_viewer(&request.camera_id)
        .await
    {
        Ok(_) => Json(ApiResponse::<()>::success(None)),
        Err(e) => Json(ApiResponse::<()>::error(e.to_string())),
    }
}