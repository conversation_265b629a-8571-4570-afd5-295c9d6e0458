//src/model/webrtc_stream.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use std::time::SystemTime;
use webrtc::track::track_local::track_local_static_sample::TrackLocalStaticSample;
use webrtc::peer_connection::RTCPeerConnection;
use std::sync::atomic::AtomicI32;
use std::time::Instant;

#[derive(Clone,Debug)]
pub struct SharedStreamInfo {
    pub camera_id: String,
    pub rtsp_url: String,
    pub video_track: Arc<TrackLocalStaticSample>,
    pub viewer_count: Arc<AtomicI32>,
    pub last_activity: Arc<RwLock<Instant>>,
    pub overlay_info: Arc<RwLock<Option<OverlayInfo>>>,
    pub start_time: SystemTime,
    pub peer_connections: Arc<RwLock<HashMap<String, Arc<RTCPeerConnection>>>>,
}

#[derive(Debug, Clone)]
pub struct WebrtcStreamState {
    pub active_streams: Arc<RwLock<HashMap<String, SharedStreamInfo>>>,
    pub stream_channels: Arc<RwLock<HashMap<String, broadcast::Sender<Vec<u8>>>>>,
}

//叠加文字、形状信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OverlayInfo {
    pub texts: Vec<TextOverlay>,   //叠加文字信息
    pub shapes: Vec<ShapeOverlay>, //叠加形状信息
}

//叠加文字信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextOverlay {
    pub text: String,   //文字内容
    pub x: i32,         //文字位置x
    pub y: i32,         //文字位置y
    pub font_size: i32, //文字大小
    pub color: String,  //文字颜色
}

//叠加形状信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShapeOverlay {
    pub shape_type: String, // "rectangle", "circle", "polygon"
    pub points: Vec<Point>, //形状点
    pub color: String,      //形状颜色
    pub filled: bool,       //是否填充
}

//叠加点信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Point {
    pub x: i32, //点x
    pub y: i32, //点y
}

// 控制消息定义
#[derive(Debug, Serialize, Deserialize)]
pub enum ControlMessage {
    UpdateOverlay(OverlayInfo),
    StopStream,
    UpdateViewerCount(i32),
}

//webrtc流状态实现
impl WebrtcStreamState {
    pub fn new() -> Self {
        Self {
            active_streams: Arc::new(RwLock::new(HashMap::new())),
            stream_channels: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

impl SharedStreamInfo {
    pub fn new(
        camera_id: String,
        rtsp_url: String,
        video_track: Arc<TrackLocalStaticSample>,
        overlay_info: Option<OverlayInfo>,
    ) -> Self {
        Self {
            camera_id,
            rtsp_url,
            video_track,
            viewer_count: Arc::new(AtomicI32::new(1)),
            last_activity: Arc::new(RwLock::new(Instant::now())),
            overlay_info: Arc::new(RwLock::new(overlay_info)),
            start_time: SystemTime::now(),
            peer_connections: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

// impl fmt::Debug for WebRTCConnection {
//     fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
//         f.debug_struct("WebRTCConnection")
//             .field("peer_connection", &self.peer_connection)
//             .field("data_channel", &self.data_channel.as_ref().map(|_| "RTCDataChannel"))
//             .field("stream_info", &self.stream_info)
//             .field("last_activity", &self.last_activity)
//             .finish()
//     }
// }

// //rtsp码流转webrtc流的模型信息
// #[derive(Debug, Clone, Serialize, Deserialize)]
// pub struct WebrtcStreamInfo {
//     pub stream_id: String,                 //webrtc流的id
//     pub rtsp_url: String,                  //rtsp码流地址
//     pub camera_id: String,                 //摄像头id
//     pub viewer_count: i32,                 //观看人数
//     pub overlay_info: Option<OverlayInfo>, //叠加文字、形状信息
//     pub start_time: SystemTime,            //任务开始时间
// }


// //webrtc流状态
// #[derive(Debug, Clone)]
// pub struct WebrtcStreamState {
//     pub active_streams: Arc<RwLock<HashMap<String, WebrtcStreamInfo>>>, //活跃的webrtc流
//     pub stream_channels: Arc<RwLock<HashMap<String, broadcast::Sender<Vec<u8>>>>>, //webrtc流通道
// }


// // WebRTC连接信息
// #[derive(Clone)]
// pub struct WebRTCConnection {
//     pub peer_connection: Arc<RTCPeerConnection>,
//     pub data_channel: Option<Arc<RTCDataChannel>>,  //这个不支持Debug triat
//     pub stream_info: WebrtcStreamInfo,
//     pub last_activity: std::time::Instant,
// }
