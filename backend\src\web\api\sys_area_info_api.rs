//web/api/sys_area_info_api.rs
use axum::{
    extract::State,
    routing::{get, post},
    Json, Router,
};
use std::sync::Arc;
use axum::routing::delete;
use axum::extract::Path;
use axum::extract::Query;
use crate::web::model::common_model::ApiResponse;
use crate::web::model::sys_area_info::{AreaUpdateRequest, SysAreaInfo, LazyLoadQuery};
use crate::web::model::appstate::AppState;

//注册行政区划管理API路由
pub fn register_sys_area_info_api() -> Router<Arc<AppState>> {
    Router::new().nest("/sys_area_info", sys_area_info_routes())
}

fn sys_area_info_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/import", post(import_areas))
        .route("/list", get(get_areas))
        .route("/create", post(create_area))
        .route("/update", post(update_area))
        .route("/delete/:code", delete(delete_area))
        .route("/detail/:code", get(get_area_by_code))
        .route("/lazy_tree_node", get(get_lazy_tree_node))
}

// API路由处理函数
pub async fn import_areas(State(state): State<Arc<AppState>>) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .sys_area_info_service
        .import_json_data("./doc/sql/area_code_2024.json")
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("导入失败: {:#}", e))),
    }
}

pub async fn get_areas(State(state): State<Arc<AppState>>) -> Json<ApiResponse<Vec<SysAreaInfo>>> {
    match state
        .service_manager
        .sys_area_info_service
        .get_all_areas()
        .await
    {
        Ok(areas) => Json(ApiResponse::success(Some(areas))),
        Err(e) => Json(ApiResponse::error(format!("获取失败: {:#}", e))),
    }
}

pub async fn update_area(
    State(state): State<Arc<AppState>>,
    Json(req): Json<AreaUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .sys_area_info_service
        .update_area_status(&req.code, &req.name, req.is_active)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("更新失败: {:#}", e))),
    }
}

// 新增处理函数
pub async fn create_area(
    State(state): State<Arc<AppState>>,
    Json(area): Json<SysAreaInfo>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .sys_area_info_service
        .create_area(area)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("创建失败: {:#}", e))),
    }
}

pub async fn delete_area(
    State(state): State<Arc<AppState>>,
    Path(code): Path<String>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .sys_area_info_service
        .delete_area(&code)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除失败: {:#}", e))),
    }
}

pub async fn get_area_by_code(
    State(state): State<Arc<AppState>>,
    Path(code): Path<String>,
) -> Json<ApiResponse<Vec<SysAreaInfo>>> {
    match state
        .service_manager
        .sys_area_info_service
        .get_area_by_code(&code)
        .await
    {
        Ok(area) => Json(ApiResponse::success(Some(area))),
        Err(e) => Json(ApiResponse::error(format!("获取失败: {:#}", e))),
    }
}


async fn get_lazy_tree_node(
    State(state): State<Arc<AppState>>,
    Query(query): Query<LazyLoadQuery>,
) -> Json<ApiResponse<Vec<SysAreaInfo>>> {
    match state
        .service_manager
        .sys_area_info_service
        .get_lazy_tree_node(query.parent_code)
        .await
    {
        Ok(areas) => Json(ApiResponse::success(Some(areas))),
        Err(e) => Json(ApiResponse::error(format!("获取区划数据失败: {:#}", e))),
    }
}