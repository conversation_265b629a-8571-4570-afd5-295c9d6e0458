use serde::{Serialize, Deserialize};
#[derive(Debug, Serialize, Deserialize)]
pub struct StatisticResponse {
    pub camera_count: i64,
    pub room_count: i64,
    pub hourly_alerts: i64,
    pub daily_alerts: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImageAlarmResponse {
    pub id: String,
    pub image: String, // 图片URL
    pub time: String,  // 报警时间
    pub description: String // 报警描述
}

