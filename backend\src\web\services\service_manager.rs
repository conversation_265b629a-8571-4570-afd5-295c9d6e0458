//src/web/services/service_manager.rs
use std::sync::Arc;

use crate::web::services::sys_area_info_service::SysAreaInfoService;
use crate::database::database::DatabaseConfig;
use crate::web::services::sys_user_service::SysUserService;
use crate::model::JWT_SECRET;
use crate::web::services::office_organization_service::OfficeOrganizationService;
use crate::web::services::room_purpose_type_service::RoomPurposeTypeService;
use crate::web::services::algorithm_dictionary_service::AlgorithmDictionaryService;
use crate::web::services::sys_permission_service::SysPermissionService;
use crate::web::services::sys_role_service::SysRoleService;
use crate::web::services::building_service::BuildingService;
use crate::web::services::location_service::LocationService;
use crate::web::services::camera_service::CameraService;
use crate::webrtc::service::webrtc_stream_service::WebrtcStreamService;
use crate::webrtc::model::webrtc_stream::WebrtcStreamState;
use crate::webrtc::service::webrtc_overlay_service::WebrtcOverlayService;
use crate::web::services::alarm_record_service::AlarmRecordService;
use crate::web::services::dashboard_services::DashboardService;
#[derive(Clone, Debug)]
pub struct ServiceManager {
    pub sys_area_info_service: Arc<SysAreaInfoService>,
    pub sys_user_service: Arc<SysUserService>,
    pub office_organization_service: Arc<OfficeOrganizationService>,
    pub room_purpose_type_service: Arc<RoomPurposeTypeService>,
    pub algorithm_dictionary_service: Arc<AlgorithmDictionaryService>,
    pub sys_permission_service: Arc<SysPermissionService>,
    pub sys_role_service: Arc<SysRoleService>,
    pub building_service: Arc<BuildingService>,
    pub location_service: Arc<LocationService>,
    pub camera_service: Arc<CameraService>,
    pub webrtc_stream_service: Arc<WebrtcStreamService>,
    pub alarm_record_service: Arc<AlarmRecordService>,
    pub dashboard_service: Arc<DashboardService>,
}

impl ServiceManager {
    pub fn new(database: &Arc<DatabaseConfig>) -> Self {

        let stream_state = Arc::new(WebrtcStreamState::new());
        let overlay_service = Arc::new(WebrtcOverlayService::new());
        let stream_service = Arc::new(WebrtcStreamService::new(Arc::clone(&stream_state), Arc::clone(&overlay_service)));

        Self {
            sys_area_info_service: Arc::new(SysAreaInfoService::new(Arc::clone(&database.get_pool()))),
            sys_user_service: Arc::new(SysUserService::new(
                Arc::clone(&database.get_pool()), 
                String::from_utf8(JWT_SECRET.to_vec()).unwrap()
            )),
            office_organization_service: Arc::new(OfficeOrganizationService::new(Arc::clone(&database.get_pool()))),
            room_purpose_type_service: Arc::new(RoomPurposeTypeService::new(Arc::clone(&database.get_pool()))),
            algorithm_dictionary_service: Arc::new(AlgorithmDictionaryService::new(Arc::clone(&database.get_pool()))),
            sys_permission_service: Arc::new(SysPermissionService::new(Arc::clone(&database.get_pool()))),
            sys_role_service: Arc::new(SysRoleService::new(Arc::clone(&database.get_pool()))),
            building_service: Arc::new(BuildingService::new(Arc::clone(&database.get_pool()))),
            location_service: Arc::new(LocationService::new(Arc::clone(&database.get_pool()))),
            camera_service: Arc::new(CameraService::new(Arc::clone(&database.get_pool()))),
            webrtc_stream_service: Arc::new(WebrtcStreamService::new(Arc::clone(&stream_state), Arc::clone(&overlay_service))),
            alarm_record_service: Arc::new(AlarmRecordService::new(Arc::clone(&database.get_pool()))),
            dashboard_service: Arc::new(DashboardService::new(Arc::clone(&database.get_pool()))),
        }

    }
}
