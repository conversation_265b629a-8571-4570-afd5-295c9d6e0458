<!-- src/views/config/CameraManagement.vue -->
<template>
  <div class="flex h-full">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 p-4 border-r overflow-hidden flex flex-col">
      <div class="mb-4">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索节点"
          clearable
          prefix-icon="Search"
        />
      </div>
      <div class="flex-1 overflow-auto">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="flex items-center">
              <el-icon class="mr-1">
                <Location v-if="data.type === 'area'" />
                <OfficeBuilding v-else-if="data.type === 'org'" />
                <House v-else-if="data.type === 'building'" />
                <Box v-else-if="data.type === 'location'" />
              </el-icon>
              <span>{{ data.name }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 p-4 overflow-hidden flex flex-col">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleAdd" :disabled="!isAddEnable">
            新增摄像头
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
        <div class="flex space-x-2">
          <el-select
            v-model="queryParams.status"
            placeholder="摄像头状态"
            clearable
          >
            <el-option label="正常" value="active" />
            <el-option label="离线" value="inactive" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
          <el-input
            v-model="queryParams.search_text"
            placeholder="搜索摄像头名称/描述/IP/RTSP"
            class="w-80"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>

      <!-- 当前位置显示 -->
      <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        border
        stripe
        class="flex-1"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="camera_name"
          label="摄像头名称"
          min-width="120"
        />
        <el-table-column
          prop="camera_description"
          label="描述"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="camera_ip_address" label="IP地址" width="140" />
        <el-table-column prop="camera_username" label="用户名" width="100" />
        <el-table-column
          prop="camera_rtsp_stream_url"
          label="RTSP地址"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
              <el-button
                size="small"
                type="success"
                @click="handlePreview(row)"
              >
                预览
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="摄像头名称" prop="camera_name">
          <el-input v-model="formData.camera_name" />
        </el-form-item>
        <el-form-item label="描述" prop="camera_description">
          <el-input
            v-model="formData.camera_description"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        <el-form-item label="IP地址" prop="camera_ip_address">
          <el-input v-model="formData.camera_ip_address" />
        </el-form-item>
        <el-form-item label="用户名" prop="camera_username">
          <el-input v-model="formData.camera_username" />
        </el-form-item>
        <el-form-item label="密码" prop="camera_password">
          <el-input
            v-model="formData.camera_password"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="RTSP地址" prop="camera_rtsp_stream_url">
          <el-input v-model="formData.camera_rtsp_stream_url" />
        </el-form-item>
        <el-form-item label="所属位置">
          <el-input
            v-model="formData.location_name"
            disabled
            placeholder="请在左侧树中选择位置"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status">
            <el-option label="正常" value="active" />
            <el-option label="离线" value="inactive" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用" v-if="formData.camera_id">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      title="摄像头预览"
      v-model="previewVisible"
      width="800px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="aspect-video">
        <RtspPlayer
          v-if="selectedCamera"
          ref="playerRef"
          :camera-id="selectedCamera.camera_id"
          :drawing-enabled="isDrawingEnabled"
          @error="handlePlayerError"
          @shape-added="handleShapeAdded"
          @shapes-cleared="handleShapesCleared"
        />
      </div>
      <template #footer>
        <el-button @click="toggleDrawing">
          {{ isDrawingEnabled ? "关闭绘制" : "开启绘制" }}
        </el-button>
        <el-button @click="previewVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { format } from "date-fns";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const currentNode = ref(null);
const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

// 表格相关
const loading = ref(false);
const tableRef = ref(null);
const tableData = ref([]);
const selectedRows = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const isAddEnable = ref(false);

// 查询参数
const queryParams = ref({
  location_id: "",
  status: "",
  search_text: "",
});

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增摄像头");
const formRef = ref(null);
const formData = ref({
  camera_id: "",
  camera_name: "",
  camera_description: "",
  camera_ip_address: "",
  camera_username: "",
  camera_password: "",
  camera_rtsp_stream_url: "",
  location_id: "",
  location_name: "",
  status: "active",
  is_active: true,
});

// 预览相关
const previewVisible = ref(false);
const selectedCamera = ref(null);

const formRules = {
  camera_name: [
    { required: true, message: "请输入摄像头名称", trigger: "blur" },
    { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
  ],
  camera_ip_address: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern: /^(\d{1,3}\.){3}\d{1,3}$/,
      message: "请输入正确的IP地址格式",
      trigger: "blur",
    },
  ],
  camera_username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
  ],
  camera_password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  camera_rtsp_stream_url: [
    { required: true, message: "请输入RTSP地址", trigger: "blur" },
    {
      pattern: /^rtsp:\/\//,
      message: "RTSP地址必须以rtsp://开头",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 修改 loadNode 函数以支持多级加载
const loadNode = async (node, resolve) => {
  try {
    if (node.level === 0) {
      // 加载省级行政区划
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map((item) => ({
        ...item,
        id: item.code,
        type: "area",
        name: item.name,
        leaf: false,
      }));
      resolve(areas);
    } else {
      const { data } = node;
      let children = [];

      switch (data.type) {
        case "area":
          // 加载区划下的子区划和机构
          const [areaRes, orgRes] = await Promise.all([
            axios.get("/sys_area_info/lazy_tree_node", {
              params: { parent_code: data.code },
            }),
            axios.get(`/office_organization/list/${data.code}`),
          ]);

          // 处理子区划
          const areas = areaRes.data.data.map((item) => ({
            ...item,
            id: item.code,
            type: "area",
            name: item.name,
            leaf: item.level >= 4,
          }));

          // 处理机构
          const orgs = orgRes.data.data.map((item) => ({
            id: item.office_id,
            type: "org",
            name: item.office_name,
            leaf: false,
            office_id: item.office_id,
            office_name: item.office_name,
            area_code: data.code,
          }));

          children = [...areas, ...orgs];
          break;

        case "org":
          // 加载机构下的楼宇
          const buildingRes = await axios.get("/building/list", {
            params: {
              office_id: data.office_id,
              page_size: 1000,
            },
          });

          children = buildingRes.data.data.items.map((item) => ({
            id: item.building_id,
            type: "building",
            name: item.building_name,
            leaf: false,
            building_id: item.building_id,
            building_name: item.building_name,
            office_id: data.office_id,
          }));
          break;

        case "building":
          // 加载楼宇下的位置
          const locationRes = await axios.get("/location/list", {
            params: {
              building_id: data.building_id,
              page_size: 1000,
            },
          });

          children = locationRes.data.data.items.map((item) => ({
            id: item.location_id,
            type: "location",
            name: item.location_name,
            leaf: true,
            location_id: item.location_id,
            location_name: item.location_name,
            building_id: data.building_id,
          }));
          break;
      }

      resolve(children);
    }
  } catch (error) {
    console.error("加载树节点失败:", error);
    ElMessage.error("加载数据失败");
    resolve([]);
  }
};

// 计算属性
const currentPath = computed(() => {
  if (!currentNode.value) return "全部";
  return getNodePath(currentNode.value);
});

// 树节点过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 获取节点路径
const getNodePath = (node) => {
  const path = [];
  let current = node;
  while (current.parent && current.parent.level !== 0) {
    path.unshift(current.label);
    current = current.parent;
  }
  path.unshift(current.label);
  return path.join(" / ");
};

// 节点点击处理
const handleNodeClick = (data, node) => {
  currentNode.value = node;

  // 只有在选中位置节点时才允许操作摄像头信息
  if (data.type === "location") {
    queryParams.value.location_id = data.location_id;
    formData.value.location_id = data.location_id;
    formData.value.location_name = data.location_name;
    isAddEnable.value = true;
    loadTableData();
  } else {
    queryParams.value.location_id = "";
    formData.value.location_id = "";
    formData.value.location_name = "";
    isAddEnable.value = false;
    tableData.value = [];
  }
};

// 加载表格数据
const loadTableData = async () => {
  if (!queryParams.value.location_id) return;

  loading.value = true;
  try {
    const res = await axios.post("/camera/page", {
      page: currentPage.value,
      page_size: pageSize.value,
      location_id: queryParams.value.location_id,
      status: queryParams.value.status,
      search_text: queryParams.value.search_text,
    });

    if (res.data.code === 200) {
      tableData.value = res.data.data.items;
      total.value = res.data.data.total;
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    ElMessage.error("加载摄像头数据失败");
  } finally {
    loading.value = false;
  }
};

// 表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  queryParams.value.status = "";
  queryParams.value.search_text = "";
  currentPage.value = 1;
  loadTableData();
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadTableData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadTableData();
};

// 新增摄像头
const handleAdd = () => {
  if (!formData.value.location_id) {
    ElMessage.warning("请先在左侧选择一个位置");
    return;
  }
  dialogTitle.value = "新增摄像头";
  formData.value = {
    camera_id: "",
    camera_name: "",
    camera_description: "",
    camera_ip_address: "",
    camera_username: "",
    camera_password: "",
    camera_rtsp_stream_url: "",
    location_id: formData.value.location_id,
    location_name: formData.value.location_name,
    status: "active",
    is_active: true,
  };
  dialogVisible.value = true;
};

// 编辑摄像头
const handleEdit = (row) => {
  dialogTitle.value = "编辑摄像头";
  formData.value = {
    ...row,
    location_name: currentNode.value?.data?.name || "",
  };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (formData.value.camera_id) {
      // 编辑
      const res = await axios.post("/camera/update", {
        camera_id: formData.value.camera_id,
        camera_name: formData.value.camera_name,
        camera_description: formData.value.camera_description,
        camera_ip_address: formData.value.camera_ip_address,
        camera_username: formData.value.camera_username,
        camera_password: formData.value.camera_password,
        camera_rtsp_stream_url: formData.value.camera_rtsp_stream_url,
        location_id: formData.value.location_id,
        status: formData.value.status,
        is_active: formData.value.is_active,
      });

      if (res.data.code === 200) {
        ElMessage.success("更新成功");
      } else {
        ElMessage.error(res.data.message);
        return;
      }
    } else {
      // 新增
      const res = await axios.post("/camera/create", {
        camera_name: formData.value.camera_name,
        camera_description: formData.value.camera_description,
        camera_ip_address: formData.value.camera_ip_address,
        camera_username: formData.value.camera_username,
        camera_password: formData.value.camera_password,
        camera_rtsp_stream_url: formData.value.camera_rtsp_stream_url,
        location_id: formData.value.location_id,
        status: formData.value.status,
      });

      if (res.data.code === 200) {
        ElMessage.success("创建成功");
      } else {
        ElMessage.error(res.data.message);
        return;
      }
    }

    dialogVisible.value = false;
    loadTableData();
  } catch (error) {
    console.error(error);
    ElMessage.error("表单验证失败");
  }
};

// 删除摄像头
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      "确定要删除该摄像头吗？此操作不可恢复！",
      "警告",
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    );

    const res = await axios.delete(`/camera/delete/${row.camera_id}`);
    if (res.data.code === 200) {
      ElMessage.success("删除成功");
      loadTableData();
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个摄像头吗？此操作不可恢复！`,
      "警告",
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    );

    const promises = selectedRows.value.map((row) =>
      axios.delete(`/camera/delete/${row.camera_id}`)
    );
    await Promise.all(promises);

    ElMessage.success("批量删除成功");
    loadTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
    }
  }
};

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const res = await axios.post("/camera/update", {
      camera_id: row.camera_id,
      is_active: row.is_active,
    });

    if (res.data.code === 200) {
      ElMessage.success("状态更新成功");
    } else {
      row.is_active = !row.is_active;
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    row.is_active = !row.is_active;
    ElMessage.error("状态更新失败");
  }
};

// 预览摄像头
const handlePreview = (row) => {
  selectedCamera.value = row;
  previewVisible.value = true;
};

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    active: "正常",
    inactive: "离线",
    maintenance: "维护中",
  };
  return statusMap[status] || status;
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    active: "success",
    inactive: "danger",
    maintenance: "warning",
  };
  return typeMap[status] || "info";
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return "-";
  return format(new Date(date), "yyyy-MM-dd HH:mm:ss");
};

// 监听器
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val);
});

// 生命周期钩子
onMounted(() => {
  // 可以在这里添加初始化逻辑
});


// 预览相关
const playerRef = ref(null)
const isDrawingEnabled = ref(false)

// 预览相关方法
const toggleDrawing = () => {
  isDrawingEnabled.value = !isDrawingEnabled.value
}

const handlePlayerError = (error) => {
  ElMessage.error(`视频播放错误: ${error.message}`)
}

const handleShapeAdded = (shape) => {
  console.log('新增选区:', shape)
  // 这里可以处理选区数据，例如保存到后端
}

const handleShapesCleared = () => {
  console.log('清除所有选区')
  // 这里可以处理清除选区的后续操作
}

</script>

<style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

.el-dialog {
  max-width: 90%;
}

:deep(.el-table) {
  height: 100%;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 表格内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
}

/* 按钮组样式 */
:deep(.el-button-group) {
  display: flex;
  gap: 4px;
}

/* 分页器样式 */
:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 16px 0;
}

/* 搜索框样式 */
.search-input {
  width: 240px;
}

/* 表单项样式 */
:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

/* 对话框表单样式 */
:deep(.el-dialog__body) {
  padding: 20px 40px;
}

/* 开关按钮样式 */
:deep(.el-switch) {
  margin: 0 8px;
}

/* 滚动条样式 */
.el-tree::-webkit-scrollbar,
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

.el-tree::-webkit-scrollbar-thumb,
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #dcdfe6;
  border-radius: 3px;
}

.el-tree::-webkit-scrollbar-track,
:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f7fa;
}
</style>
