// src/web/middleware/jwt_auth.rs
use crate::model::JWT_SECRET;
use crate::web::model::sys_user::Claims;
use axum::http::Method;
use axum::{
    body::Body as AxumBody, // 导入 AxumBody
    http::{Request, StatusCode},
    middleware::Next,
    response::Response,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use log::info;
use once_cell::sync::Lazy;
use regex::Regex;
use serde::Deserialize;

// 使用正则表达式定义白名单
static WHITELIST_PATTERNS: Lazy<Vec<Regex>> = Lazy::new(|| {
    vec![
        Regex::new(r"^/api/v1/auth/login").unwrap(), // 所有认证相关的路径
        Regex::new(r"^/health.*").unwrap(),          // 所有健康检查相关的路径
        Regex::new(r"^/public/.*").unwrap(),         // 所有公开资源
    ]
});

// 定义不需要认证的路径
const WHITELIST_PATHS: [&str; 3] = ["/api/v1/auth/login", "/health", "/health/check"];

#[derive(Deserialize, Default)]
struct TokenQuery {
    x_shinow_token: Option<String>,
}

pub async fn auth_middleware(
    mut request: Request<AxumBody>,
    next: Next,
) -> Result<Response, StatusCode> {
    let path = request.uri().path();
    info!("path: {}", path);

    if is_path_whitelisted(path) {
        return Ok(next.run(request).await);
    }

    //如果是跨域的OPTIONS请求，则直接返回空的200响应
    if request.method() == Method::OPTIONS {
        info!("OPTIONS request");
        return Ok(next.run(request).await);
    }

    //info!("request.headers(): {:?}", request.headers());

    // 获取认证token（支持多种方式）
    let auth_header = {
        // 1. 首先尝试从header中获取
        let token_from_header = request
            .headers()
            .get("x-shinow-token")
            .and_then(|header| header.to_str().ok())
            .and_then(|header| {
                if header.starts_with("Bearer ") {
                    Some(header[7..].to_string())
                } else {
                    None
                }
            });

        // 2. 如果header中没有，尝试从URL查询参数中获取
        if token_from_header.is_none() {
            // 解析URL查询参数
            let uri = request.uri();
            if let Some(query) = uri.query() {
                let params: TokenQuery = serde_qs::from_str(query).unwrap_or_default();
                params.x_shinow_token
            } else {
                None
            }
        } else {
            token_from_header
        }
    }
    .ok_or(StatusCode::UNAUTHORIZED)?;

    //info!("auth_header: {}", auth_header);

    // 从环境变量获取 JWT secret
    let jwt_secret = JWT_SECRET;

    // 验证 token
    let token_data = decode::<Claims>(
        &auth_header,
        &DecodingKey::from_secret(jwt_secret),
        &Validation::default(),
    )
    .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // 将解析出的 claims 存入请求扩展中
    request.extensions_mut().insert(token_data.claims);

    // 继续处理请求
    Ok(next.run(request).await)
}

fn is_path_whitelisted(path: &str) -> bool {
    // 精确匹配,判断path结尾是否在WHITELIST_PATHS中
    for whitelist_path in WHITELIST_PATHS {
        if path.ends_with(whitelist_path) {
            return true;
        }
    }

    // 正则匹配
    WHITELIST_PATTERNS
        .iter()
        .any(|pattern| pattern.is_match(path))
}
