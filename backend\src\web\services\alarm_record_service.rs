// src/web/service/alarm_record_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::alarm_record::{AlarmRecord, AlarmRecordConfirmRequest, AlarmRecordQueryRequest,AlarmRecordDetailResponse, PageResponse};

#[derive(<PERSON><PERSON>,Debug)]
pub struct AlarmRecordService {
    pool: Arc<Pool<Postgres>>,
}

impl AlarmRecordService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }


    pub async fn get_page(&self, req: AlarmRecordQueryRequest) -> Result<PageResponse<AlarmRecordDetailResponse>> {
        let page = req.page.unwrap_or(1);
        let page_size = req.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;
    
        let mut query = String::from(
            "SELECT 
                sa.name as area_name,
                oo.office_name,
                b.building_name,
                l.location_name,
                rpt.room_purpose_type_name,
                l.location_name as room_name,
                c.camera_name,
                ad.algorithm_name,
                a.*
             FROM alarm_record a
             LEFT JOIN camera c ON a.camera_id = c.camera_id
             LEFT JOIN algorithm_dictionary ad ON a.algorithm_id = ad.algorithm_id 
             LEFT JOIN location l ON c.location_id = l.location_id
             LEFT JOIN room_purpose_type rpt ON l.purpose_type_id = rpt.room_purpose_type_id
             LEFT JOIN building b ON l.building_id = b.building_id
             LEFT JOIN office_organization oo ON b.office_id = oo.office_id
             LEFT JOIN sys_area_info sa ON oo.area_code = sa.code
             WHERE a.alarm_time BETWEEN $1 AND $2"
        );
    
        let mut param_index = 3;
        let mut params: Vec<String> = vec![];
    
        if let Some(camera_name) = &req.camera_name {
            if !camera_name.is_empty() {
                query.push_str(&format!(" AND c.camera_name ILIKE ${}", param_index));
                params.push(format!("%{}%", camera_name));
                param_index += 1;
            }
        }
    
        if let Some(algorithm_id) = &req.algorithm_id {
            if !algorithm_id.is_empty() {
                query.push_str(&format!(" AND a.algorithm_id = ${}", param_index));
                params.push(algorithm_id.to_string());
                param_index += 1;
            }
        }
    
        if let Some(is_confirmed) = req.is_confirmed {
            query.push_str(&format!(" AND a.is_confirmed = ${}::boolean", param_index));
            params.push(is_confirmed.to_string());
            param_index += 1;
        }
    
        // 获取总数
        let count_query = format!("SELECT COUNT(*) FROM ({}) t", query);
        let mut count_query = sqlx::query_scalar(&count_query);
        count_query = count_query.bind(req.start_time).bind(req.end_time);
        for param in &params {
            count_query = count_query.bind(param);
        }
        let total = count_query
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;
    
        // 分页查询
        query.push_str(" ORDER BY a.alarm_time DESC LIMIT $");
        query.push_str(&param_index.to_string());
        query.push_str(" OFFSET $");
        query.push_str(&(param_index + 1).to_string());
    
        let mut query = sqlx::query_as::<_, AlarmRecordDetailResponse>(&query);
        query = query.bind(req.start_time).bind(req.end_time);
        for param in &params {
            query = query.bind(param);
        }
        query = query.bind(page_size as i64).bind(offset as i64);
    
        let items = query
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to get alarm records")?;
    
        Ok(PageResponse { total, items })
    }
    

    pub async fn confirm(&self, req: AlarmRecordConfirmRequest) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE alarm_record 
            SET is_confirmed = true,
                confirm_user_id = $1,
                confirm_time = $2,
                confirm_note = $3,
                updated_at = $2
            WHERE alarm_id = $4
            "#,
            req.confirm_user_id,
            Utc::now(),
            req.confirm_note,
            req.alarm_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to confirm alarm record")?;
        Ok(())
    }

    // 创建报警记录
    pub async fn create(&self, camera_id: String, algorithm_id: String, image_url: Option<String>, video_url: Option<String>) -> Result<()> {
        let alarm_id = Uuid::new_v4().simple().to_string();
        
        sqlx::query!(
            r#"
            INSERT INTO alarm_record (
                alarm_id, camera_id, algorithm_id, alarm_time,
                alarm_image_url, alarm_video_url, is_pushed, is_confirmed
            ) VALUES ($1, $2, $3, $4, $5, $6, false, false)
            "#,
            alarm_id,
            camera_id,
            algorithm_id,
            Utc::now(),
            image_url,
            video_url
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create alarm record")?;

        Ok(())
    }

    // 更新推送状态
    pub async fn update_push_status(&self, alarm_id: &str, is_pushed: bool) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE alarm_record 
            SET is_pushed = $1, 
                push_time = $2,
                updated_at = $2
            WHERE alarm_id = $3
            "#,
            is_pushed,
            Utc::now(),
            alarm_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update push status")?;

        Ok(())
    }

    // 删除报警记录
    pub async fn delete(&self, alarm_id: &str) -> Result<()> {
        sqlx::query!(
            "DELETE FROM alarm_record WHERE alarm_id = $1",
            alarm_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to delete alarm record")?;

        Ok(())
    }

    // 获取单条报警记录详情
    pub async fn get_by_id(&self, alarm_id: &str) -> Result<AlarmRecord> {
        let record = sqlx::query_as!(
            AlarmRecord,
            r#"
            SELECT * FROM alarm_record WHERE alarm_id = $1
            "#,
            alarm_id
        )
        .fetch_one(self.pool.as_ref())
        .await
        .context("Failed to get alarm record")?;

        Ok(record)
    }

    // 批量删除报警记录
    pub async fn batch_delete(&self, alarm_ids: Vec<String>) -> Result<()> {
        sqlx::query!(
            "DELETE FROM alarm_record WHERE alarm_id = ANY($1)",
            &alarm_ids
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to batch delete alarm records")?;

        Ok(())
    }


}
