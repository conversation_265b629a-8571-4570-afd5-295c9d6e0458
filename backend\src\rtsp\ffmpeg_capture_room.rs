//rtsp/ffmpeg_caper.rs
// 用于解码RTSP流并截图
use anyhow::{Erro<PERSON>, Result};
use ffmpeg_next as ffmpeg;
use futures::future::join_all;
use log::*;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use std::sync::Arc;
use crate::model::rtsp_model::{CameraFrame, CameraSource, RoomFrame, RtspSource};
use crate::model::frame_queue::FrameQueue;
use crate::onnx::yolo_detector::ObjectDetector;
use crate::rtsp::ffmpeg_capture_trait::FrameCaptureTriat;
use bytes::Bytes;

pub struct FrameCaptureRoom {
    worker_id: usize,        // 工作线程ID
    room_source: RtspSource, // RTSP码流源信息
}

impl FrameCaptureRoom {
    pub fn new(worker_id: usize, room_source: RtspSource) -> Result<Self> {
        Ok(FrameCaptureRoom {
            worker_id,
            room_source,
        })
    }

    /// 解码单个摄像头帧
    ///
    /// # 参数
    /// - `worker_id`: 工作线程ID
    /// - `camera`: 摄像头源信息
    /// - `timeout`: 工作超时时间
    /// - `start_time`: 开始时间
    ///
    /// # 返回
    /// - `Result<Option<CameraFrame>>`: 解码后的摄像头帧，如果失败则返回None
    async fn decode_camera_frame(
        worker_id: usize,
        camera: CameraSource,
        timeout: Duration,
        start_time: Instant,
    ) -> Result<Option<CameraFrame>> {
        if start_time.elapsed() > timeout {
            return Err(anyhow::anyhow!(
                "Operation timed out before starting camera decode"
            ));
        }

        let frame_result = tokio::task::spawn_blocking(move || -> Result<Option<CameraFrame>> {
            debug!(
                "Worker {} starting to decode camera: {} ({})",
                worker_id, camera.camera_name, camera.camera_id
            );

            let start_time = Instant::now(); //记录开始时间

            // FFmpeg配置
            let mut options = ffmpeg::Dictionary::new();
            options.set("rtsp_transport", "tcp");            //ffmpeg 连接rtsp时的协议格式
            options.set("reconnect", "1");                   // 断开重连
            options.set("reconnect_at_eof", "1");            // 到达文件末尾时重连
            options.set("reconnect_streamed", "1");          // 流式重连
            options.set("reconnect_on_network_error", "1");  // 网络错误重连
            options.set("stimeout", "5000000");              // 设置超时时间
            options.set("buffer_size", "2048000");           // 设置缓冲区大小
            options.set("max_delay", "5000000");             // 设置最大延迟
            options.set("analyzeduration", "10000000");      // 设置分析时间
            options.set("probesize", "100000000");           // 设置探测大小

            // 打开输入流
            let mut ictx = match ffmpeg::format::input_with_dictionary(&camera.url, options) {
                Ok(ctx) => ctx,
                Err(e) => {
                    warn!(
                        "Worker {} failed to open stream for camera {}: {}",
                        worker_id, camera.camera_id, e
                    );
                    return Ok(None);
                }
            };

            // 找到视频流
            let input = ictx
                .streams()
                .best(ffmpeg::media::Type::Video)
                .ok_or_else(|| Error::msg("No video stream found"))?;

            let video_stream_index = input.index();

            // 创建解码器
            let context_decoder =
                ffmpeg::codec::context::Context::from_parameters(input.parameters())?;
            let mut decoder = context_decoder.decoder().video()?;

            // 检查像素格式
            if decoder.format() == ffmpeg::format::Pixel::None {
                return Err(anyhow::anyhow!("Invalid pixel format"));
            }

            // 验证视频尺寸
            let width = decoder.width();
            let height = decoder.height();
            if width == 0 || height == 0 {
                return Err(anyhow::anyhow!(
                    "Invalid video dimensions: {}x{}",
                    width,
                    height
                ));
            }

            debug!(
                "Worker {} video dimensions: {}x{}",
                worker_id, width, height
            );
            debug!("worker {} 创建解码器成功", worker_id);

            // 创建帧和缩放器
            let mut frame = ffmpeg::frame::Video::empty();
            let mut rgb_frame = ffmpeg::frame::Video::empty();

            let mut scaler = ffmpeg::software::scaling::Context::get(
                decoder.format(),
                decoder.width(),
                decoder.height(),
                ffmpeg::format::Pixel::RGB24,
                decoder.width(),
                decoder.height(),
                ffmpeg::software::scaling::Flags::BILINEAR
                    | ffmpeg::software::scaling::Flags::FULL_CHR_H_INT,
            )?;

            // 记录rtsp连接时间
            let rtsp_connection_time = start_time.elapsed().as_millis() as i64;
            let mut rtsp_capture_frame_time: i64 = 0; // 记录rtsp截取帧时间
            let mut rtsp_decode_frame_time: i64 = 0; // 记录rtsp解码帧时间

            // 处理数据包
            let mut frame_obtained = false;
            'packet_loop: for (stream, packet) in ictx.packets() {
                if stream.index() == video_stream_index && packet.is_key() {
                    if start_time.elapsed() > timeout {
                        return Err(anyhow::anyhow!("Timeout while processing video packets"));
                    }

                    rtsp_capture_frame_time = start_time.elapsed().as_millis() as i64; // 记录rtsp截取帧时间

                    match decoder.send_packet(&packet) {
                        Ok(_) => {
                            while decoder.receive_frame(&mut frame).is_ok() {
                                scaler.run(&frame, &mut rgb_frame)?;
                                frame_obtained = true;
                                rtsp_decode_frame_time = start_time.elapsed().as_millis() as i64; // 记录rtsp解码帧时间
                                break 'packet_loop;
                            }
                        }
                        Err(e) => {
                            warn!(
                                "Worker {} failed to process packet for camera {}: {}",
                                worker_id, camera.camera_id, e
                            );
                            continue;
                        }
                    }
                }
            }

            if !frame_obtained {
                warn!(
                    "Worker {} no valid frame obtained for camera {}",
                    worker_id, camera.camera_id
                );
                return Ok(None);
            }

            debug!(
                "Worker {} successfully decoded frame for camera {}",
                worker_id, camera.camera_id
            );

            Ok(Some(CameraFrame {
                camera_id: camera.camera_id,
                camera_name: camera.camera_name,
                camera_type: camera.camera_type,
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs() as i64,
                width: frame.width() as i32,
                height: frame.height() as i32,
                data: Bytes::from(rgb_frame.data(0).to_vec()),
                debug_rtsp_connection_time: rtsp_connection_time,
                debug_rtsp_capture_frame_time: rtsp_capture_frame_time,
                debug_rtsp_decode_frame_time: rtsp_decode_frame_time,
                debug_rtsp_send_frame_time: 0,
            }))
        })
        .await?;

        frame_result
    }

    pub async fn decode_room_frames(&self) -> Result<Option<RoomFrame>> {
        let start_time = Instant::now();
        let timeout = Duration::from_secs(20); // 总体超时时间
        let camera_timeout = Duration::from_secs(10); // 单个摄像头超时时间

        info!(
            "Worker {} starting to decode frames for room {} ({})",
            self.worker_id, self.room_source.room_name, self.room_source.room_id
        );

        // 创建所有摄像头的解码任务
        let mut frame_futures: Vec<JoinHandle<Result<Option<CameraFrame>>>> = Vec::new();

        for camera in &self.room_source.cameras {
            let camera = camera.clone();
            let worker_id = self.worker_id;

            frame_futures.push(tokio::spawn(async move {
                Self::decode_camera_frame(worker_id, camera, camera_timeout, start_time).await
            }));
        }

        // 设置总体超时
        let timeout_future = tokio::time::sleep(timeout);
        let frames_future = join_all(frame_futures);

        // 使用tokio::select!等待结果或超时
        let results = tokio::select! {
            _ = timeout_future => {
                error!(
                    "Worker {} timeout while decoding room {} frames",
                    self.worker_id, self.room_source.room_id
                );
                return Err(anyhow::anyhow!("Room decode timeout"));
            }
            frames = frames_future => frames,
        };

        // 处理结果
        let mut frames = HashMap::new();
        let mut success_count = 0;
        let mut error_count = 0;

        for (idx, result) in results.into_iter().enumerate() {
            match result {
                Ok(Ok(Some(frame))) => {
                    success_count += 1;
                    frames.insert(frame.camera_id.clone(), frame);
                }
                Ok(Ok(None)) => {
                    error_count += 1;
                    warn!(
                        "Worker {} failed to decode camera {} in room {}",
                        self.worker_id,
                        self.room_source.cameras[idx].camera_id,
                        self.room_source.room_id
                    );
                }
                Ok(Err(e)) => {
                    error_count += 1;
                    error!(
                        "Worker {} error decoding camera {} in room {}: {}",
                        self.worker_id,
                        self.room_source.cameras[idx].camera_id,
                        self.room_source.room_id,
                        e
                    );
                }
                Err(e) => {
                    error_count += 1;
                    error!(
                        "Worker {} task error for camera {} in room {}: {}",
                        self.worker_id,
                        self.room_source.cameras[idx].camera_id,
                        self.room_source.room_id,
                        e
                    );
                }
            }
        }

        // 记录完成状态
        info!(
            "Worker {} completed room {} decode: {} successful, {} failed, took {:?}",
            self.worker_id,
            self.room_source.room_id,
            success_count,
            error_count,
            start_time.elapsed()
        );

        // 根据成功率决定是否返回结果
        let total_cameras = self.room_source.cameras.len();
        let success_rate = success_count as f32 / total_cameras as f32;

        if success_rate >= 0.5 && !frames.is_empty() {
            // 可以调整成功率阈值
            Ok(Some(RoomFrame {
                room_id: self.room_source.room_id.clone(),
                room_name: self.room_source.room_name.clone(),
                frames,
            }))
        } else {
            warn!(
                "Worker {} insufficient successful decodes for room {} ({}/{} cameras)",
                self.worker_id, self.room_source.room_id, success_count, total_cameras
            );
            Ok(None)
        }
    }
}


impl FrameCaptureTriat for FrameCaptureRoom {
    fn new(
        worker_id: usize,
        room_source: RtspSource,
        frame_queue: Option<Arc<FrameQueue>>,
        detector: Option<Arc<dyn ObjectDetector + Send + Sync>>,
    ) -> Result<Self> {
        FrameCaptureRoom::new(worker_id, room_source)
    }

    async fn capture_frames(&self) -> Result<Option<RoomFrame>> {
        self.decode_room_frames().await
    }
}