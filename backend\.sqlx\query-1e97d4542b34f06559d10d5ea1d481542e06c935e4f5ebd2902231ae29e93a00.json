{"db_name": "PostgreSQL", "query": "  \n            SELECT user_id, organization_id,username, user_pass, email, full_name, is_active as \"is_active!\", created_at, updated_at, last_login\n            FROM sys_user   \n            WHERE username = $1 AND is_active = true  \n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "user_pass", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "full_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "last_login", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [false, true, false, false, false, true, true, true, true, true]}, "hash": "1e97d4542b34f06559d10d5ea1d481542e06c935e4f5ebd2902231ae29e93a00"}