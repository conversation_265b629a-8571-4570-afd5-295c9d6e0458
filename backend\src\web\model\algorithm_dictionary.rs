//src/web/model/algorithm_dictionary.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// 数据库模型
#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct AlgorithmDictionary {
    pub algorithm_id: String,
    pub algorithm_name: String,
    pub algorithm_description: Option<String>,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

// 分页响应
#[derive(Debug, Serialize, Deserialize)]
pub struct PageResponse<T> {
    pub records: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
}

// 请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct AlgorithmCreateRequest {
    pub algorithm_name: String,
    pub algorithm_description: Option<String>,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AlgorithmUpdateRequest {
    pub algorithm_id: String,
    pub algorithm_name: String,
    pub algorithm_description: Option<String>,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageRequest {
    pub page: i64,
    pub page_size: i64,
    pub search_text: Option<String>,
}