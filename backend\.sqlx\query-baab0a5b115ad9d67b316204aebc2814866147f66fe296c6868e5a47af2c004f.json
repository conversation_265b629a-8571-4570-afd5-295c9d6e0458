{"db_name": "PostgreSQL", "query": "SELECT code, name, parent_code, level, is_active as \"is_active!\", created_at, updated_at FROM sys_area_info ORDER BY code", "describe": {"columns": [{"ordinal": 0, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "parent_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "level", "type_info": "Int4"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": []}, "nullable": [false, false, true, false, true, true, true]}, "hash": "baab0a5b115ad9d67b316204aebc2814866147f66fe296c6868e5a47af2c004f"}