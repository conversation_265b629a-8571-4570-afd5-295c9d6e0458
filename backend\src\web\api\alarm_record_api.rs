// src/web/api/alarm_record_api.rs
use axum::{
    extract::{Path, State},
    routing::{get, post, delete},
    Json, Router,
};
use std::sync::Arc;

use crate::web::model::{
    alarm_record::{AlarmRecordConfirmRequest, AlarmRecordQueryRequest, AlarmRecordDetailResponse},
    common_model::ApiResponse,
};
use crate::web::model::appstate::AppState;
use crate::web::model::alarm_record::{AlarmRecord, PageResponse};


pub fn register_alarm_record_api() -> Router<Arc<AppState>> {
    Router::new()
        .route("/alarm_record/page", post(get_alarm_record_page))
        .route("/alarm_record/confirm", post(confirm_alarm_record))
        .route("/alarm_record/detail/:id", get(get_alarm_record_detail))
        .route("/alarm_record/delete/:id", delete(delete_alarm_record))
        .route("/alarm_record/batch_delete", post(batch_delete_alarm_records))
}


async fn get_alarm_record_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<AlarmRecordQueryRequest>,
) -> Json<ApiResponse<PageResponse<AlarmRecordDetailResponse>>> {
    match state.service_manager.alarm_record_service.get_page(req).await {
        Ok(page) => Json(ApiResponse::success(Some(page))),
        Err(e) => Json(ApiResponse::error(format!("获取报警记录失败: {:#}", e))),
    }
}

async fn confirm_alarm_record(
    State(state): State<Arc<AppState>>,
    Json(req): Json<AlarmRecordConfirmRequest>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.alarm_record_service.confirm(req).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("确认报警记录失败: {:#}", e))),
    }
}


// 获取报警记录详情
async fn get_alarm_record_detail(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<AlarmRecord>> {
    match state.service_manager.alarm_record_service.get_by_id(&id).await {
        Ok(record) => Json(ApiResponse::success(Some(record))),
        Err(e) => Json(ApiResponse::error(format!("获取报警记录详情失败: {:#}", e))),
    }
}

// 删除单条报警记录
async fn delete_alarm_record(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.alarm_record_service.delete(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除报警记录失败: {:#}", e))),
    }
}

// 批量删除报警记录
async fn batch_delete_alarm_records(
    State(state): State<Arc<AppState>>,
    Json(alarm_ids): Json<Vec<String>>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.alarm_record_service.batch_delete(alarm_ids).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("批量删除报警记录失败: {:#}", e))),
    }
}
