{"db_name": "PostgreSQL", "query": "SELECT code, name, parent_code, level, is_active as \"is_active!\", created_at, updated_at   \n            FROM sys_area_info WHERE parent_code = $1", "describe": {"columns": [{"ordinal": 0, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "parent_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "level", "type_info": "Int4"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, true, false, true, true, true]}, "hash": "7b52416b3af65686faf3c99da3ffa2bd0ea4c82d46d2bbeecd7ccf50672f236e"}