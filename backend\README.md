apt install build-essential
apt install clang clang-devel llvm-devel
windows的话需要自己到https://github.com/llvm/llvm-project/releases/tag/llvmorg-18.1.8 去下载https://github.com/llvm/llvm-project/releases/download/llvmorg-18.1.8/LLVM-18.1.8-win64.exe ，安装完以后配置PATH环境变量将llvm的安装后的路径加进去。

apt install pkg-config libssl-dev

curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh  一路回车安装rust开发环境以后reboot重启

apt install libavcodec-dev libswscale-dev libswresample-dev libavformat-dev libavfilter-dev #安装ffmpeg开发库

到https://github.com/protocolbuffers/protobuf/releases   下载最新版的protoc编译工具，解压缩并配置环境变量，例如
export PROTOC=/data/protoc/bin/protoc
export PATH=/data/protoc/bin:$PATH

到https://github.com/microsoft/onnxruntime/releases 下载适合cpu或者gpu的onnx运行时解压缩并复制到/usr/lib路径内
wget  https://github.com/microsoft/onnxruntime/releases/download/v1.21.0/onnxruntime-linux-x64-1.21.0.tgz 
tar xzf onnxruntime-linux-x64-1.21.0.tgz
sudo cp onnxruntime-linux-x64-1.21.0/lib/libonnxruntime.so* /usr/lib/

安装sqlx-cli工具，命令行窗口执行下面的命令：
cargo install sqlx-cli
