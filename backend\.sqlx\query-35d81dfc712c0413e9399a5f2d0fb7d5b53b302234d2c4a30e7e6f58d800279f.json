{"db_name": "PostgreSQL", "query": "\n            SELECT \n                office_id, office_name, office_address, area_code,\n                is_active as \"is_active!\", created_at, updated_at\n            FROM office_organization\n            WHERE area_code = $1\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "office_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "office_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "office_address", "type_info": "Text"}, {"ordinal": 3, "name": "area_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, true, true, true, true, true]}, "hash": "35d81dfc712c0413e9399a5f2d0fb7d5b53b302234d2c4a30e7e6f58d800279f"}