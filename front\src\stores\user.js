// src/stores/user.js  
import { defineStore } from "pinia";  
import axios from "@/utils/axios";  
import { addDynamicRoutes } from "@/router";  
import { useRouter } from "vue-router";

export const useUserStore = defineStore("user", {  
  state: () => ({  
    token: localStorage.getItem("token") || "",  
    userInfo: JSON.parse(localStorage.getItem("userInfo")) || null,  
    menus: JSON.parse(localStorage.getItem("menus")) || [],  
    loading: false,  
    error: null,  
  }),  

  getters: {  
    userName: (state) =>  
      state.userInfo?.full_name || state.userInfo?.username || "未知用户",  
  },  

  actions: {  
    async login(username, password) {  
      this.loading = true;  
      this.error = null;  
      try {  
        const response = await axios.post("/auth/login", {  
          username,  
          password,  
        });  

        if (response.data.code === 200) {  
          const { token, user, menus } = response.data.data;  
          this.token = token;  
          this.userInfo = user;  
          this.menus = menus;  

          // 保存到本地存储  
          localStorage.setItem("token", token);  
          localStorage.setItem("userInfo", JSON.stringify(user));  
          localStorage.setItem("menus", JSON.stringify(menus));  

          // 添加动态路由  
          await addDynamicRoutes(menus);  

          return true;  
        } else {  
          this.error = response.data.message;  
          return false;  
        }  
      } catch (error) {  
        this.error = error.message || "登录失败";  
        return false;  
      } finally {  
        this.loading = false;  
      }  
    }  
  },  
});  