//model/rtsp_model.rs
use std::fmt::Display;
use std::collections::HashMap;
use bytes::Bytes;


#[derive(Debug, Clone)]
pub struct CameraSource {
    pub camera_id: String,   // 摄像头id
    pub camera_name: String, // 摄像头名称
    pub camera_type: String, // 摄像头类型
    pub url: String,         // 视频流地址
}

#[derive(Debug, <PERSON>lone)]
pub struct RtspSource {
    pub room_id: String,     // 房间id
    pub room_name: String,   // 房间名称
    pub cameras: Vec<CameraSource>, // 该房间的所有摄像头
}

impl Display for RtspSource {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "room_id: {}, room_name:{}, camera_count:{}",
            self.room_id,
            self.room_name,
            self.cameras.len()
        )?;
        for camera in &self.cameras {
            write!(
                f,
                "\n  camera_id:{}, camera_name:{}, camera_type:{}, url: {}",
                camera.camera_id, camera.camera_name, camera.camera_type, camera.url
            )?;
        }
        Ok(())
    }
}

#[derive(Clone)]
pub struct CameraFrame {
    pub camera_id: String,   // 摄像头id
    pub camera_name: String, // 摄像头名称
    pub camera_type: String, // 摄像头类型
    pub timestamp: i64,      // 截图时间
    pub width: i32,          // 视频帧宽度
    pub height: i32,         // 视频帧高度
    //pub data: Arc<Vec<u8>>,       // 视频帧rgb数据
    pub data: Bytes,       // 视频帧rgb数据
    pub debug_rtsp_connection_time: i64, // 调试字段，记录rtsp连接时间
    pub debug_rtsp_capture_frame_time: i64, // 调试字段，记录rtsp截取帧时间
    pub debug_rtsp_decode_frame_time: i64,  // 调试字段，记录rtsp解码帧时间
    pub debug_rtsp_send_frame_time: i64,     // 调试字段，记录rtsp发送帧时间
}

#[derive(Clone)]
pub struct RoomFrame {
    pub room_id: String,     // 房间id
    pub room_name: String,   // 房间名称
    pub frames: HashMap<String, CameraFrame>, // 键是camera_id，值是对应的帧数据
}

impl Default for CameraFrame {
    fn default() -> Self {
        CameraFrame { camera_id: "".to_string(), camera_name: "".to_string(), camera_type: "".to_string(), timestamp: 0, width: 0, height: 0, data: Bytes::new(), debug_rtsp_connection_time: 0, debug_rtsp_capture_frame_time: 0, debug_rtsp_decode_frame_time: 0, debug_rtsp_send_frame_time: 0 }
    }
}

impl Default for RoomFrame {
    fn default() -> Self {
        RoomFrame { room_id: "".to_string(), room_name: "".to_string(), frames: HashMap::new() }
    }
}