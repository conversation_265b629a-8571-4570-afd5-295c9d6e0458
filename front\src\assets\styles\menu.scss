/* src/assets/styles/menu.scss */  
// 可以创建一个全局的菜单样式文件  
.submenu-popper {  
    .el-menu {  
      background-color: #1e293b !important;  
  
      .el-menu-item {  
        background-color: #1e293b !important;  
  
        &:hover {  
          background-color: rgba(64, 158, 255, 0.1) !important;  
        }  
  
        &.is-active {  
          background-color: rgba(64, 158, 255, 0.1) !important;  
        }  
      }  
    }  
  }  