//onnx/ort_yolo_detector.rs
#![allow(clippy::manual_retain)]
use ab_glyph::{FontArc, PxScale};
use base64::{engine::general_purpose, Engine as _};
use image::{imageops::FilterType, DynamicImage, GenericImageView, ImageFormat, Rgba};
use imageproc::drawing::{draw_hollow_rect_mut, draw_text_mut};
use imageproc::rect::Rect;
use ndarray::Array4;
use ndarray::Axis;
use ndarray::{s, Array2, Ix3};
use ort::session::Session;
use ort::execution_providers::{CUDAExecutionProvider, CPUExecutionProvider};
use ort::session::builder::GraphOptimizationLevel;
use ort::value::Value;
use ort::inputs;
use rayon::prelude::*;
use std::env;
use std::io::Cursor;
use std::path::PathBuf;
use std::sync::Arc;
use std::thread;
use std::sync::Mutex;
use tokio::time::Instant;
use anyhow::Result;
use async_trait::async_trait;
use std::path::Path;

use crate::onnx::yolo_detector::{ObjectDetector, ModelConfig, ModelDetectionResult, DetectionBox};
use crate::onnx::yolo_utils::calculate_iou;
use crate::onnx::yolo_utils::YOLOV8_CLASS_LABELS;

const IMAGE_WIDTH: u32 = 640;
const IMAGE_HEIGHT: u32 = 640;
const CONFIDENCE_THRESHOLD: f32 = 0.1;

pub struct OrtYoloDetector {
    session: Arc<Session>,
    config: ModelConfig,
}

#[async_trait]
impl ObjectDetector for OrtYoloDetector {
    async fn init(config: ModelConfig) -> Result<Self> {
        let session = Self::init_yolo(&config).await?;
        Ok(Self { session, config })
    }

    async fn detect_from_memory(&self, image_data: &[u8]) -> Result<ModelDetectionResult> {
        let result = detect_image(image_data, &self.session).await?;
        
        Ok(ModelDetectionResult {
            num_persons: result.num_persons,
            boxes: result.boxes.into_iter()
                .map(|d| DetectionBox {
                    class_name: d.class_name,
                    confidence: d.confidence,
                    bbox: d.bbox,
                })
                .collect(),
            processing_time_ms: result.processing_time_ms,
            annotated_image: None,
        })
    }

    async fn detect_from_image(&self, image: &DynamicImage) -> Result<ModelDetectionResult> {
        let mut buffer = Vec::new();
        image.write_to(&mut Cursor::new(&mut buffer), ImageFormat::Jpeg)?;
        self.detect_from_memory(&buffer).await
    }

    async fn detect_from_file(&self, image_path: &Path) -> Result<ModelDetectionResult> {
        let image_data = tokio::fs::read(image_path).await?;
        self.detect_from_memory(&image_data).await
    }

    fn get_config(&self) -> &ModelConfig {
        &self.config
    }
}

impl OrtYoloDetector {

    pub async fn new(config: ModelConfig) -> Result<Self> {
        let session = Self::init_yolo(&config).await?;
        Ok(Self { session, config })
    }

    async fn init_yolo(config: &ModelConfig) -> Result<Arc<Session>> {

        let device = config.config.rtsp.yolo_device.as_str();
        let providers = if device == "cuda" {
            [CUDAExecutionProvider::default().build()]
        } else {
            [CPUExecutionProvider::default().build()]
        };

        ort::init()
            .with_name("yolo")
            .with_execution_providers(providers)
            .commit()?;

        let yolo_onnx_model_path = &config.config.rtsp.yolo_model_path;
        if !Path::new(yolo_onnx_model_path).exists() {
            return Err(anyhow::anyhow!("ONNX model file not found: {}", yolo_onnx_model_path));
        }

        let num_cores = thread::available_parallelism()
            .map(|p| p.get())
            .unwrap_or(4);

        let session = Session::builder()?
            .with_optimization_level(GraphOptimizationLevel::Level3)?
            .with_intra_threads(num_cores)?
            .with_parallel_execution(false)?
            .with_memory_pattern(false)?
            .commit_from_file(yolo_onnx_model_path)?;

        Ok(Arc::new(session))
    }
}


pub async fn init_yolo() -> Result<Arc<Session>> {
    ort::init()
        .with_name("yolo")
        .with_execution_providers([
            CPUExecutionProvider::default().build(),
            CUDAExecutionProvider::default().build(),
        ])
        .commit()?;
    let yolo_onnx_model_path = get_model_path();
    //判断模型是否存在
    if !yolo_onnx_model_path.exists() {
        log::error!("ONNX model file not found: {:?}", yolo_onnx_model_path);
        return Err(anyhow::anyhow!("ONNX model file not found"));
    }
    log::debug!("Loading ONNX model from: {:?}", yolo_onnx_model_path);
    // 获取系统的逻辑核心数
    let num_cores = thread::available_parallelism()
        .map(|p| p.get())
        .unwrap_or(4);
    log::debug!("Using {} threads for intra-thread pool", num_cores);
    let session = Session::builder()?
        .with_optimization_level(GraphOptimizationLevel::Level3)? //启用最高优化等级
        .with_intra_threads(num_cores)?
        .with_parallel_execution(false)?
        .with_memory_pattern(false)? // 启用内存模式
        .commit_from_file(yolo_onnx_model_path)?;
    let session = Arc::new(session);
    Ok(session)
}

// 优化后的目标检测函数
async fn detect_image(
    image_data: &[u8],
    session: &Arc<Session>,
) -> anyhow::Result<ModelDetectionResult> {
    let start_time = Instant::now();

    // 图像加载和预处理可以放在一个独立的任务中
    let image_data = image_data.to_vec(); // 克隆数据
    let img = tokio::task::spawn_blocking(move || {
        let img = image::load_from_memory(&image_data)?;
        let resized = img.resize_exact(IMAGE_WIDTH, IMAGE_HEIGHT, FilterType::CatmullRom);
        anyhow::Ok((img, resized))
    }).await??;

    let img=img.1;

    // 并行处理图像预处理
    let input = preprocess_image(&img);

    log::debug!("预处理时间: {} ms", start_time.elapsed().as_millis());
    let inference_start = Instant::now();


    // 使用异步推理
    let input_tensor = Value::from_array(input.into_dyn())?;
    let inputs = inputs!["images" => input_tensor]?;
    let outputs = session.run_async(inputs)?.await?;    

    log::debug!("推理时间: {} ms", inference_start.elapsed().as_millis());
    let postprocess_start = Instant::now();

    // 提取并处理输出
    let output = outputs["output0"]
        .try_extract_tensor::<f32>()?
        .into_owned()
        .into_dimensionality::<Ix3>()?
        .index_axis_move(Axis(0), 0)
        .t()
        .to_owned();

    // 并行后处理
    let detections = postprocess_output_parallel(&output, img.width(), img.height()).unwrap();

    log::debug!("后处理时间: {} ms", postprocess_start.elapsed().as_millis());

    let num_persons = detections.iter().filter(|d| d.class_name == "person").count();

    // 并行绘制检测结果
    let annotated_img = draw_detections_parallel(img, &detections);
    //let encoded_img = encode_image(annotated_img).unwrap();

    Ok(ModelDetectionResult {
        num_persons,
        boxes: detections,
        processing_time_ms: start_time.elapsed().as_millis(),
        annotated_image: None,
    })
}

// 并行图像预处理
fn preprocess_image(resized: &DynamicImage) -> Array4<f32> {
    let width = IMAGE_WIDTH as usize;
    let height = IMAGE_HEIGHT as usize;

    // 创建一个共享的可变数组
    let input = Arc::new(Mutex::new(Array4::zeros((1, 3, height, width))));

    // 收集所有像素
    let pixels: Vec<_> = resized.pixels().collect();

    // 并行处理像素
    pixels.par_iter().for_each(|pixel| {
        let x = pixel.0 as usize;
        let y = pixel.1 as usize;
        let [r, g, b, _] = pixel.2 .0;

        let mut input = input.lock().unwrap();
        input[[0, 0, y, x]] = (r as f32) / 255.;
        input[[0, 1, y, x]] = (g as f32) / 255.;
        input[[0, 2, y, x]] = (b as f32) / 255.;
    });

    // 返回处理后的数组
    Arc::try_unwrap(input).unwrap().into_inner().unwrap()
}

// 并行后处理
fn postprocess_output_parallel(
    output: &Array2<f32>,
    img_width: u32,
    img_height: u32,
) -> Result<Vec<DetectionBox>, Box<dyn std::error::Error>> {
    let num_classes = YOLOV8_CLASS_LABELS.len();

    let detections: Vec<_> = output
        .axis_iter(Axis(0))
        .enumerate()
        .par_bridge()
        .filter_map(|(_, row)| {
            if row.len() != 4 + num_classes {
                return None;
            }

            let x = row[0];
            let y = row[1];
            let w = row[2];
            let h = row[3];

            let class_probs = row.slice(s![4..]);

            if let Some((class_id, &confidence)) = class_probs
                .iter()
                .enumerate()
                .max_by(|a, b| a.1.partial_cmp(b.1).unwrap())
            {
                if confidence < CONFIDENCE_THRESHOLD {
                    return None;
                }

                let label = YOLOV8_CLASS_LABELS[class_id];

                // 转换坐标
                let xc = x / IMAGE_WIDTH as f32 * img_width as f32;
                let yc = y / IMAGE_HEIGHT as f32 * img_height as f32;
                let w = w / IMAGE_WIDTH as f32 * img_width as f32;
                let h = h / IMAGE_HEIGHT as f32 * img_height as f32;

                Some(DetectionBox {
                    class_name: label.to_string(),
                    confidence,
                    bbox: [xc - w / 2.0, yc - h / 2.0, xc + w / 2.0, yc + h / 2.0],
                })
            } else {
                None
            }
        })
        .collect();

    Ok(non_max_suppression(&mut detections.clone(), 0.5))
}

// 并行绘制检测结果
fn draw_detections_parallel(img: DynamicImage, detections: &[DetectionBox]) -> DynamicImage {
    let mut img = img.to_rgba8();
    let width = img.width();
    let height = img.height();
    let font_data = include_bytes!("../../font/DejaVuSansMono.ttf");
    let font = Arc::new(FontArc::try_from_slice(font_data).unwrap());
    let scale = PxScale::from(20.0);
  
    // 将图像分成多个垂直条带
    let num_strips = num_cpus::get();
    let strip_width = width / num_strips as u32;
  
    let strips: Vec<_> = (0..num_strips).map(|i| {
        let start_x = i as u32 * strip_width;
        let end_x = if i == num_strips - 1 { width } else { (i + 1) as u32 * strip_width };
        (start_x, end_x)
    }).collect();
  
    // 并行处理每个条带
    let processed_strips: Vec<_> = strips.par_iter().map(|&(start_x, end_x)| {
        let mut strip = img.view(start_x, 0, end_x - start_x, height).to_image();
        let font = Arc::clone(&font);
  
        // 处理与当前条带相交的检测框
        for detection in detections {
            let x1 = detection.bbox[0].max(0.0) as i32;
            let y1 = detection.bbox[1].max(0.0) as i32;
            let x2 = detection.bbox[2].min(width as f32) as i32;
            let y2 = detection.bbox[3].min(height as f32) as i32;
  
            // 检查检测框是否与当前条带相交
            if x2 >= start_x as i32 && x1 < end_x as i32 {
                let rect = Rect::at(x1 - start_x as i32, y1)
                    .of_size((x2 - x1) as u32, (y2 - y1) as u32);
                let color = match detection.class_name.as_str() {
                    "person" => Rgba([255, 0, 0, 255]),
                    _ => Rgba([0, 255, 0, 255]),
                };
  
                draw_hollow_rect_mut(&mut strip, rect, color);
  
                // 只在检测框起始位置所在的条带绘制文本
                if x1 >= start_x as i32 && x1 < end_x as i32 {
                    let text = format!("{}: {:.2}", detection.class_name, detection.confidence);
                    // 使用解引用的 FontArc
                    draw_text_mut(&mut strip, color, x1 - start_x as i32, y1 - 20, scale, &*font, &text);
                }
            }
        }
        (start_x, strip)
    }).collect();
  
    // 将处理后的条带合并回原图，转换坐标类型为 i64
    for (start_x, strip) in processed_strips {
        image::imageops::replace(&mut img, &strip, start_x as i64, 0);
    }
  
    DynamicImage::ImageRgba8(img)
}


// 绘制检测结果边框和标签
fn draw_detections(img: DynamicImage, detections: &[DetectionBox]) -> DynamicImage {
    let mut img = img.to_rgba8();
    let font_data = include_bytes!("../../font/DejaVuSansMono.ttf");
    let font = FontArc::try_from_slice(font_data).unwrap();
    let scale = PxScale::from(20.0);

    for detection in detections {
        let x1 = detection.bbox[0].max(0.0) as i32;
        let y1 = detection.bbox[1].max(0.0) as i32;
        let x2 = detection.bbox[2].min(img.width() as f32) as i32;
        let y2 = detection.bbox[3].min(img.height() as f32) as i32;

        let rect = Rect::at(x1, y1).of_size((x2 - x1) as u32, (y2 - y1) as u32);
        let color = match detection.class_name.as_str() {
            "person" => Rgba([255, 0, 0, 255]),
            _ => Rgba([0, 255, 0, 255]),
        };
        draw_hollow_rect_mut(&mut img, rect, color);

        // 绘制标签文字
        let text = format!("{}: {:.2}", detection.class_name, detection.confidence);
        draw_text_mut(&mut img, color, x1, y1 - 20, scale, &font, &text);
    }

    DynamicImage::ImageRgba8(img)
}


fn encode_image(img: DynamicImage) -> Result<String> {
    let mut buf = Vec::new();
    img.write_to(&mut Cursor::new(&mut buf), ImageFormat::Jpeg)?;
    Ok(general_purpose::STANDARD.encode(&buf))
}

// 获取模型路径
fn get_model_path() -> PathBuf {
    let model_filename = "yolov8m.onnx";//"yolov8m.onnx";
    // 首先检查是否设置了环境变量
    if let Ok(path) = env::var("YOLO_ONNX_MODEL_PATH") {
        return PathBuf::from(path);
    }

    // 如果环境变量未设置，使用默认路径
    #[cfg(debug_assertions)]
    {
        // 在调试模式下，使用项目根目录
        let manifest_dir = env!("CARGO_MANIFEST_DIR");
        PathBuf::from(manifest_dir).join(model_filename)
    }

    #[cfg(not(debug_assertions))]
    {
        // 在发布模式下，使用可执行文件所在目录
        let mut exe_path = env::current_exe().expect("Failed to get executable path");
        exe_path.pop(); // 移除可执行文件名，只保留目录
        exe_path.join(model_filename)
    }
}

// 非极大值抑制,去除重叠的边界框,用于去除重复检测的目标
fn non_max_suppression(
    detections: &mut Vec<DetectionBox>,
    iou_threshold: f32,
) -> Vec<DetectionBox> {
    detections.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap());

    let mut result = Vec::new();
    while let Some(detection) = detections.pop() {
        result.push(detection.clone());
        detections.retain(|d| {
            let iou = calculate_iou(&detection, &d);
            iou < iou_threshold
        });
    }
    result
}