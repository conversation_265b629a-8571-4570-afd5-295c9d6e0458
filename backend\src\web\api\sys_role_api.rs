//src/web/api/sys_role_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete, get, post},
    Json, Router,
};
use log::{error, info};
use std::sync::Arc;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    sys_role::{
        PageRequest, PageResponse, RoleCreateRequest, RolePermissionRequest, RoleUpdateRequest,
        SysRoleEntity,
    },
};

pub fn register_sys_role_api() -> Router<Arc<AppState>> {
    Router::new().nest("/sys_role", sys_role_routes())
}

fn sys_role_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create))
        .route("/update", post(update))
        .route("/delete/:id", delete(delete_role))
        .route("/page", post(get_page))
        .route("/assign_permissions", post(assign_permissions))
        .route("/permissions/:role_id", get(get_role_permissions))
}

async fn create(
    State(state): State<Arc<AppState>>,
    <PERSON><PERSON>(req): Json<RoleCreateRequest>,
) -> Json<ApiResponse<()>> {
    info!("Creating role: {:?}", req);
    match state.service_manager.sys_role_service.create(req).await {
        Ok(_) => {
            info!("Successfully created role");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to create role: {:#}", e);
            Json(ApiResponse::error(format!("创建失败: {:#}", e)))
        }
    }
}

async fn update(
    State(state): State<Arc<AppState>>,
    Json(req): Json<RoleUpdateRequest>,
) -> Json<ApiResponse<()>> {
    info!("Updating role: {:?}", req);
    match state.service_manager.sys_role_service.update(req).await {
        Ok(_) => {
            info!("Successfully updated role");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to update role: {:#}", e);
            Json(ApiResponse::error(format!("更新失败: {:#}", e)))
        }
    }
}

async fn delete_role(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    info!("Deleting role with id: {}", id);
    match state.service_manager.sys_role_service.delete(&id).await {
        Ok(_) => {
            info!("Successfully deleted role");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to delete role: {:#}", e);
            Json(ApiResponse::error(format!("删除失败: {:#}", e)))
        }
    }
}

async fn get_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<PageRequest>,
) -> Json<ApiResponse<PageResponse<SysRoleEntity>>> {
    info!("Getting role page: {:?}", req);
    match state.service_manager.sys_role_service.get_page(req).await {
        Ok(page) => {
            info!("Successfully got role page");
            Json(ApiResponse::success(Some(page)))
        }
        Err(e) => {
            error!("Failed to get role page: {:#}", e);
            Json(ApiResponse::error(format!("获取失败: {:#}", e)))
        }
    }
}

async fn assign_permissions(
    State(state): State<Arc<AppState>>,
    Json(req): Json<RolePermissionRequest>,
) -> Json<ApiResponse<()>> {
    info!("Assigning permissions to role: {:?}", req);
    match state
        .service_manager
        .sys_role_service
        .assign_permissions(req)
        .await
    {
        Ok(_) => {
            info!("Successfully assigned permissions");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to assign permissions: {:#}", e);
            Json(ApiResponse::error(format!("分配权限失败: {:#}", e)))
        }
    }
}

async fn get_role_permissions(
    State(state): State<Arc<AppState>>,
    Path(role_id): Path<String>,
) -> Json<ApiResponse<Vec<String>>> {
    info!("Getting permissions for role: {}", role_id);
    match state
        .service_manager
        .sys_role_service
        .get_role_permissions(&role_id)
        .await
    {
        Ok(permissions) => {
            info!("Successfully got role permissions");
            Json(ApiResponse::success(Some(permissions)))
        }
        Err(e) => {
            error!("Failed to get role permissions: {:#}", e);
            Json(ApiResponse::error(format!("获取角色权限失败: {:#}", e)))
        }
    }
}