refresh_rate: 30 seconds

appenders:
  console:
    kind: console
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S)} [{t}] {h({l})} {f}:{L} [{M}] - {m}{n}"

  file:
    kind: rolling_file
    path: "logs/app.log"
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S)} [{t}] {h({l})} {f}:{L} [{M}] - {m}{n}"
    policy:
      trigger:
        kind: size
        limit: 50mb
      roller:
        kind: fixed_window
        pattern: logs/app_{}.log
        base: 1
        count: 5


root:
  level: debug
  appenders:
    - console
    - file

loggers:
  app::worker:
    level: debug
