//rtsp/ffmpeg_frame_worker.rs
use log::{debug, error};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::Semaphore;
use tokio::time::Duration;

use crate::config::{self};
use crate::model::frame_queue::FrameQueue;
use crate::model::rtsp_source_manager::RtspManager;
use crate::rtsp::ffmpeg_capture_room::FrameCaptureRoom;
use crate::rtsp::ffmpeg_capture_person_detector::FrameCaptureAndDetector;
use crate::onnx::yolo_detector::ObjectDetector;

pub struct FrameCaptureWorker {
    frame_queue: Arc<FrameQueue>,
    rtsp_manager: Arc<RtspManager>,
    config: Arc<config::Config>,
    semaphore: Arc<Semaphore>,
    current_index: Arc<AtomicUsize>,
    worker_id_counter: Arc<AtomicUsize>,
    total_frame_counter: Arc<AtomicUsize>,
    images_dir: Arc<String>,
    detector: Arc<dyn ObjectDetector + Send + Sync>,
}

impl FrameCaptureWorker {
    pub fn new(
        frame_queue: Arc<FrameQueue>,
        rtsp_manager: Arc<RtspManager>,
        config: Arc<config::Config>,
        semaphore: Arc<Semaphore>,
        current_index: Arc<AtomicUsize>,
        worker_id_counter: Arc<AtomicUsize>,
        total_frame_counter: Arc<AtomicUsize>,
        images_dir: Arc<String>,
        detector: Arc<dyn ObjectDetector + Send + Sync>,
    ) -> Self {
        Self {
            frame_queue,
            rtsp_manager,
            config,
            semaphore,
            current_index,
            worker_id_counter,
            total_frame_counter,
            images_dir,
            detector,
        }
    }

    pub fn spawn(&self) -> tokio::task::JoinHandle<()> {
        let rtsp_manager = Arc::clone(&self.rtsp_manager);
        let config = Arc::clone(&self.config);
        let frame_queue = Arc::clone(&self.frame_queue);
        let current_index = Arc::clone(&self.current_index);
        let semaphore = Arc::clone(&self.semaphore);
        let worker_id_counter = Arc::clone(&self.worker_id_counter);
        let max_concurrent = Arc::clone(&self.semaphore);
        let total_frame_counter = Arc::clone(&self.total_frame_counter);
        let detector = Arc::clone(&self.detector);

        tokio::spawn(async move {
            let mut handles = Vec::new();
            let worker_count = max_concurrent.available_permits();

            for _ in 0..worker_count {
                let rtsp_manager = Arc::clone(&rtsp_manager);
                let config = Arc::clone(&config);
                let frame_queue = Arc::clone(&frame_queue);
                let current_index = Arc::clone(&current_index);
                let semaphore = Arc::clone(&semaphore);
                let worker_id = worker_id_counter.fetch_add(1, Ordering::SeqCst);
                let total_frame_counter = Arc::clone(&total_frame_counter);
                let detector = Arc::clone(&detector);

                let handle = tokio::spawn(async move {
                    loop {
                        let _permit = semaphore.acquire().await.unwrap();

                        // 检查是否需要更新RTSP源列表
                        if let Err(e) = rtsp_manager.update_sources().await {
                            error!("Failed to update RTSP sources: {}", e);
                        }

                        // 获取当前的RTSP源列表
                        let urls = {
                            let sources = rtsp_manager.get_sources();
                            let sources_guard = sources.read().unwrap();
                            sources_guard.clone() // 克隆数据以便后续使用
                        };

                        let index = current_index.fetch_add(1, Ordering::SeqCst);

                        if index >= urls.len() {
                            // 重置索引以循环使用
                            current_index.store(0, Ordering::SeqCst);
                            continue;
                        }

                        let url = urls[index].clone(); // 使用克隆的数据

                        debug!("Worker {} processing stream: {}", worker_id, url);

                        if config.rtsp.use_yolo_detection {
                            //使用yolo检测的情况
                            if frame_queue.usage_percentage() > 90.0 {
                                current_index.fetch_sub(1, Ordering::SeqCst);
                                tokio::time::sleep(Duration::from_secs(2)).await;
                                continue;
                            }

                            // 使用timeout包装整个操作,以避免长时间阻塞
                            match tokio::time::timeout(
                                Duration::from_secs(config.rtsp.cap_time_out as u64),
                                FrameCaptureAndDetector::capture_and_detect(worker_id, &url.clone(), &detector, &frame_queue),
                            )
                            .await
                            {
                                Ok(_) => {
                                    // 处理检测结果
                                }
                                Err(_) => {
                                    error!("Timeout while decoding frame from {}", url);
                                }
                            }

                        } else {
                            //不使用yolo检测的情况
                            if let Ok(decoder) = FrameCaptureRoom::new(worker_id, url.clone()) {
                                if frame_queue.usage_percentage() > 90.0 {
                                    current_index.fetch_sub(1, Ordering::SeqCst);
                                    tokio::time::sleep(Duration::from_secs(2)).await;
                                    continue;
                                }

                                // 使用timeout包装整个操作,以避免长时间阻塞
                                match tokio::time::timeout(
                                    Duration::from_secs(config.rtsp.cap_time_out as u64),
                                    decoder.decode_room_frames(),
                                )
                                .await
                                {
                                    Ok(decode_result) => match decode_result {
                                        Ok(Some(frame)) => {
                                            let image_count = frame.frames.len();
                                            if frame_queue.push(frame) {
                                                total_frame_counter
                                                    .fetch_add(image_count, Ordering::SeqCst);
                                            } else {
                                                error!("Queue full, dropping frame from {}", url);
                                            }
                                        }
                                        Ok(None) => {
                                            debug!("No frame available from {}", url);
                                        }
                                        Err(e) => {
                                            error!("Error decoding frame from {}: {}", url, e);
                                        }
                                    },
                                    Err(_) => {
                                        error!("Timeout while decoding frame from {}", url);
                                    }
                                }
                            }
                        }

                        // 如果使用了sleep,则等待一段时间来降低cpu占用
                        if config.rtsp.use_cap_sleep {
                            tokio::time::sleep(Duration::from_millis(config.rtsp.cap_sleep_time))
                                .await;
                        } else {
                            tokio::task::yield_now().await;
                        }
                    }
                });
                handles.push(handle);
            }

            for handle in handles {
                let _ = handle.await;
            }
        })
    }
}
