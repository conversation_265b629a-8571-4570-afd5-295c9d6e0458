编译项目需要安装以下依赖：

1. 安装rust
2. 安装openssl-dev,apt install libssl-dev
3. 安装ffmpeg-dev,apt install libavutil-dev libavcodec-dev libavformat-dev libavdevice-dev libavfilter-dev libresample-dev libswresample-dev libswscale-dev
4. 安装clang,apt install clang
5. 安装cmake,apt install cmake
6. 安装protobuf,unzip protobuf-28.3.zip,在/etc/profile中添加export PATH=/data/protoc/bin:$PATH，export PROTOC=/data/protoc/bin/protoc
7. 安装sqlx,cargo install sqlx-cli,cargo sqlx prepare




export DATABASE_URL=***********************************************/ycxw

echo "DATABASE_URL=***********************************************/ycxw" > .env

安装nodejs开发环境
curl -fsSL https://deb.nodesource.com/setup_23.x | sudo -E bash -
sudo apt-get install -y nodejs