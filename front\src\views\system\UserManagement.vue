<!-- src/views/system/UserManagement.vue -->
<template>
  <div class="flex h-full">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 p-4 border-r overflow-hidden flex flex-col">
      <div class="mb-4">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索区划/机构"
          clearable
          prefix-icon="Search"
        />
      </div>
      <div class="flex-1 overflow-auto">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="flex items-center">
              <el-icon class="mr-1">
                <Location v-if="data.type === 'area'" />
                <OfficeBuilding v-else />
              </el-icon>
              <span>{{ data.name }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 p-4 overflow-hidden flex flex-col">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleAdd">新增用户</el-button>
          <el-button
            type="danger"
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
        <div class="flex space-x-2">
          <el-input
            v-model="searchText"
            placeholder="搜索用户名/邮箱/姓名"
            class="w-64"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>

      <!-- 当前位置显示 -->
      <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        border
        stripe
        class="flex-1"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="full_name" label="姓名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formData.username"
            :disabled="!!formData.user_id"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!formData.user_id">
          <el-input v-model="formData.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" />
        </el-form-item>
        <el-form-item label="姓名" prop="full_name">
          <el-input v-model="formData.full_name" />
        </el-form-item>
        <el-form-item label="所属机构">
          <el-input
            v-model="formData.organization_name"
            disabled
            placeholder="请在左侧树中选择机构"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Location, OfficeBuilding } from "@element-plus/icons-vue";
import { format } from "date-fns";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const currentNode = ref(null);
const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

// 表格相关
const loading = ref(false);
const tableRef = ref(null);
const tableData = ref([]);
const selectedRows = ref([]);
const searchText = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增用户");
const formRef = ref(null);
const formData = ref({
  user_id: "",
  username: "",
  password: "",
  email: "",
  full_name: "",
  organization_id: "",
  organization_name: "",
  is_active: true,
});

const formRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" },
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
  ],
};

// 计算属性
const currentPath = computed(() => {
  if (!currentNode.value) return "全部";
  return getNodePath(currentNode.value);
});


const loadNode = async (node, resolve) => {
  const loading = ElLoading.service({
    target: treeRef.value?.$el,
  });

  try {
    if (node.level === 0) {
      // 加载省级数据
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map((item) => ({
        ...item,
        id: item.code,
        type: "area",
        leaf: false,
      }));
      resolve(areas);
    } else {
      const { data } = node;
      if (data.type === "area") {
        // 并行加载下级区划和机构
        const [areaRes, orgRes] = await Promise.all([
          axios.get("/sys_area_info/lazy_tree_node", {
            params: { parent_code: data.code },
          }),
          axios.get(`/office_organization/list/${data.code}`),
        ]);

        const areas = areaRes.data.data.map((item) => ({
          ...item,
          id: item.code,
          type: "area",
          // 根据级别判断是否还有子节点
          leaf: item.level >= 4, // 假设区划最多4级
        }));

        const orgs = orgRes.data.data.map((item) => ({
          id: item.office_id,
          code: item.office_id, // 添加 code 字段保持一致性
          name: item.office_name, // 使用 office_name 作为显示名称
          type: "org",
          leaf: true,
          // 保存原始数据以供后续使用
          office_id: item.office_id,
          office_name: item.office_name,
          office_address: item.office_address,
          area_code: item.area_code,
          is_active: item.is_active,
          created_at: item.created_at,
          updated_at: item.updated_at,
        }));

        resolve([...areas, ...orgs]);
      } else {
        resolve([]);
      }
    }
  } catch (error) {
    ElMessage.error("加载数据失败");
    resolve([]);
  } finally {
    loading.close();
  }
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

const getNodePath = (node) => {
  const path = [];
  let current = node;
  while (current.parent && current.parent.level !== 0) {
    path.unshift(current.label);
    current = current.parent;
  }
  path.unshift(current.label);
  return path.join(" / ");
};

// 继续 UserManagement.vue 的 script 部分

// 节点点击处理
const handleNodeClick = (data, node) => {
  currentNode.value = node;
  if (data.type === "org") {
    formData.value.organization_id = data.office_id;
    formData.value.organization_name = data.office_name;
  } else {
    formData.value.organization_id = "";
    formData.value.organization_name = "";
  }
  loadTableData();
};

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  try {
    const res = await axios.get("/sys_user/list", {
      params: {
        page: currentPage.value,
        page_size: pageSize.value,
        organization_id: formData.value.organization_id,
        search_text: searchText.value,
      },
    });
    tableData.value = res.data.data.items;
    total.value = res.data.data.total;
  } catch (error) {
    ElMessage.error("加载用户数据失败");
  } finally {
    loading.value = false;
  }
};

// 表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  searchText.value = "";
  currentPage.value = 1;
  loadTableData();
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadTableData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadTableData();
};

// 新增用户
const handleAdd = () => {
  if (!formData.value.organization_id) {
    ElMessage.warning("请先在左侧选择一个机构");
    return;
  }
  dialogTitle.value = "新增用户";
  formData.value = {
    user_id: "",
    username: "",
    password: "",
    email: "",
    full_name: "",
    organization_id: formData.value.organization_id,
    organization_name: formData.value.organization_name,
    is_active: true,
  };
  dialogVisible.value = true;
};

// 编辑用户
const handleEdit = (row) => {
  dialogTitle.value = "编辑用户";
  formData.value = {
    ...row,
    organization_name: currentNode.value?.data?.office_name || "",
  };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (formData.value.user_id) {
      // 编辑
      await axios.put("/sys_user/update", {
        user_id: formData.value.user_id,
        email: formData.value.email,
        full_name: formData.value.full_name,
        organization_id: formData.value.organization_id,
        is_active: formData.value.is_active,
      });
      ElMessage.success("更新成功");
    } else {
      // 新增
      await axios.post("/sys_user/create", {
        username: formData.value.username,
        password: formData.value.password,
        email: formData.value.email,
        full_name: formData.value.full_name,
        organization_id: formData.value.organization_id,
        is_active: formData.value.is_active,
      });
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    loadTableData();
  } catch (error) {
    if (error?.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("操作失败");
    }
  }
};

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确定要删除该用户吗？此操作不可恢复！", "警告", {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    await axios.delete(`/sys_user/delete/${row.user_id}`);
    ElMessage.success("删除成功");
    loadTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个用户吗？此操作不可恢复！`,
      "警告",
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    );

    await Promise.all(
      selectedRows.value.map((row) =>
        axios.delete(`/sys_user/delete/${row.user_id}`)
      )
    );

    ElMessage.success("批量删除成功");
    loadTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
    }
  }
};

// 状态变更
const handleStatusChange = async (row) => {
  try {
    await axios.put("/sys_user/update", {
      user_id: row.user_id,
      is_active: row.is_active,
    });
    ElMessage.success("状态更新成功");
  } catch (error) {
    row.is_active = !row.is_active;
    ElMessage.error("状态更新失败");
  }
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return "-";
  return format(new Date(date), "yyyy-MM-dd HH:mm:ss");
};

// 监听器
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val);
});

// 生命周期钩子
onMounted(() => {
  loadTableData();
});
</script>

<style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

.el-dialog {
  max-width: 90%;
}

:deep(.el-table) {
  height: 100%;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}
</style>
