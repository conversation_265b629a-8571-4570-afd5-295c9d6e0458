-- 创建数据库
CREATE DATABASE ycxw;

-- 连接到新创建的数据库
\c ycxw

-- 创建行政区划信息表
CREATE TABLE sys_area_info (
    code VARCHAR(20) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_code VARCHAR(20),
    level INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE sys_area_info IS '行政区划信息表';
COMMENT ON COLUMN sys_area_info.code IS '行政区划编码';
COMMENT ON COLUMN sys_area_info.name IS '行政区划名称';
COMMENT ON COLUMN sys_area_info.parent_code IS '父级行政区划ID';
COMMENT ON COLUMN sys_area_info.level IS '行政区划级别';
COMMENT ON COLUMN sys_area_info.is_active IS '是否启用';
COMMENT ON COLUMN sys_area_info.created_at IS '创建时间';
COMMENT ON COLUMN sys_area_info.updated_at IS '更新时间';

-- 创建房间用途分类字典表
CREATE TABLE room_purpose_type (
    room_purpose_type_id VARCHAR(50) PRIMARY KEY,
    room_purpose_type_name VARCHAR(50) NOT NULL,
    room_purpose_type_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE room_purpose_type IS '房间用途分类字典表';
COMMENT ON COLUMN room_purpose_type.room_purpose_type_id IS '主键ID';
COMMENT ON COLUMN room_purpose_type.room_purpose_type_name IS '用途名称';
COMMENT ON COLUMN room_purpose_type.room_purpose_type_description IS '用途描述';
COMMENT ON COLUMN room_purpose_type.is_active IS '是否启用';
COMMENT ON COLUMN room_purpose_type.created_at IS '创建时间';
COMMENT ON COLUMN room_purpose_type.updated_at IS '更新时间';


-- 插入默认的字典数据值
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('1', '询问室');
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('2', '讯问室');
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('3', '侯问室');
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('4', '辨认室');
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('5', '办事大厅');
INSERT INTO room_purpose_type (room_purpose_type_id, room_purpose_type_name) VALUES ('6', '其他');


-- 创建办公机构信息表
CREATE TABLE office_organization (
    office_id VARCHAR(50) PRIMARY KEY,
    office_name VARCHAR(100) NOT NULL,
    office_address TEXT,
    area_code VARCHAR(20) REFERENCES sys_area_info(code),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE office_organization IS '办公机构信息表';
COMMENT ON COLUMN office_organization.office_id IS '主键ID';
COMMENT ON COLUMN office_organization.office_name IS '机构名称';
COMMENT ON COLUMN office_organization.office_address IS '机构地址';
COMMENT ON COLUMN office_organization.area_code IS '所属行政区划code';
COMMENT ON COLUMN office_organization.is_active IS '是否启用';
COMMENT ON COLUMN office_organization.created_at IS '创建时间';
COMMENT ON COLUMN office_organization.updated_at IS '更新时间';

-- 创建楼宇信息表
CREATE TABLE building (
    building_id VARCHAR(50) PRIMARY KEY,
    building_name VARCHAR(100) NOT NULL,
    office_id VARCHAR(50) REFERENCES office_organization(office_id),
    floors INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE building IS '楼宇信息表';
COMMENT ON COLUMN building.building_id IS '主键ID';
COMMENT ON COLUMN building.building_name IS '楼宇名称';
COMMENT ON COLUMN building.office_id IS '所属机构ID';
COMMENT ON COLUMN building.floors IS '楼层数';
COMMENT ON COLUMN building.is_active IS '是否启用';
COMMENT ON COLUMN building.created_at IS '创建时间';
COMMENT ON COLUMN building.updated_at IS '更新时间';

-- 创建位置信息表（包括房间、楼道、大院等）
CREATE TABLE location (
    location_id VARCHAR(50) PRIMARY KEY,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(20) NOT NULL CHECK (location_type IN ('room', 'corridor', 'yard', 'other')),
    building_id VARCHAR(50) REFERENCES building(building_id),
    floor INTEGER,
    purpose_type_id VARCHAR(50) REFERENCES room_purpose_type(room_purpose_type_id),
    area INTEGER,
    capacity INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE location IS '位置信息表';
COMMENT ON COLUMN location.location_id IS '位置信息表主键ID';
COMMENT ON COLUMN location.location_name IS '位置名称';
COMMENT ON COLUMN location.location_type IS '位置类型（房间、楼道、大院等）';
COMMENT ON COLUMN location.building_id IS '所属楼宇ID';
COMMENT ON COLUMN location.floor IS '所在楼层（如适用）';
COMMENT ON COLUMN location.purpose_type_id IS '用途类型ID（如适用）';
COMMENT ON COLUMN location.area IS '面积（平方米，如适用）';
COMMENT ON COLUMN location.capacity IS '容纳人数（如适用）';
COMMENT ON COLUMN location.is_active IS '是否启用';
COMMENT ON COLUMN location.created_at IS '创建时间';
COMMENT ON COLUMN location.updated_at IS '更新时间';

-- 创建摄像头信息表
CREATE TABLE camera (
    camera_id VARCHAR(50) PRIMARY KEY,
    camera_name VARCHAR(100) NOT NULL,
    camera_description TEXT,
    camera_ip_address VARCHAR(50) NOT NULL,
    camera_username VARCHAR(50) NOT NULL,
    camera_password VARCHAR(100) NOT NULL,
    camera_rtsp_stream_url TEXT NOT NULL,
    location_id VARCHAR(50) REFERENCES location(location_id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE camera IS '摄像头信息表';
COMMENT ON COLUMN camera.camera_id IS '摄像头主键ID';
COMMENT ON COLUMN camera.camera_name IS '摄像头名称';
COMMENT ON COLUMN camera.camera_description IS '摄像头描述';
COMMENT ON COLUMN camera.camera_ip_address IS '摄像头IP地址';
COMMENT ON COLUMN camera.camera_username IS '访问用户名';
COMMENT ON COLUMN camera.camera_password IS '访问密码';
COMMENT ON COLUMN camera.camera_rtsp_stream_url IS 'RTSP码流地址格式';
COMMENT ON COLUMN camera.location_id IS '位置ID';
COMMENT ON COLUMN camera.status IS '摄像头状态';
COMMENT ON COLUMN camera.is_active IS '是否启用';
COMMENT ON COLUMN camera.created_at IS '创建时间';
COMMENT ON COLUMN camera.updated_at IS '更新时间';

-- RBAC相关表结构

-- 用户表
CREATE TABLE sys_user (
    user_id VARCHAR(50) PRIMARY KEY,
    organization_id VARCHAR(50),
    username VARCHAR(50) UNIQUE NOT NULL,
    user_pass VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE sys_user IS '用户表';
COMMENT ON COLUMN sys_user.user_id IS '用户主键ID';
COMMENT ON COLUMN sys_user.organization_id IS '组织机构ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.user_pass IS '密码（加密存储）';
COMMENT ON COLUMN sys_user.email IS '电子邮箱';
COMMENT ON COLUMN sys_user.full_name IS '姓名';
COMMENT ON COLUMN sys_user.is_active IS '是否启用';
COMMENT ON COLUMN sys_user.last_login IS '最后登录时间';
COMMENT ON COLUMN sys_user.created_at IS '创建时间';
COMMENT ON COLUMN sys_user.updated_at IS '更新时间';



-- 角色表
CREATE TABLE sys_role (
    role_id VARCHAR(50) PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    role_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE sys_role IS '角色表';
COMMENT ON COLUMN sys_role.role_id IS '角色主键ID';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.role_description IS '角色描述';
COMMENT ON COLUMN sys_role.is_active IS '是否启用';
COMMENT ON COLUMN sys_role.created_at IS '创建时间';
COMMENT ON COLUMN sys_role.updated_at IS '更新时间';

-- 修改权限表结构  
DROP TABLE IF EXISTS sys_role_permission;  
DROP TABLE IF EXISTS sys_permission;  

CREATE TABLE sys_permission (  
    permission_id VARCHAR(50) PRIMARY KEY,  
    parent_id VARCHAR(50),  
    permission_name VARCHAR(100) NOT NULL,  
    permission_code VARCHAR(100) UNIQUE,  -- 权限标识符，用于后端权限验证  
    permission_type VARCHAR(20) NOT NULL, -- 类型：menu-菜单，button-按钮，api-接口  
    component_path VARCHAR(200),          -- 前端组件路径  
    path VARCHAR(200),                    -- 路由路径  
    redirect VARCHAR(200),                -- 重定向路径  
    icon VARCHAR(100),                    -- 图标  
    sort_order INT DEFAULT 0,             -- 排序号  
    is_hidden BOOLEAN DEFAULT FALSE,      -- 是否隐藏  
    permission_description TEXT,  
    is_active BOOLEAN DEFAULT TRUE,  
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,  
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,  
    FOREIGN KEY (parent_id) REFERENCES sys_permission(permission_id) ON DELETE CASCADE  
);  

CREATE INDEX idx_permission_parent_id ON sys_permission(parent_id);  
CREATE INDEX idx_permission_sort ON sys_permission(sort_order);  

COMMENT ON TABLE sys_permission IS '权限表';  
COMMENT ON COLUMN sys_permission.permission_id IS '权限主键ID';  
COMMENT ON COLUMN sys_permission.parent_id IS '父级权限ID';  
COMMENT ON COLUMN sys_permission.permission_name IS '权限名称';  
COMMENT ON COLUMN sys_permission.permission_code IS '权限标识符';  
COMMENT ON COLUMN sys_permission.permission_type IS '权限类型';  
COMMENT ON COLUMN sys_permission.component_path IS '前端组件路径';  
COMMENT ON COLUMN sys_permission.path IS '路由路径';  
COMMENT ON COLUMN sys_permission.redirect IS '重定向路径';  
COMMENT ON COLUMN sys_permission.icon IS '图标';  
COMMENT ON COLUMN sys_permission.sort_order IS '排序号';  
COMMENT ON COLUMN sys_permission.is_hidden IS '是否隐藏';  
COMMENT ON COLUMN sys_permission.permission_description IS '权限描述';  
COMMENT ON COLUMN sys_permission.is_active IS '是否启用';  
COMMENT ON COLUMN sys_permission.created_at IS '创建时间';  
COMMENT ON COLUMN sys_permission.updated_at IS '更新时间';  


-- 用户角色关联表
CREATE TABLE sys_user_role (
    user_id VARCHAR(50) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    role_id VARCHAR(50) REFERENCES sys_role(role_id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE sys_user_role IS '用户角色关联表';
COMMENT ON COLUMN sys_user_role.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_role.role_id IS '角色ID';
COMMENT ON COLUMN sys_user_role.created_at IS '创建时间';

-- 角色权限关联表
CREATE TABLE sys_role_permission (  
    role_id VARCHAR(50) REFERENCES sys_role(role_id) ON DELETE CASCADE,  
    permission_id VARCHAR(50) REFERENCES sys_permission(permission_id) ON DELETE CASCADE,  
    PRIMARY KEY (role_id, permission_id),  
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP  
);  

COMMENT ON TABLE sys_role_permission IS '角色权限关联表';
COMMENT ON COLUMN sys_role_permission.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_permission.permission_id IS '权限ID';
COMMENT ON COLUMN sys_role_permission.created_at IS '创建时间';



-- 创建异常行为分析区域表
CREATE TABLE camera_analysis_area (
    camera_analysis_area_id VARCHAR(50) PRIMARY KEY,
    camera_id VARCHAR(50) NOT NULL REFERENCES camera(camera_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    algorithm_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE camera_analysis_area IS '异常行为分析区域表';
COMMENT ON COLUMN camera_analysis_area.camera_analysis_area_id IS '异常监控分析区域主键ID';
COMMENT ON COLUMN camera_analysis_area.camera_id IS '关联的摄像头ID';
COMMENT ON COLUMN camera_analysis_area.name IS '区域名称';
COMMENT ON COLUMN camera_analysis_area.description IS '区域描述';
COMMENT ON COLUMN camera_analysis_area.is_active IS '是否启用';
COMMENT ON COLUMN camera_analysis_area.algorithm_count IS '配置的异常行为分析算法数量';
COMMENT ON COLUMN camera_analysis_area.created_at IS '创建时间';
COMMENT ON COLUMN camera_analysis_area.updated_at IS '更新时间';

-- 创建异常行为分析选区表
CREATE TABLE camera_analysis_zone (
    camera_analysis_zone_id VARCHAR(50) PRIMARY KEY,
    camera_analysis_area_id VARCHAR(50) NOT NULL REFERENCES camera_analysis_area(camera_analysis_area_id),
    camera_analysis_zone_name VARCHAR(100) NOT NULL,
    area_type VARCHAR(20) NOT NULL, -- 区域类型，如 '矩形' 或 '多边形等'
    polygon_points TEXT NOT NULL, -- 存储多边形顶点坐标，格式如: "x1,y1;x2,y2;x3,y3;..."
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE camera_analysis_zone IS '异常行为分析选区表';
COMMENT ON COLUMN camera_analysis_zone.camera_analysis_zone_id IS '异常行为分析选区主键ID';
COMMENT ON COLUMN camera_analysis_zone.camera_analysis_area_id IS '关联的分析区域ID';
COMMENT ON COLUMN camera_analysis_zone.camera_analysis_zone_name IS '选区名称';
COMMENT ON COLUMN camera_analysis_zone.area_type IS '选区类型';
COMMENT ON COLUMN camera_analysis_zone.polygon_points IS '多边形选区顶点坐标';
COMMENT ON COLUMN camera_analysis_zone.created_at IS '创建时间';
COMMENT ON COLUMN camera_analysis_zone.updated_at IS '更新时间';

-- 创建算法字典表
CREATE TABLE algorithm_dictionary (
    algorithm_id VARCHAR(50) PRIMARY KEY,
    algorithm_name VARCHAR(100) NOT NULL UNIQUE,
    algorithm_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE algorithm_dictionary IS '算法字典表';
COMMENT ON COLUMN algorithm_dictionary.algorithm_id IS '算法主键ID';
COMMENT ON COLUMN algorithm_dictionary.algorithm_name IS '算法名称';
COMMENT ON COLUMN algorithm_dictionary.algorithm_description IS '算法描述';
COMMENT ON COLUMN algorithm_dictionary.is_active IS '是否启用';
COMMENT ON COLUMN algorithm_dictionary.created_at IS '创建时间';
COMMENT ON COLUMN algorithm_dictionary.updated_at IS '更新时间';


insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_djxxw', '单警讯询问', '按照规定，民警办案时需要两个民警同时在场,此算法检测是否只有一个民警在办案', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_wrks', '无人看守', '检测是否有人看守', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_pangao', '攀高', '检测是否有攀高行为', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_yuejie', '越界', '检测是否有越界行为', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_shuijiao', '睡岗', '检测是否在岗睡觉', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_dadianhua', '打电话玩手机', '检测是否打电话玩手机行为', true);
insert into algorithm_dictionary (algorithm_id, algorithm_name, algorithm_description, is_active) values ('ALG_weichuanjingfu', '未穿警服', '检测是否办案人员是否有不穿警服行为', true);


-- 创建区域算法配置表
CREATE TABLE area_algorithm_config (
    area_algorithm_config_id VARCHAR(50) PRIMARY KEY,
    camera_area_id VARCHAR(50) NOT NULL REFERENCES camera_analysis_area(camera_analysis_area_id),
    algorithm_id VARCHAR(50) NOT NULL REFERENCES algorithm_dictionary(algorithm_id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE area_algorithm_config IS '区域算法配置表';
COMMENT ON COLUMN area_algorithm_config.area_algorithm_config_id IS '区域算法配置主键ID';
COMMENT ON COLUMN area_algorithm_config.camera_area_id IS '关联的分析区域ID';
COMMENT ON COLUMN area_algorithm_config.algorithm_id IS '关联的算法ID';
COMMENT ON COLUMN area_algorithm_config.is_active IS '是否启用';
COMMENT ON COLUMN area_algorithm_config.created_at IS '创建时间';
COMMENT ON COLUMN area_algorithm_config.updated_at IS '更新时间';

-- 创建算法配置详情表
CREATE TABLE algorithm_config_detail (
    algorithm_config_detail_id VARCHAR(50) PRIMARY KEY,
    area_algorithm_config_id VARCHAR(50) NOT NULL REFERENCES area_algorithm_config(area_algorithm_config_id),
    key VARCHAR(100) NOT NULL,
    value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE algorithm_config_detail IS '算法配置详情表';
COMMENT ON COLUMN algorithm_config_detail.algorithm_config_detail_id IS '算法配置详情主键ID';
COMMENT ON COLUMN algorithm_config_detail.area_algorithm_config_id IS '关联的区域算法配置ID';
COMMENT ON COLUMN algorithm_config_detail.key IS '配置项键名';
COMMENT ON COLUMN algorithm_config_detail.value IS '配置项值';
COMMENT ON COLUMN algorithm_config_detail.created_at IS '创建时间';
COMMENT ON COLUMN algorithm_config_detail.updated_at IS '更新时间';

-- 创建索引以提高查询性能
CREATE INDEX idx_camera_analysis_area_camera_id ON camera_analysis_area(camera_id);
CREATE INDEX idx_camera_analysis_zone_area_id ON camera_analysis_zone(camera_analysis_area_id);
CREATE INDEX idx_area_algorithm_config_area_id ON area_algorithm_config(camera_area_id);
CREATE INDEX idx_area_algorithm_config_algorithm_id ON area_algorithm_config(algorithm_id);
CREATE INDEX idx_algorithm_config_detail_config_id ON algorithm_config_detail(area_algorithm_config_id);



-- 创建异常行为报警记录表
CREATE TABLE alarm_record (
    alarm_id VARCHAR(50) PRIMARY KEY,
    camera_id VARCHAR(50) NOT NULL REFERENCES camera(camera_id),
    algorithm_id VARCHAR(50) NOT NULL REFERENCES algorithm_dictionary(algorithm_id),
    alarm_time TIMESTAMP WITH TIME ZONE NOT NULL,
    alarm_image_url TEXT,
    alarm_video_url TEXT,
    is_pushed BOOLEAN DEFAULT FALSE,
    push_time TIMESTAMP WITH TIME ZONE,
    is_confirmed BOOLEAN DEFAULT FALSE,
    confirm_user_id VARCHAR(50) REFERENCES sys_user(user_id),
    confirm_time TIMESTAMP WITH TIME ZONE,
    confirm_note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE alarm_record IS '异常行为报警记录表';
COMMENT ON COLUMN alarm_record.alarm_id IS '报警记录ID';
COMMENT ON COLUMN alarm_record.camera_id IS '报警摄像头ID';
COMMENT ON COLUMN alarm_record.algorithm_id IS '触发报警的算法ID';
COMMENT ON COLUMN alarm_record.alarm_time IS '报警发生时间';
COMMENT ON COLUMN alarm_record.alarm_image_url IS '报警截图URL';
COMMENT ON COLUMN alarm_record.alarm_video_url IS '报警录像URL';
COMMENT ON COLUMN alarm_record.is_pushed IS '是否已推送消息';
COMMENT ON COLUMN alarm_record.push_time IS '消息推送时间';
COMMENT ON COLUMN alarm_record.is_confirmed IS '是否已确认处理';
COMMENT ON COLUMN alarm_record.confirm_user_id IS '确认处理人ID';
COMMENT ON COLUMN alarm_record.confirm_time IS '确认处理时间';
COMMENT ON COLUMN alarm_record.confirm_note IS '处理备注';
COMMENT ON COLUMN alarm_record.created_at IS '创建时间';
COMMENT ON COLUMN alarm_record.updated_at IS '更新时间';

-- 创建索引提高查询性能
CREATE INDEX idx_alarm_record_camera ON alarm_record(camera_id);
CREATE INDEX idx_alarm_record_algorithm ON alarm_record(algorithm_id);
CREATE INDEX idx_alarm_record_time ON alarm_record(alarm_time);
CREATE INDEX idx_alarm_record_confirm_user ON alarm_record(confirm_user_id);


-- 插入默认管理员用户（密码为 shinow 的加密形式）  
INSERT INTO sys_user (user_id, username, user_pass, email, full_name, organization_id)   
VALUES ('ADMIN_001', 'admin', '$argon2id$v=19$m=19456,t=2,p=1$jvZa8sCuktGw36UYVvfJuQ$jhYy3ort08eBdqz4oYCKnccYauCmLQtS/DAqRo4CsZ4', '<EMAIL>', '系统管理员', '-1');  

INSERT INTO sys_role(role_id, role_name,role_description) values ('ROLE_SUPER_ADMIN','超级管理员角色','系统超级管理员角色');

-- 插入管理员与角色关联  
INSERT INTO sys_user_role (user_id, role_id)  VALUES ('ADMIN_001', 'ROLE_SUPER_ADMIN');  

INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-6', '2', '系统参数设定', 'system:params', 'menu', '../views/system/Params.vue', '/params', NULL, 'Tools', 6, 'f', '系统参数设定', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('3', NULL, '异常行为分析配置', 'config', 'menu', NULL, '/config', '/config/building', 'Operation', 3, 'f', '异常行为分析配置模块', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('4-2', '4', '历史报警记录', 'analysis:history', 'menu', '../views/analysis/History.vue', '/history', NULL, 'Timer', 2, 'f', '历史报警记录', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('4-3', '4', '实时预览', 'analysis:realtime', 'menu', '../views/analysis/Realtime.vue', '/realtime', NULL, 'VideoPlay', 3, 'f', '实时预览', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('4-4', '4', '批量规则定义', 'analysis:batch-rules', 'menu', '../views/analysis/BatchRules.vue', '/batch-rules', NULL, 'Files', 4, 'f', '批量规则定义', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2', NULL, '系统配置', 'system', 'menu', NULL, '/system', '/system/area', 'Setting', 4, 'f', '系统配置模块', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('4', NULL, '异常行为分析管理', 'analysis', 'menu', NULL, '/analysis', '/analysis/rules', 'Warning', 1, 'f', '异常行为分析管理模块', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('4-1', '4', '异常行为检测规则设定', 'analysis:rules', 'menu', '../views/analysis/Rules.vue', '/rules', NULL, 'Document', 2, 'f', '异常行为检测规则设定', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-7', '2', '算法字典维护', 'system:algorithm', 'menu', 'AlgorithmDictionary', '/algorithm', NULL, 'Monitor', 7, 'f', '算法字典维护', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-5', '2', '菜单管理', 'system:permission', 'menu', 'Permission', '/permission', NULL, 'Key', 5, 'f', '权限管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-4', '2', '角色管理', 'system:role', 'menu', 'RoleManagement', '/role', NULL, 'UserFilled', 4, 'f', '角色管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('1', NULL, '首页', 'dashboard', 'menu', 'Dashboard', '/dashboard', NULL, 'HomeFilled', 1, 'f', '系统首页', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-1', '2', '行政区划管理', 'system:area', 'menu', 'AreaManagement', '/area', NULL, 'Location', 1, 'f', '行政区划管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-3', '2', '用户管理', 'system:user', 'menu', 'UserManagement', '/user', NULL, 'User', 3, 'f', '用户管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('3-1', '3', '楼宇信息管理', 'config:building', 'menu', 'BuildingManagement', '/building', NULL, 'OfficeBuilding', 1, 'f', '楼宇信息管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('3-2', '3', '位置信息管理', 'config:location', 'menu', 'LocationManagement', '/location', NULL, 'MapLocation', 2, 'f', '位置信息管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('3-3', '3', '摄像头信息管理', 'config:camera', 'menu', 'CameraManagement', '/camera', NULL, 'VideoCamera', 3, 'f', '摄像头信息管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-2', '2', '组织机构管理', 'system:org', 'menu', 'OfficeManagement', '/office_management', NULL, 'Ship', 2, 'f', '组织机构管理', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');
INSERT INTO "sys_permission" ("permission_id", "parent_id", "permission_name", "permission_code", "permission_type", "component_path", "path", "redirect", "icon", "sort_order", "is_hidden", "permission_description", "is_active", "created_at", "updated_at") VALUES ('2-8', '2', '房间用途字典维护', 'system:room-usage', 'menu', 'RoomPurposeType', '/room-usage', NULL, 'House', 8, 'f', '房间用途字典维护', 't', '2024-12-11 14:39:25.059269+08', '2024-12-11 14:39:25.059269+08');


-- 为超级管理员角色分配所有权限  
INSERT INTO sys_role_permission (role_id, permission_id) SELECT 'ROLE_SUPER_ADMIN', permission_id FROM sys_permission;  

