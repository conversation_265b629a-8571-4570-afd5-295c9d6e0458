{"db_name": "PostgreSQL", "query": "\n            SELECT \n                office_id, office_name, office_address, area_code,\n                is_active as \"is_active!\", created_at, updated_at\n            FROM office_organization\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "office_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "office_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "office_address", "type_info": "Text"}, {"ordinal": 3, "name": "area_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": []}, "nullable": [false, false, true, true, true, true, true]}, "hash": "e52a0644438743a7af3fd8e8975882714dd904e56bb3d25223f72aa148015d61"}