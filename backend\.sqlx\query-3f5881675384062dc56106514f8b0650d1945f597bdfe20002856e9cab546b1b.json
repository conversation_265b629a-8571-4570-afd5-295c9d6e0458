{"db_name": "PostgreSQL", "query": "\n            INSERT INTO building (building_id, building_name, office_id, floors, is_active, created_at, updated_at)\n            VALUES ($1, $2, $3, $4, $5, $6, $7)\n            RETURNING building_id, building_name, office_id, floors, is_active as \"is_active!\", created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "building_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "building_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "office_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "floors", "type_info": "Int4"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int4", "Bool", "Timestamptz", "Timestamptz"]}, "nullable": [false, false, true, false, true, true, true]}, "hash": "3f5881675384062dc56106514f8b0650d1945f597bdfe20002856e9cab546b1b"}