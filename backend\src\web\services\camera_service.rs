//src/web/services/camera_service.rs
use anyhow::{Context, Result};
use log::info;
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::camera::{Camera, CameraCreateRequest, CameraQueryRequest, CameraUpdateRequest, PageResponse};

#[derive(<PERSON><PERSON>, Debug)]
pub struct CameraService {
    pool: Arc<Pool<Postgres>>,
}

impl CameraService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    pub async fn create(&self, req: CameraCreateRequest) -> Result<()> {
        let id = Uuid::new_v4().simple().to_string();
        
        info!("Creating camera with id: {}", id);

        sqlx::query!(
            r#"
            INSERT INTO camera (
                camera_id, camera_name, camera_description, camera_ip_address,
                camera_username, camera_password, camera_rtsp_stream_url,
                location_id, status, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#,
            id,
            req.camera_name,
            req.camera_description,
            req.camera_ip_address,
            req.camera_username,
            req.camera_password,
            req.camera_rtsp_stream_url,
            req.location_id,
            req.status.unwrap_or_else(|| "active".to_string()),
            true,
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create camera")?;

        info!("Successfully created camera with id: {}", id);
        Ok(())
    }

    pub async fn update(&self, req: CameraUpdateRequest) -> Result<()> {
        info!("Updating camera with id: {}", req.camera_id);

        let mut query = sqlx::QueryBuilder::new("UPDATE camera SET updated_at = now()");

        if let Some(name) = req.camera_name {
            query.push(", camera_name = ");
            query.push_bind(name);
        }

        if let Some(desc) = req.camera_description {
            query.push(", camera_description = ");
            query.push_bind(desc);
        }

        if let Some(ip) = req.camera_ip_address {
            query.push(", camera_ip_address = ");
            query.push_bind(ip);
        }

        if let Some(username) = req.camera_username {
            query.push(", camera_username = ");
            query.push_bind(username);
        }

        if let Some(password) = req.camera_password {
            query.push(", camera_password = ");
            query.push_bind(password);
        }

        if let Some(url) = req.camera_rtsp_stream_url {
            query.push(", camera_rtsp_stream_url = ");
            query.push_bind(url);
        }

        if let Some(location_id) = req.location_id {
            query.push(", location_id = ");
            query.push_bind(location_id);
        }

        if let Some(status) = req.status {
            query.push(", status = ");
            query.push_bind(status);
        }

        if let Some(is_active) = req.is_active {
            query.push(", is_active = ");
            query.push_bind(is_active);
        }

        query.push(" WHERE camera_id = ");
        query.push_bind(req.camera_id);

        query.build()
            .execute(self.pool.as_ref())
            .await
            .context("Failed to update camera")?;

        info!("Successfully updated camera");
        Ok(())
    }

    pub async fn delete(&self, id: &str) -> Result<()> {
        info!("Deleting camera with id: {}", id);

        sqlx::query!("DELETE FROM camera WHERE camera_id = $1", id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete camera")?;

        info!("Successfully deleted camera with id: {}", id);
        Ok(())
    }

    pub async fn get_page(&self, req: CameraQueryRequest) -> Result<PageResponse<Camera>> {
        let page = req.page.unwrap_or(1);
        let page_size = req.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        let mut query = String::from("SELECT COUNT(*) FROM camera WHERE 1=1");
        let mut query_sql = String::from("SELECT * FROM camera WHERE 1=1");

        // 构建查询条件
        let mut conditions = vec![];
        let mut params: Vec<String> = vec![];

        if let Some(location_id) = req.location_id {
            if !location_id.is_empty() {
                conditions.push("location_id = $");
                params.push(location_id);
            }
        }

        if let Some(status) = req.status {
            if status.is_empty() {
                conditions.push("status = 'active'");
            } else {
                conditions.push("status = $");
                params.push(status);
            }
        }

        if let Some(search_text) = req.search_text {
            if !search_text.is_empty() {
                conditions.push("(camera_name ILIKE $ OR camera_description ILIKE $)");
                params.push(format!("%{}%", search_text));
                params.push(format!("%{}%", search_text));
            }
        }

        if let Some(camera_name) = req.camera_name {
            if !camera_name.is_empty() {
                conditions.push("camera_name ILIKE $");
                params.push(format!("%{}%", camera_name));
            }
        }

        // 添加条件到查询语句
        for (i, condition) in conditions.iter().enumerate() {
            let condition = condition.replace("$", &format!("${}", i + 1));
            query.push_str(" AND ");
            query.push_str(&condition);
            query_sql.push_str(" AND ");
            query_sql.push_str(&condition);
        }

        query_sql.push_str(" ORDER BY created_at DESC LIMIT $");
        query_sql.push_str(&(params.len() + 1).to_string());
        query_sql.push_str(" OFFSET $");
        query_sql.push_str(&(params.len() + 2).to_string());

        // 执行计数查询
        let mut query = sqlx::query_scalar(&query);
        for param in &params {
            query = query.bind(param);
        }
        let total: i64 = query
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        // 执行分页查询
        let mut query = sqlx::query_as::<_, Camera>(&query_sql);
        for param in &params {
            query = query.bind(param);
        }
        query = query.bind(page_size as i64).bind(offset as i64);

        let items = query
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to get cameras")?;

        Ok(PageResponse { total, items })
    }

    // 根据主键获取摄像头信息
    pub async fn get_camera_by_id(&self, camera_id: &str) -> Result<Camera> {
        let camera = sqlx::query_as!(
            Camera,
            r#"
            SELECT *
            FROM camera 
            WHERE camera_id = $1
            "#,
            camera_id
        )
        .fetch_one(self.pool.as_ref())
        .await
        .context("获取摄像头失败")?;

        Ok(camera)
    }

    pub async fn list(&self, location_id: &str) -> Result<Vec<Camera>> {
        let cameras = sqlx::query_as::<_, Camera>(
            "SELECT * FROM camera WHERE is_active = true and status = 'active' and location_id = $1"
        )
        .bind(location_id)
        .fetch_all(self.pool.as_ref())
        .await
        .context("获取摄像头列表失败")?;
        Ok(cameras)
    }

}
