// src/router/index.js
import { createRouter, createWebHistory } from "vue-router";
import Layout from "../layouts/Layout.vue";
import Login from "../views/Login.vue";
import { generateRoutes } from "../utils/route-generator";


// 基础路由
export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: Login,
    meta: { requiresAuth: false },
  },
  {
    path: "/",
    name: "Layout",
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("../views/Dashboard.vue"),
        meta: { title: "首页" },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/NotFound.vue"),
    meta: { requiresAuth: false },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
});


// 修改动态路由添加方法
export function addDynamicRoutes(menus) {
  try {
    // 先删除所有动态路由
    router.getRoutes().forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });

    // 生成新的动态路由
    const dynamicRoutes = generateRoutes(menus);

    // 添加到主布局路由下
    dynamicRoutes.forEach((route) => {
      router.addRoute("Layout", {
        ...route,
        path: route.path.startsWith("/") ? route.path.slice(1) : route.path, // 确保路径格式正确
      });
    });

    return true;
  } catch (error) {
    console.error("添加动态路由失败:", error);
    return false;
  }
}

router.beforeEach((to, from, next) => {
  const token = localStorage.getItem("token");

  // 判断该路由是否需要登录权限
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 需要登录权限但没有token
    if (!token) {
      next({
        path: "/login",
        // 保存要跳转的路由，登录成功后跳转到该路由
        query: { redirect: to.fullPath },
      });
    } else {
      next();
    }
  } else {
    // 不需要登录权限的路由
    // 如果已登录且要跳转登录页，重定向到首页
    if (token && to.path === "/login") {
      next({ path: "/" });
    } else {
      next();
    }
  }
});

export default router;
