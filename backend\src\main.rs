//main.rs
//#![allow(warnings)]

use anyhow::{Context, Result};
use log::{error, info};
use rtsp::frame_consumer_grpc;
use std::sync::atomic::AtomicUsize;
use std::sync::Arc;
use tokio::sync::Semaphore;
use tokio::time::Duration;

use axum::middleware;
use axum::routing::Router;
use tokio::signal;
use tower_http::cors::CorsLayer;
use tower_http::cors::Any;  
use tower_http::trace::{TraceLayer, DefaultMakeSpan};

use crate::database::database::DatabaseConfig;
use crate::model::frame_queue::FrameQueue;
use crate::model::rtsp_source_manager::RtspManager;
use crate::onnx::ort_yolo_detector::OrtYoloDetector;
use crate::onnx::tract_yolo_detector::TractYoloDetector;
use crate::onnx::yolo_detector::ModelConfig;
use crate::onnx::yolo_detector::ObjectDetector;
use crate::rtsp::frame_consumer_disk::FrameConsumer;
use crate::rtsp::monitor::Monitor;
use crate::web::middleware::jwt_auth;
use crate::web::model::appstate::AppState;
use crate::web::services::service_manager::ServiceManager;
use crate::web::services::sys_user_service::SysUserService;

mod apiclient;
mod config;
mod database;
mod model;
mod onnx;
mod proto;
mod rtsp;
mod utils;
mod web;
mod webrtc;
mod mse;
#[cfg(not(target_env = "msvc"))]
use jemallocator::Jemalloc;

#[cfg(not(target_env = "msvc"))]
#[global_allocator]
static GLOBAL: Jemalloc = Jemalloc;

// 初始化FFmpeg
fn init_ffmpeg() -> Result<()> {
    ffmpeg_next::init().context("初始化ffmpeg失败!")?;
    Ok(())
}
// 日志初始化函数
fn init_logger() -> Result<()> {
    //自动创建logs目录
    let current_dir = std::env::current_dir()?;
    let logs_dir = current_dir.join("logs");
    if !logs_dir.exists() {
        std::fs::create_dir(&logs_dir)?;
    }
    log4rs::init_file("log4rs.yaml", Default::default()).context("初始化log4rs日志框架失败!")?;
    Ok(())
}

async fn start_web_server(state: Arc<AppState>, config: &config::Config) {
    // 配置跨域
    let cors = CorsLayer::new()
    // 允许所有源
    .allow_origin(Any)
    // 允许所有方法
    .allow_methods(Any)
    // 允许所有请求头
    .allow_headers(Any)
    // 允许所有响应头
    .expose_headers(Any)
    .max_age(Duration::from_secs(3600));    

    let app = Router::new()
        .merge(web::api::register_all_routes())
        .with_state(state)
        .layer(
            TraceLayer::new_for_http()
                .make_span_with(DefaultMakeSpan::default().include_headers(true))
        )
        .layer(cors)
        .layer(middleware::from_fn(jwt_auth::auth_middleware));

    let web_server_addr = format!("{}:{}", config.web.host, config.web.port);

    // 使用 tokio::net::TcpListener 创建监听器
    let listener = tokio::net::TcpListener::bind(web_server_addr)
        .await
        .unwrap();
    println!("Server starting on {}", listener.local_addr().unwrap());

    // 使用 axum::serve 启动服务器
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await
        .unwrap();
}

async fn shutdown_signal() {
    tokio::signal::ctrl_c()
        .await
        .expect("Failed to install CTRL+C signal handler");
    println!("Shutdown signal received");
}

#[tokio::main(flavor = "multi_thread", worker_threads = 8)]
async fn main() -> Result<()> {
    // 初始化日志
    init_logger()?;
    info!("Application starting...");

    init_ffmpeg()?;
    info!("FFmpeg initialized successfully");

    let config = Arc::new(config::Config::new()?);

    // 确保images目录存在
    let current_dir = std::env::current_dir()?;
    let images_dir = current_dir.join("images");
    if !images_dir.exists() {
        std::fs::create_dir(&images_dir)?;
    }

    let images_dir = Arc::new(images_dir.to_string_lossy().to_string());

    // 设置rayon线程池
    rayon::ThreadPoolBuilder::new()
        .num_threads(num_cpus::get())
        .build_global()?;

    // let yolo_session = init_yolo().await?;

    // 数据库配置
    let db_config = DatabaseConfig::new(config.database.url.clone(), config.database.max_conn);
    db_config.initialize().await?;

    let database = Arc::new(db_config);
    let service_manager = Arc::new(ServiceManager::new(&Arc::clone(&database)));

    init_test_data(&Arc::clone(&database)).await?;

    let rtsp_manager = Arc::new(
        RtspManager::new(
            Duration::from_secs(300), // 5分钟更新一次
            Arc::clone(&config),
        )
        .await?,
    );

    let frame_queue = Arc::new(FrameQueue::new(config.rtsp.queue_size)); // 初始化保存截图结果的内存队列
    let current_index = Arc::new(AtomicUsize::new(0));
    let semaphore = Arc::new(Semaphore::new(config.rtsp.cap_thread_count));
    let worker_id_counter = Arc::new(AtomicUsize::new(0));
    let total_frame_counter = Arc::new(AtomicUsize::new(0));

    let yolo_detector_config = ModelConfig {
        config: Arc::clone(&config),
        confidence_threshold: config.rtsp.yolo_confidence_threshold,
        iou_threshold: config.rtsp.yolo_iou_threshold,
        input_height: 640,
        input_width: 640,
    };

    let yolo_detector: Arc<dyn ObjectDetector + Send + Sync> =
        match config.rtsp.yolo_detector.as_str() {
            "ort" => Arc::new(OrtYoloDetector::new(yolo_detector_config).await.unwrap()),
            "tract" => Arc::new(TractYoloDetector::new(yolo_detector_config).await.unwrap()),
            _ => {
                error!("Unsupported yolo detector: {}", config.rtsp.yolo_detector);
                return Err(anyhow::anyhow!(
                    "Unsupported yolo detector: {}",
                    config.rtsp.yolo_detector
                ));
            }
        };

    // 创建监控器和工作器
    let monitor = Monitor::new(
        Arc::clone(&frame_queue),
        Arc::clone(&rtsp_manager),
        Arc::clone(&current_index),
        Arc::clone(&semaphore),
        Arc::clone(&total_frame_counter),
    );

    let frame_worker = rtsp::ffmpeg_capture_worker::FrameCaptureWorker::new(
        Arc::clone(&frame_queue),
        Arc::clone(&rtsp_manager),
        Arc::clone(&config),
        Arc::clone(&semaphore),
        Arc::clone(&current_index),
        Arc::clone(&worker_id_counter),
        Arc::clone(&total_frame_counter),
        Arc::clone(&images_dir),
        Arc::clone(&yolo_detector),
    );

    // 创建 web 服务状态
    let app_state = Arc::new(AppState::new(
        &Arc::clone(&config),
        &Arc::clone(&database),
        &Arc::clone(&service_manager),
        &Arc::clone(&frame_queue),
    ));

    let grpc_client = frame_consumer_grpc::GrpcFrameConsumer::new(
        Arc::clone(&config),
        Arc::clone(&frame_queue),
        &config.grpcConfig.endpoint,
    );

    // 启动所有服务
    tokio::select! {
        _ = start_web_server(app_state,&config) => {},
        _ = async {
            _=tokio::join!(
                async {
                    match config.rtsp.consumer_target.as_str() {
                        "grpc" => {
                            let _ = grpc_client.start().await;
                        },
                        "disk" => {
                            let _ = FrameConsumer::new(
                                Arc::clone(&frame_queue),
                                config.rtsp.consumer_thread_count,
                                Arc::clone(&config),
                                Arc::clone(&images_dir),
                            ).start().await;
                        },
                        _ => {
                            error!("Unsupported consumer target: {}", config.rtsp.consumer_target);
                        }
                    }
                },
                monitor.spawn(),
                frame_worker.spawn(),
            );
        } => {},
        _ = signal::ctrl_c() => {
            println!("Shutting down gracefully...");
            grpc_client.shutdown().await;
        }
    }

    Ok(())
}

async fn init_test_data(database: &Arc<DatabaseConfig>) -> Result<()> {
    let pool = database.get_pool();
    let sys_user_service = SysUserService::new(pool, String::from_utf8(model::JWT_SECRET.to_vec()).unwrap());
    sys_user_service.init_test_data().await?;
    Ok(())
}