//src/web/api/office_organization_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete, get, post},
    Json, Router,
};
use std::sync::Arc;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    office_organization::{OfficeCreateRequest, OfficeOrganization, OfficeUpdateRequest},
};

pub fn register_office_organization_api() -> Router<Arc<AppState>> {
    Router::new().nest("/office_organization", office_organization_routes())
}

fn office_organization_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create_office))
        .route("/update", post(update_office))
        .route("/delete/:id", delete(delete_office))
        .route("/list", get(get_all_offices))
        .route("/list/:area_code", get(get_offices_by_area))
}

async fn create_office(
    State(state): State<Arc<AppState>>,
    Json(req): Json<OfficeCreateRequest>,
) -> <PERSON><PERSON><ApiResponse<()>> {
    match state
        .service_manager
        .office_organization_service
        .create_office(req)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("创建失败: {:#}", e))),
    }
}

async fn update_office(
    State(state): State<Arc<AppState>>,
    Json(req): Json<OfficeUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .office_organization_service
        .update_office(req)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("更新失败: {:#}", e))),
    }
}

async fn delete_office(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .office_organization_service
        .delete_office(&id)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除失败: {:#}", e))),
    }
}

async fn get_all_offices(
    State(state): State<Arc<AppState>>,
) -> Json<ApiResponse<Vec<OfficeOrganization>>> {
    match state
        .service_manager
        .office_organization_service
        .get_all_offices()
        .await
    {
        Ok(offices) => Json(ApiResponse::success(Some(offices))),
        Err(e) => Json(ApiResponse::error(format!("获取失败: {:#}", e))),
    }
}

async fn get_offices_by_area(
    State(state): State<Arc<AppState>>,
    Path(area_code): Path<String>,
) -> Json<ApiResponse<Vec<OfficeOrganization>>> {
    match state
        .service_manager
        .office_organization_service
        .get_offices_by_area(&area_code)
        .await
    {
        Ok(offices) => Json(ApiResponse::success(Some(offices))),
        Err(e) => Json(ApiResponse::error(format!("获取失败: {:#}", e))),
    }
}