{"db_name": "PostgreSQL", "query": "  \n                WITH RECURSIVE menu_tree AS (  \n                    -- 基础查询：获取用户的顶级菜单  \n                    SELECT DISTINCT p.*  \n                    FROM sys_permission p  \n                    JOIN sys_role_permission rp ON p.permission_id = rp.permission_id  \n                    JOIN sys_user_role ur ON rp.role_id = ur.role_id  \n                    WHERE ur.user_id = $1   \n                    AND p.parent_id IS NULL  \n                    AND p.permission_type = 'menu'  \n        \n                    UNION ALL  \n        \n                    -- 递归查询：获取子菜单  \n                    SELECT p.*  \n                    FROM sys_permission p  \n                    JOIN sys_role_permission rp ON p.permission_id = rp.permission_id  \n                    JOIN sys_user_role ur ON rp.role_id = ur.role_id  \n                    JOIN menu_tree mt ON p.parent_id = mt.permission_id  \n                    WHERE ur.user_id = $1  \n                    AND p.permission_type = 'menu'  \n                )  \n                SELECT   \n                    permission_id,  \n                    parent_id,  \n                    permission_name,  \n                    permission_code,  \n                    permission_type,  \n                    component_path,  \n                    path,  \n                    redirect,  \n                    icon,  \n                    sort_order,  \n                    permission_description  \n                FROM menu_tree  \n                ORDER BY sort_order  \n                ", "describe": {"columns": [{"ordinal": 0, "name": "permission_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "parent_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "permission_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "permission_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "permission_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "component_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 7, "name": "redirect", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "icon", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "sort_order", "type_info": "Int4"}, {"ordinal": 10, "name": "permission_description", "type_info": "Text"}], "parameters": {"Left": ["Text"]}, "nullable": [null, null, null, null, null, null, null, null, null, null, null]}, "hash": "fdf64b4896fa0d8f2935fb5ee1ace51d55ff6bdd69b65b5633b38776827e5ce4"}