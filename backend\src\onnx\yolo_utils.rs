use crate::onnx::yolo_detector::DetectionBox;


pub const YOLOV8_CLASS_LABELS: [&str; 80] = [
    "person",
    "bicycle",
    "car",
    "motorcycle",
    "airplane",
    "bus",
    "train",
    "truck",
    "boat",
    "traffic light",
    "fire hydrant",
    "stop sign",
    "parking meter",
    "bench",
    "bird",
    "cat",
    "dog",
    "horse",
    "sheep",
    "cow",
    "elephant",
    "bear",
    "zebra",
    "giraffe",
    "backpack",
    "umbrella",
    "handbag",
    "tie",
    "suitcase",
    "frisbee",
    "skis",
    "snowboard",
    "sports ball",
    "kite",
    "baseball bat",
    "baseball glove",
    "skateboard",
    "surfboard",
    "tennis racket",
    "bottle",
    "wine glass",
    "cup",
    "fork",
    "knife",
    "spoon",
    "bowl",
    "banana",
    "apple",
    "sandwich",
    "orange",
    "broccoli",
    "carrot",
    "hot dog",
    "pizza",
    "donut",
    "cake",
    "chair",
    "couch",
    "potted plant",
    "bed",
    "dining table",
    "toilet",
    "tv",
    "laptop",
    "mouse",
    "remote",
    "keyboard",
    "cell phone",
    "microwave",
    "oven",
    "toaster",
    "sink",
    "refrigerator",
    "book",
    "clock",
    "vase",
    "scissors",
    "teddy bear",
    "hair drier",
    "toothbrush",
];



// 计算IOU（交并比）
pub fn calculate_iou(box1: &DetectionBox, box2: &DetectionBox) -> f32 {
    let x1 = box1.bbox[0].max(box2.bbox[0]);
    let y1 = box1.bbox[1].max(box2.bbox[1]);
    let x2 = box1.bbox[2].min(box2.bbox[2]);
    let y2 = box1.bbox[3].min(box2.bbox[3]);

    let intersection = (x2 - x1).max(0.0) * (y2 - y1).max(0.0);
    let area1 = (box1.bbox[2] - box1.bbox[0]) * (box1.bbox[3] - box1.bbox[1]);
    let area2 = (box2.bbox[2] - box2.bbox[0]) * (box2.bbox[3] - box2.bbox[1]);
    let union = area1 + area2 - intersection;

    intersection / union
}

// // 计算两个边界框的交并比
// fn calculate_iou(bbox1: &[f32; 4], bbox2: &[f32; 4]) -> f32 {
//     let x1 = bbox1[0].max(bbox2[0]);
//     let y1 = bbox1[1].max(bbox2[1]);
//     let x2 = bbox1[2].min(bbox2[2]);
//     let y2 = bbox1[3].min(bbox2[3]);

//     let intersection = (x2 - x1).max(0.0) * (y2 - y1).max(0.0);
//     let area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1]);
//     let area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1]);

//     intersection / (area1 + area2 - intersection)
// }