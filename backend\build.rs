use std::fs;
use std::path::Path;
use std::env;


fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 获取输出目录（debug 或 release）
    let out_dir = env::var("OUT_DIR").unwrap();
    let profile = env::var("PROFILE").unwrap();
    
    // 构建目标目录路径
    let target_dir = Path::new(&out_dir)
        .ancestors()
        .nth(3)
        .unwrap()
        .to_path_buf();
    
    // 源文件目录
    let source_dir = Path::new("onnx");
    let dest_dir = target_dir.join("onnx");

    // 如果目标目录不存在，创建它
    if !dest_dir.exists() {
        fs::create_dir_all(&dest_dir).unwrap();
    }

    // 复制文件
    let source_file = source_dir.join("yolov8m.onnx");
    let dest_file = dest_dir.join("yolov8m.onnx");
    
    // 只在文件不存在或源文件更新时复制
    if !dest_file.exists() || 
       fs::metadata(&source_file).unwrap().modified().unwrap() > 
       fs::metadata(&dest_file).unwrap_or_else(|_| fs::metadata(&source_file).unwrap()).modified().unwrap() {
        fs::copy(&source_file, &dest_file).unwrap();
        println!("cargo:rerun-if-changed=onnx/yolov8m.onnx");
    }    

    println!("cargo:rerun-if-changed=proto/shinow_image_analysis.proto");
    
    // 添加更详细的错误处理
    match tonic_build::compile_protos("proto/shinow_image_analysis.proto") {
        Ok(_) => {
            println!("Successfully compiled protos");
            return Ok(())
        },
        Err(e) => {
            println!("Failed to compile protos: {:?}", e);
            return Err(e.into())
        }
    }

    Ok(())
}
