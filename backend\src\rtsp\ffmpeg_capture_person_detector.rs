// rtsp/ffmpeg_capture_person_detector.rs
// 用于从RTSP流中捕获帧并进行人员检测的模块
use anyhow::Result;
use ffmpeg_next as ffmpeg;
use image::DynamicImage;
use log::*;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use bytes::Bytes;
use crate::model::frame_queue::FrameQueue;
use crate::model::rtsp_model::{CameraFrame, CameraSource, RoomFrame, RtspSource};
use crate::rtsp::ffmpeg_capture_trait::FrameCaptureTriat;
use crate::onnx::yolo_detector::ObjectDetector;
pub struct FrameCaptureAndDetector {
    worker_id: usize,
    room_source: RtspSource,
    detector: Arc<dyn ObjectDetector + Send + Sync>,
    frame_queue: Arc<FrameQueue>,
}

impl FrameCaptureAndDetector {
    pub fn new(
        worker_id: usize,
        room_source: RtspSource,
        detector: Arc<dyn ObjectDetector + Send + Sync>,
        frame_queue: Arc<FrameQueue>,
    ) -> Result<Self> {
        Ok(FrameCaptureAndDetector {
            worker_id,
            room_source,
            detector,
            frame_queue,
        })
    }

    /// 只负责截图的函数
    async fn capture_camera_frame(
        worker_id: usize,
        camera: CameraSource,
        timeout: Duration,
        start_time: Instant,
    ) -> Result<Option<CameraFrame>> {
        if start_time.elapsed() > timeout {
            return Err(anyhow::anyhow!("Operation timed out"));
        }

        let frame_result = tokio::task::spawn_blocking(move || -> Result<Option<CameraFrame>> {
            let mut options = ffmpeg::Dictionary::new();
            options.set("rtsp_transport", "tcp");
            options.set("reconnect", "1");
            options.set("reconnect_at_eof", "1");
            options.set("reconnect_streamed", "1");
            options.set("reconnect_on_network_error", "1");
            options.set("stimeout", "5000000");
            options.set("buffer_size", "2048000");
            options.set("max_delay", "5000000");
            options.set("analyzeduration", "10000000");
            options.set("probesize", "100000000");

            // 打开输入流
            let mut ictx = match ffmpeg::format::input_with_dictionary(&camera.url, options) {
                Ok(ctx) => ctx,
                Err(e) => {
                    warn!(
                        "Worker {} failed to open stream for camera {}: {}",
                        worker_id, camera.camera_id, e
                    );
                    return Ok(None);
                }
            };

            // 找到视频流
            let input = ictx
                .streams()
                .best(ffmpeg::media::Type::Video)
                .ok_or_else(|| anyhow::anyhow!("No video stream found"))?;

            let video_stream_index = input.index();

            // 创建解码器
            let context_decoder =
                ffmpeg::codec::context::Context::from_parameters(input.parameters())?;
            let mut decoder = context_decoder.decoder().video()?;

            // 检查像素格式
            if decoder.format() == ffmpeg::format::Pixel::None {
                return Err(anyhow::anyhow!("Invalid pixel format"));
            }

            // 验证视频尺寸
            let width = decoder.width();
            let height = decoder.height();
            if width == 0 || height == 0 {
                return Err(anyhow::anyhow!(
                    "Invalid video dimensions: {}x{}",
                    width,
                    height
                ));
            }

            debug!(
                "Worker {} video dimensions: {}x{}",
                worker_id, width, height
            );
            debug!("worker {} 创建解码器成功", worker_id);

            // 创建帧和缩放器
            let mut frame = ffmpeg::frame::Video::empty();
            let mut rgb_frame = ffmpeg::frame::Video::empty();

            let mut scaler = ffmpeg::software::scaling::Context::get(
                decoder.format(),
                decoder.width(),
                decoder.height(),
                ffmpeg::format::Pixel::RGB24,
                decoder.width(),
                decoder.height(),
                ffmpeg::software::scaling::Flags::BILINEAR
                    | ffmpeg::software::scaling::Flags::FULL_CHR_H_INT,
            )?;

            // 记录rtsp连接时间
            let rtsp_connection_time = start_time.elapsed().as_millis() as i64;
            let mut rtsp_capture_frame_time: i64 = 0; // 记录rtsp截取帧时间
            let mut rtsp_decode_frame_time: i64 = 0; // 记录rtsp解码帧时间

            // 处理数据包
            let mut frame_obtained = false;
            'packet_loop: for (stream, packet) in ictx.packets() {
                if stream.index() == video_stream_index && packet.is_key() {
                    if start_time.elapsed() > timeout {
                        return Err(anyhow::anyhow!("Timeout while processing video packets"));
                    }

                    rtsp_capture_frame_time = start_time.elapsed().as_millis() as i64; // 记录rtsp截取帧时间

                    match decoder.send_packet(&packet) {
                        Ok(_) => {
                            while decoder.receive_frame(&mut frame).is_ok() {
                                scaler.run(&frame, &mut rgb_frame)?;
                                frame_obtained = true;
                                rtsp_decode_frame_time = start_time.elapsed().as_millis() as i64; // 记录rtsp解码帧时间
                                break 'packet_loop;
                            }
                        }
                        Err(e) => {
                            warn!(
                                "Worker {} failed to process packet for camera {}: {}",
                                worker_id, camera.camera_id, e
                            );
                            continue;
                        }
                    }
                }
            }

            if !frame_obtained {
                warn!(
                    "Worker {} no valid frame obtained for camera {}",
                    worker_id, camera.camera_id
                );
                return Ok(None);
            }

            debug!(
                "Worker {} successfully decoded frame for camera {}",
                worker_id, camera.camera_id
            );

            Ok(Some(CameraFrame {
                camera_id: camera.camera_id.clone(),
                camera_name: camera.camera_name.clone(),
                camera_type: camera.camera_type.clone(),
                timestamp: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs() as i64,
                width: frame.width() as i32,
                height: frame.height() as i32,
                data: Bytes::from(rgb_frame.data(0).to_vec()),
                debug_rtsp_connection_time: rtsp_connection_time,
                debug_rtsp_capture_frame_time: rtsp_capture_frame_time,
                debug_rtsp_decode_frame_time: rtsp_decode_frame_time,
                debug_rtsp_send_frame_time: 0,
            }))
        })
        .await?;

        debug!("Worker {} 截图完成", worker_id);

        if let Some(frame) = frame_result? {
            Ok(Some(frame))
        } else {
            Ok(None)
        }

    }


    /// 专门负责检测的函数
    async fn detect_frame(
        detector: Arc<dyn ObjectDetector + Send + Sync>,
        frame: CameraFrame,
    ) -> Result<(CameraFrame, bool)> {
        let start_time = Instant::now();
        let image = image::RgbImage::from_raw(frame.width as u32, frame.height as u32, frame.data.as_ref().to_vec())
            .ok_or_else(|| anyhow::anyhow!("Failed to create image from frame data"))?;

        let dynamic_image = DynamicImage::ImageRgb8(image);

        let result = detector.detect_from_image(&dynamic_image).await?;

        debug!("检测完成，耗时: {:?}, 检测结果: {:?}", start_time.elapsed(), result);

        Ok((frame, !result.boxes.is_empty()))
    }

    pub async fn capture_and_detect(
        worker_id: usize,
        room_source: &RtspSource,
        detector: &Arc<dyn ObjectDetector + Send + Sync>,
        frame_queue: &Arc<FrameQueue>,
    ) -> Result<()> {
        let start_time = Instant::now();
        let timeout = Duration::from_secs(20);
        let camera_timeout = Duration::from_secs(10);

        // 1. 并行执行所有摄像头的截图
        let capture_futures: Vec<_> = room_source.cameras.iter().map(|camera| {
            let camera = camera.clone();
            Self::capture_camera_frame(
                worker_id,
                camera,
                camera_timeout,
                start_time,
            )
        }).collect();

        // 2. 等待所有截图完成
        let captured_frames = tokio::time::timeout(
            timeout,
            futures::future::join_all(capture_futures)
        ).await?;

        //打印一下全部截图成功的耗时情况
        debug!("Worker {} 全部截图成功，耗时: {:?}", worker_id, start_time.elapsed());

        // 3. 对成功截取的帧进行检测
        let mut detection_futures = Vec::new();
        for frame_result in captured_frames {
            if let Ok(Some(frame)) = frame_result {
                let detector = Arc::clone(detector);
                let detection_future = Self::detect_frame(detector, frame);
                detection_futures.push(detection_future);
            }
        }

        // 4. 并行执行所有检测
        let detection_results = futures::future::join_all(detection_futures).await;

        //打印一下全部检测完成的耗时情况
        debug!("Worker {} 全部检测完成，耗时: {:?}", worker_id, start_time.elapsed());

        // 5. 处理检测结果
        let mut frames = HashMap::new();
        let mut has_person_detected = false;

        for result in detection_results {
            if let Ok((frame, detected)) = result {
                if detected {
                    has_person_detected = true;
                }
                frames.insert(frame.camera_id.clone(), frame);
            }
        }

        // 6. 如果检测到人，将结果推入队列
        if has_person_detected && !frames.is_empty() {
            let room_frame = RoomFrame {
                room_id: room_source.room_id.clone(),
                room_name: room_source.room_name.clone(),
                frames,
            };


            debug!("Worker {} 检测到人员，将结果推入队列", worker_id);

            if !frame_queue.push(room_frame) {
                warn!("Failed to push frame to queue: queue is full");
            }
        }

        Ok(())
    }    

}

impl FrameCaptureTriat for FrameCaptureAndDetector {
    fn new(
        worker_id: usize,
        room_source: RtspSource,
        frame_queue: Option<Arc<FrameQueue>>,
        detector: Option<Arc<dyn ObjectDetector + Send + Sync>>,
    ) -> Result<Self> {
        let frame_queue = frame_queue.ok_or_else(|| anyhow::anyhow!("frame_queue is required"))?;
        let detector = detector.ok_or_else(|| anyhow::anyhow!("detector is required"))?;

        Ok(FrameCaptureAndDetector {
            worker_id,
            room_source,
            detector,
            frame_queue,
        })
    }

    async fn capture_frames(&self) -> Result<Option<RoomFrame>> {
        Self::capture_and_detect(
            self.worker_id,
            &self.room_source,
            &self.detector,
            &self.frame_queue,
        )
        .await?;
        Ok(None)
    }
}
