//src/web/model/appstate.rs
use std::sync::Arc;
use crate::config::Config;
use crate::database::database::DatabaseConfig;
use crate::model::frame_queue::FrameQueue;
use crate::web::services::service_manager::ServiceManager;


#[derive(Clone, Debug)]
pub struct AppState {
    pub service_manager: Arc<ServiceManager>,
    pub frame_queue: Arc<FrameQueue>,
    pub config: Arc<Config>,
    pub database: Arc<DatabaseConfig>,
}

impl AppState {
    pub fn new(config: &Arc<Config>,database: &Arc<DatabaseConfig>,service_manager: &Arc<ServiceManager>,frame_queue: &Arc<FrameQueue>) -> Self {
        Self {
            service_manager: Arc::clone(service_manager),
            frame_queue: Arc::clone(frame_queue),
            config: Arc::clone(config),
            database: Arc::clone(database),
        }
    }
}