syntax = "proto3";

package shinow.image.analysis;

// 服务定义
service ImageAnalysisService {
    rpc AnalyzeImages (ImageAnalysisRequest) returns (ImageAnalysisResponse) {}
}

// 算法参数配置
message AlgorithmParam {
    string param_key = 1;                    // 算法配置参数key名称，如yuzhi
    string param_value = 2;                  // 算法配置参数配置值,如0.8
}

// 算法信息
message Algorithm {
    string algorithm_id = 1;                 // 算法id
    string algorithm_name = 2;               // 算法名称
    repeated AlgorithmParam parameters = 3;  // 算法参数HashMap
}

// 摄像头图像数据
message CameraImageData {
    string camera_id = 1;                    // 摄像头id
    string camera_name = 2;                  // 摄像头名称
    string camera_type = 3;                  // 摄像头类型,如全景,特写等
    bytes image_binary = 4;                  // 二进制图片数据
    repeated Algorithm algorithms = 5;        // 配置的算法列表
}

// 房间的图像数据及其元信息
message RoomImageData {
    string room_id = 1;                      // 房间id
    string room_name = 2;                    // 房间名称
    repeated CameraImageData cameras = 3;     // 该房间的所有摄像头数据
}

// 检测到的异常行为信息
message AbnormalBehavior {
    repeated int32 boxes = 1;                // [x1, y1, x2, y2] 坐标，表示人员所在区域
    double confidence = 2;                   // 置信度
    string behavior_type = 3;                // 异常行为类型（如 "climb"、"fall" 等）
    string person_id = 4;                    // 人员标识
}

// 单个摄像头的分析结果
message CameraAnalysisResult {
    string camera_id = 1;                    // 摄像头id
    string camera_name = 2;                  // 摄像头名称
    int32 person_count = 3;                  // 该摄像头检测到的总人数
    repeated AbnormalBehavior abnormal_behaviors = 4;  // 检测到的异常行为列表
}

// 单个房间的分析结果
message RoomAnalysisResult {
    string room_id = 1;                      // 房间ID
    string room_name = 2;                    // 房间名称
    repeated CameraAnalysisResult camera_results = 3;  // 该房间所有摄像头的分析结果
}

// 请求消息
message ImageAnalysisRequest {
    repeated RoomImageData rooms = 1;        // 房间图像数据数组
}

// 响应消息
message ImageAnalysisResponse {
    int32 status_code = 1;                   // 状态码
    string message = 2;                      // 状态信息
    repeated RoomAnalysisResult results = 3;  // 分析结果数组，按房间组织
}