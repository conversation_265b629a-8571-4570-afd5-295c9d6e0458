// src/web/services/building_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::building::{Building, BuildingQueryRequest, BuildingUpdateRequest, PageResponse};

#[derive(Clone, Debug)]
pub struct BuildingService {
    pool: Arc<Pool<Postgres>>,
}

impl BuildingService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    // 创建楼宇
    pub async fn create_building(&self, building: Building) -> Result<Building> {
        let building_id = Uuid::new_v4().simple().to_string();
        let now = Utc::now();

        let building = sqlx::query_as!(
            Building,
            r#"
            INSERT INTO building (building_id, building_name, office_id, floors, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING building_id, building_name, office_id, floors, is_active as "is_active!", created_at, updated_at
            "#,
            building_id,
            building.building_name,
            building.office_id,
            building.floors,
            true,
            now,
            now
        )
        .fetch_one(self.pool.as_ref())
        .await
        .context("Failed to create building")?;

        Ok(building)
    }

    // 分页查询楼宇
    pub async fn query_buildings(&self, query: BuildingQueryRequest) -> Result<PageResponse<Building>> {
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = ((page - 1) * page_size) as i64;

        let mut sql = String::from(
            "SELECT building_id, building_name, office_id, floors, is_active, created_at, updated_at 
             FROM building WHERE 1=1"
        );
        
        let mut bind_values = vec![];
        let mut param_count = 1;

        if let Some(office_id) = query.office_id {
            sql.push_str(&format!(" AND office_id = ${}", param_count));
            bind_values.push(office_id);
            param_count += 1;
        }

        if let Some(search) = query.search_text {
            sql.push_str(&format!(" AND building_name ILIKE ${}", param_count));
            bind_values.push(format!("%{}%", search));
            param_count += 1;
        }

        let count_sql = format!("SELECT COUNT(*) FROM ({}) t", sql);
        let mut count_query = sqlx::query_scalar::<_, i64>(&count_sql);
        
        for value in &bind_values {
            count_query = count_query.bind(value);
        }
        
        let total = count_query
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        sql.push_str(&format!(" ORDER BY created_at DESC LIMIT ${} OFFSET ${}", 
            param_count, param_count + 1));
        
        let mut query = sqlx::query_as::<_, Building>(&sql);
        
        for value in bind_values {
            query = query.bind(value);
        }
        
        query = query.bind(page_size as i64).bind(offset);

        let buildings = query
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to fetch buildings")?;

        Ok(PageResponse {
            total,
            items: buildings,
        })
    }

    // 更新楼宇
    pub async fn update_building(&self, req: BuildingUpdateRequest) -> Result<Building> {
        let mut query_builder = sqlx::QueryBuilder::new(
            "UPDATE building SET "
        );
        
        let mut first = true;
        
        if let Some(name) = req.building_name {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("building_name = ");
            query_builder.push_bind(name);
            first = false;
        }

        if let Some(office_id) = req.office_id {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("office_id = ");
            query_builder.push_bind(office_id);
            first = false;
        }

        if let Some(floors) = req.floors {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("floors = ");
            query_builder.push_bind(floors);
            first = false;
        }

        if let Some(is_active) = req.is_active {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("is_active = ");
            query_builder.push_bind(is_active);
            first = false;
        }

        if !first {
            query_builder.push(", ");
        }
        query_builder.push("updated_at = ");
        query_builder.push_bind(Utc::now());

        query_builder.push(" WHERE building_id = ");
        query_builder.push_bind(req.building_id);
        query_builder.push(" RETURNING *");

        let building = query_builder
            .build_query_as::<Building>()
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to update building")?;

        Ok(building)
    }

    // 删除楼宇
    pub async fn delete_building(&self, building_id: &str) -> Result<()> {
        sqlx::query!("DELETE FROM building WHERE building_id = $1", building_id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete building")?;

        Ok(())
    }
}