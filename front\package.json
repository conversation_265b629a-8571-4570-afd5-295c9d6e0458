{"name": "rtspfront", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "@heroicons/vue": "^2.2.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "echarts": "^5.5.1", "element-plus": "^2.9.0", "pinia": "^2.3.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.10.1", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "sass": "^1.82.0", "sass-loader": "^16.0.4", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.1"}}