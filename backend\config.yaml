rtsp:
  cap_thread_count: 0 # 视频流读取tokio job任务数
  consumer_thread_count: 0 # 视频流消费tokio job任务数
  queue_size: 2000 # 视频流队列大小
  consumer_target: "grpc" # 视频流消费目标，可选："disk"，"grpc"
  rtsp_data_source: "simulate" # rtsp 视频流地址来源,可选值: "jcptapi", "tsexcel","simulate"
  cap_time_out: 20 # 截图超时时间，单位秒
  use_cap_sleep: true # 截图是否使用sleep等待
  cap_sleep_time: 100 # 截图等待时间，单位毫秒
  use_consumer_sleep: true # 消费是否使用sleep等待
  consumer_sleep_time: 100 # 消费等待时间，单位毫秒
  use_yolo_detection: false # 是否启用yolo人员检测
  yolo_model_path: "./onnx/yolov8m.onnx" # yolo模型路径
  yolo_iou_threshold: 0.5 # yolo iou阈值
  yolo_confidence_threshold: 0.5 # yolo 置信度阈值
  yolo_detector: "ort" # yolo 检测器类型,可选值: "ort", "tract"
  yolo_device: "cpu" # yolo 设备类型,可选值: "cuda", "cpu"
  
jcpt:
  server_ip: ********* # 服务端ip
  server_port: 8087 # 服务端端口
  app_id: ********** # 应用id

grpcConfig:
  endpoint: "http://************:5000" #grpc 服务地址
  max_retries: 3 #grpc 调用服务重试次数
  batch_size: 100 #grpc 批量发送数据大小
  grpc_call_timeout: 20 #grpc 调用超时时间,单位秒
  grpc_connect_timeout: 5 #grpc 连接超时时间,单位秒
  grpc_keepalive_interval: 60 #grpc 连接保持时间,单位秒
  initial_stream_window_size: 20 #grpc 初始化流窗口大小,单位MB  
  initial_connection_window_size: 20 #grpc 初始化连接窗口大小,单位MB

web:
  host: 0.0.0.0 # 监听地址
  port: 8080 # 监听端口

database:
  url: ***********************************************/ycxw #数据库连接地址
  max_conn: 20 # 数据库连接池最大连接数
