//src/web/services/room_purpose_type_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::room_purpose_type::{
    PageRequest, PageResponse, RoomPurposeType, RoomPurposeTypeCreateRequest,
    RoomPurposeTypeUpdateRequest,
};

#[derive(<PERSON><PERSON>, Debug)]
pub struct RoomPurposeTypeService {
    pool: Arc<Pool<Postgres>>,
}

impl RoomPurposeTypeService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    // 创建房间用途
    pub async fn create(&self, req: RoomPurposeTypeCreateRequest) -> Result<()> {
        let id = Uuid::new_v4().simple().to_string();

        sqlx::query!(
            r#"
            INSERT INTO room_purpose_type (
                room_purpose_type_id, room_purpose_type_name, 
                room_purpose_type_description, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            "#,
            id,
            req.room_purpose_type_name,
            req.room_purpose_type_description,
            req.is_active,
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create room purpose type")?;

        Ok(())
    }

    // 更新房间用途
    pub async fn update(&self, req: RoomPurposeTypeUpdateRequest) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE room_purpose_type
            SET room_purpose_type_name = $1,
                room_purpose_type_description = $2,
                is_active = $3,
                updated_at = $4
            WHERE room_purpose_type_id = $5
            "#,
            req.room_purpose_type_name,
            req.room_purpose_type_description,
            req.is_active,
            Utc::now(),
            req.room_purpose_type_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update room purpose type")?;

        Ok(())
    }

    // 删除房间用途
    pub async fn delete(&self, id: &str) -> Result<()> {
        sqlx::query!(
            "DELETE FROM room_purpose_type WHERE room_purpose_type_id = $1",
            id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to delete room purpose type")?;

        Ok(())
    }

    // 分页获取房间用途列表
    pub async fn get_page(&self, req: PageRequest) -> Result<PageResponse<RoomPurposeType>> {
        let offset = (req.page - 1) * req.page_size;

        let total = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM room_purpose_type"
        )
        .fetch_one(self.pool.as_ref())
        .await
        .context("Failed to get total count")?
        .unwrap_or(0);

        let records = sqlx::query_as!(
            RoomPurposeType,
            r#"
            SELECT 
                room_purpose_type_id, room_purpose_type_name,
                room_purpose_type_description, is_active as "is_active!",
                created_at, updated_at
            FROM room_purpose_type
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            req.page_size,
            offset
        )
        .fetch_all(self.pool.as_ref())
        .await
        .context("Failed to fetch room purpose types")?;

        Ok(PageResponse {
            records,
            total,
            page: req.page,
            page_size: req.page_size,
        })
    }
}