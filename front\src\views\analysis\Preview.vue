<template>
  <div class="flex h-screen">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 p-4 border-r overflow-hidden flex flex-col h-full">
        <!-- 搜索框固定在顶部 -->
      <div class="mb-4 flex-shrink-0">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索节点"
          clearable
          prefix-icon="Search"
        />
      </div>
      <!-- 树区域自适应高度并显示滚动条 -->
      <div class="flex-1 overflow-y-auto">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="flex items-center">
              <el-icon class="mr-1">
                <Location v-if="data.type === 'area'" />
                <OfficeBuilding v-else-if="data.type === 'org'" />
                <House v-else-if="data.type === 'building'" />
                <Box v-else-if="data.type === 'location'" />
                <VideoCameraFilled v-else-if="data.type === 'camera'" />
              </el-icon>
              <span>{{ data.name }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧预览区域 -->
    <div class="flex-1 p-6 bg-gray-100 h-full overflow-hidden">
      <div v-if="selectedCamera" class="h-full flex flex-col">
        <div class="mb-4">
          <h2 class="text-xl font-semibold text-gray-800">
            {{ selectedCamera.name }}
          </h2>
          <p class="text-gray-600 text-sm mt-1">
            {{ selectedCamera.camera_ip_address }}
          </p>
        </div>

        <div class="flex-1 rounded-lg overflow-hidden shadow-lg relative">
          <!-- 预览区域 -->
          <div
            class="absolute inset-0 bg-black flex items-center justify-center"
          >
            <el-icon class="text-gray-600 text-6xl"><VideoCamera /></el-icon>
          </div>

          <!-- 控制栏 -->
          <div
            class="absolute bottom-0 left-0 right-0 bg-black/50 text-white p-4"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <el-button type="primary" circle>
                  <el-icon><VideoPlay /></el-icon>
                </el-button>
                <el-button type="primary" circle>
                  <el-icon><Camera /></el-icon>
                </el-button>
              </div>
              <div class="flex items-center space-x-2">
                <el-tag type="success" size="small">在线</el-tag>
                <span class="text-sm">{{
                  selectedCamera.camera_rtsp_stream_url
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="h-full flex items-center justify-center text-gray-400">
        <el-empty description="请选择摄像头进行预览" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { format } from "date-fns";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const currentNode = ref(null);
const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

const loadNode = async (node, resolve) => {
  try {
    if (node.level === 0) {
      // 加载省级行政区划
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map((item) => ({
        ...item,
        id: item.code,
        type: "area",
        name: item.name,
        leaf: false,
      }));
      resolve(areas);
      return;
    }

    const { data } = node;
    let children = [];

    switch (data.type) {
      case "area":
        // 加载区划下的子区划和机构
        const [areaRes, orgRes] = await Promise.all([
          axios.get("/sys_area_info/lazy_tree_node", {
            params: { parent_code: data.code },
          }),
          axios.get(`/office_organization/list/${data.code}`),
        ]);

        // 处理子区划
        const areas = areaRes.data.data.map((item) => ({
          ...item,
          id: item.code,
          type: "area",
          name: item.name,
          leaf: item.level >= 4,
        }));

        // 处理机构
        const orgs = orgRes.data.data.map((item) => ({
          id: item.office_id,
          type: "org",
          name: item.office_name,
          leaf: false,
          office_id: item.office_id,
          office_name: item.office_name,
          area_code: data.code,
        }));

        children = [...areas, ...orgs];
        break;

      case "org":
        // 加载机构下的楼宇
        const buildingRes = await axios.get("/building/list", {
          params: {
            office_id: data.office_id,
            page_size: 1000,
          },
        });
        children = buildingRes.data.data.items.map((item) => ({
          id: item.building_id,
          type: "building",
          name: item.building_name,
          leaf: false,
          building_id: item.building_id,
          building_name: item.building_name,
          office_id: data.office_id,
        }));
        break;

      case "building":
        // 加载楼宇下的位置
        const locationRes = await axios.get("/location/list", {
          params: {
            building_id: data.building_id,
            page_size: 1000,
          },
        });
        children = locationRes.data.data.items.map((item) => ({
          id: item.location_id,
          type: "location",
          name: item.location_name,
          leaf: false, // 改为false以支持加载摄像头节点
          location_id: item.location_id,
          location_name: item.location_name,
          building_id: data.building_id,
        }));
        break;

      case "location":
        // 加载位置下的摄像头
        const cameraRes = await axios.get(`/camera/list/${data.location_id}`);
        children = cameraRes.data.data.map((item) => ({
          id: item.camera_id,
          type: "camera",
          name: item.camera_name,
          leaf: true,
          camera_id: item.camera_id,
          camera_name: item.camera_name,
          camera_ip_address: item.camera_ip_address,
          camera_rtsp_stream_url: item.camera_rtsp_stream_url,
          location_id: data.location_id,
        }));
        break;
    }

    resolve(children);
  } catch (error) {
    console.error("加载树节点失败:", error);
    ElMessage.error("加载数据失败");
    resolve([]);
  }
};

const selectedCamera = ref(null);

const handleNodeClick = (data) => {
  if (data.type === "camera") {
    selectedCamera.value = data;
  } else {
    selectedCamera.value = null;
  }
};
</script>

<style scoped>
.el-tree {
  background: transparent;
}
</style>
