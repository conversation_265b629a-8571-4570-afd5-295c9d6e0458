// src/utils/region-manager.js
import axios from '@/utils/axios';

/**
 * 区域管理工具类
 * 提供区域配置的保存、加载、验证等功能
 */
export class RegionManager {
  constructor() {
    this.regions = [];
    this.algorithms = [];
  }

  /**
   * 加载算法字典
   */
  async loadAlgorithms() {
    try {
      const response = await axios.get('/algorithm_dictionary/list');
      if (response.data.code === 200) {
        this.algorithms = response.data.data.items || [];
        return this.algorithms;
      }
      throw new Error(response.data.message || '加载算法失败');
    } catch (error) {
      console.error('Failed to load algorithms:', error);
      throw error;
    }
  }

  /**
   * 保存摄像头的区域配置
   * @param {string} cameraId - 摄像头ID
   * @param {Array} regions - 区域配置数组
   */
  async saveRegionConfig(cameraId, regions) {
    try {
      const response = await axios.post(`/camera/${cameraId}/regions`, {
        regions: regions.map(region => ({
          shape: region.shape,
          algorithmConfig: region.algorithmConfig
        }))
      });
      
      if (response.data.code === 200) {
        return true;
      }
      throw new Error(response.data.message || '保存失败');
    } catch (error) {
      console.error('Failed to save region config:', error);
      throw error;
    }
  }

  /**
   * 加载摄像头的区域配置
   * @param {string} cameraId - 摄像头ID
   */
  async loadRegionConfig(cameraId) {
    try {
      const response = await axios.get(`/camera/${cameraId}/regions`);
      if (response.data.code === 200 && response.data.data) {
        const savedRegions = response.data.data.regions || [];
        this.regions = savedRegions.map((region, index) => ({
          id: Date.now() + index,
          shape: region.shape,
          algorithmConfig: region.algorithmConfig
        }));
        return this.regions;
      }
      return [];
    } catch (error) {
      console.error('Failed to load region config:', error);
      return [];
    }
  }

  /**
   * 验证区域配置
   * @param {Object} region - 区域对象
   */
  validateRegion(region) {
    const errors = [];

    // 验证形状
    if (!region.shape) {
      errors.push('缺少形状定义');
    } else {
      if (!region.shape.type || !['rectangle', 'polygon'].includes(region.shape.type)) {
        errors.push('无效的形状类型');
      }
      
      if (!region.shape.points || !Array.isArray(region.shape.points) || region.shape.points.length < 2) {
        errors.push('形状点数不足');
      }
    }

    // 验证算法配置
    if (region.algorithmConfig) {
      const config = region.algorithmConfig;
      
      if (!config.algorithmId) {
        errors.push('未选择算法');
      }
      
      if (typeof config.confidence !== 'number' || config.confidence < 0 || config.confidence > 1) {
        errors.push('置信度阈值无效');
      }
      
      if (typeof config.detectionInterval !== 'number' || config.detectionInterval < 1) {
        errors.push('检测间隔无效');
      }
      
      if (typeof config.alarmThreshold !== 'number' || config.alarmThreshold < 1) {
        errors.push('报警阈值无效');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证所有区域配置
   * @param {Array} regions - 区域数组
   */
  validateAllRegions(regions) {
    const results = regions.map((region, index) => ({
      index,
      ...this.validateRegion(region)
    }));

    const invalidRegions = results.filter(result => !result.isValid);
    
    return {
      isValid: invalidRegions.length === 0,
      invalidRegions,
      allResults: results
    };
  }

  /**
   * 获取算法信息
   * @param {string} algorithmId - 算法ID
   */
  getAlgorithmInfo(algorithmId) {
    return this.algorithms.find(alg => alg.algorithm_id === algorithmId);
  }

  /**
   * 创建默认算法配置
   * @param {string} algorithmId - 算法ID
   */
  createDefaultConfig(algorithmId) {
    return {
      algorithmId,
      confidence: 0.5,
      detectionInterval: 5,
      alarmThreshold: 3,
      enabled: true
    };
  }

  /**
   * 转换形状坐标（相对坐标转绝对坐标）
   * @param {Object} shape - 形状对象
   * @param {number} videoWidth - 视频宽度
   * @param {number} videoHeight - 视频高度
   */
  convertShapeToAbsolute(shape, videoWidth, videoHeight) {
    return {
      ...shape,
      points: shape.points.map(point => ({
        x: Math.round(point.x * videoWidth),
        y: Math.round(point.y * videoHeight)
      }))
    };
  }

  /**
   * 转换形状坐标（绝对坐标转相对坐标）
   * @param {Object} shape - 形状对象
   * @param {number} videoWidth - 视频宽度
   * @param {number} videoHeight - 视频高度
   */
  convertShapeToRelative(shape, videoWidth, videoHeight) {
    return {
      ...shape,
      points: shape.points.map(point => ({
        x: point.x / videoWidth,
        y: point.y / videoHeight
      }))
    };
  }

  /**
   * 计算区域面积（像素）
   * @param {Object} shape - 形状对象
   */
  calculateArea(shape) {
    if (shape.type === 'rectangle' && shape.points.length >= 2) {
      const [start, end] = shape.points;
      return Math.abs((end.x - start.x) * (end.y - start.y));
    } else if (shape.type === 'polygon' && shape.points.length >= 3) {
      // 使用鞋带公式计算多边形面积
      let area = 0;
      const points = shape.points;
      for (let i = 0; i < points.length; i++) {
        const j = (i + 1) % points.length;
        area += points[i].x * points[j].y;
        area -= points[j].x * points[i].y;
      }
      return Math.abs(area) / 2;
    }
    return 0;
  }

  /**
   * 检查点是否在区域内
   * @param {Object} point - 点坐标 {x, y}
   * @param {Object} shape - 形状对象
   */
  isPointInRegion(point, shape) {
    if (shape.type === 'rectangle' && shape.points.length >= 2) {
      const [start, end] = shape.points;
      const minX = Math.min(start.x, end.x);
      const maxX = Math.max(start.x, end.x);
      const minY = Math.min(start.y, end.y);
      const maxY = Math.max(start.y, end.y);
      
      return point.x >= minX && point.x <= maxX && point.y >= minY && point.y <= maxY;
    } else if (shape.type === 'polygon' && shape.points.length >= 3) {
      // 使用射线法判断点是否在多边形内
      let inside = false;
      const points = shape.points;
      
      for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
        if (((points[i].y > point.y) !== (points[j].y > point.y)) &&
            (point.x < (points[j].x - points[i].x) * (point.y - points[i].y) / (points[j].y - points[i].y) + points[i].x)) {
          inside = !inside;
        }
      }
      
      return inside;
    }
    return false;
  }

  /**
   * 导出配置为JSON
   * @param {string} cameraId - 摄像头ID
   * @param {Array} regions - 区域配置
   */
  exportConfig(cameraId, regions) {
    return {
      cameraId,
      exportTime: new Date().toISOString(),
      version: '1.0',
      regions: regions.map(region => ({
        shape: region.shape,
        algorithmConfig: region.algorithmConfig
      }))
    };
  }

  /**
   * 从JSON导入配置
   * @param {Object} configData - 配置数据
   */
  importConfig(configData) {
    if (!configData.regions || !Array.isArray(configData.regions)) {
      throw new Error('无效的配置格式');
    }

    return configData.regions.map((region, index) => ({
      id: Date.now() + index,
      shape: region.shape,
      algorithmConfig: region.algorithmConfig
    }));
  }
}

// 创建单例实例
export const regionManager = new RegionManager();

// 导出常用的形状类型
export const SHAPE_TYPES = {
  RECTANGLE: 'rectangle',
  POLYGON: 'polygon'
};

// 导出默认配置
export const DEFAULT_ALGORITHM_CONFIG = {
  confidence: 0.5,
  detectionInterval: 5,
  alarmThreshold: 3,
  enabled: true
};
