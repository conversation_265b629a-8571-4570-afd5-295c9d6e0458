<!--src/components/RtspPlayer.vue-->
<template>
  <div class="video-container" :style="containerStyle">
    <video ref="videoRef" class="video-element" autoplay playsinline></video>
    <canvas
      ref="canvasRef"
      class="drawing-canvas"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      v-show="isDrawingMode"
    >
    </canvas>

    <!-- 工具栏 -->
    <div class="tools-bar" v-if="isDrawingMode">
      <el-radio-group v-model="drawingType" size="small">
        <el-radio-button label="rectangle">矩形</el-radio-button>
        <el-radio-button label="polygon">多边形</el-radio-button>
      </el-radio-group>
      <el-button
        size="small"
        type="primary"
        @click="finishDrawing"
        v-if="drawingType === 'polygon' && currentShape.length > 2"
      >
        完成绘制
      </el-button>
      <el-button size="small" type="danger" @click="clearDrawing">
        清除
      </el-button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import axios from "@/utils/axios";

const props = defineProps({
  cameraId: {
    type: String,
    required: true,
  },
  width: {
    type: [String, Number],
    default: "100%",
  },
  height: {
    type: [String, Number],
    default: "100%",
  },
  drawingEnabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["error", "shape-added", "shapes-cleared"]);

// 基础状态

const canvasRef = ref(null);
const wsConnection = ref(null);
const loading = ref(true);
const isDrawingMode = ref(false);
const drawingType = ref("rectangle");
const isDrawing = ref(false);
const startPoint = ref({ x: 0, y: 0 });
const currentShape = ref([]);
const shapes = ref([]);
const streamId = ref("");

const videoRef = ref(null);
const mediaSource = ref(null);
const sourceBuffer = ref(null);

const peerConnection = ref(null);

// 计算容器样式
const containerStyle = computed(() => ({
  width: typeof props.width === "number" ? `${props.width}px` : props.width,
  height: typeof props.height === "number" ? `${props.height}px` : props.height,
}));

const handleIceCandidate = async (event) => {
  if (!streamId.value || !event.candidate) return;

  try {
    await axios.post(`/webrtc/stream/ice/${streamId.value}`, {
      candidate: event.candidate.candidate,
      sdp_mid: event.candidate.sdpMid,
      sdp_m_line_index: event.candidate.sdpMLineIndex,
    });
  } catch (error) {
    console.error("Failed to send ICE candidate:", error);
  }
};

const startStream = async () => {
  try {
    loading.value = true;
    const iceCandidatesQueue = [];

    // 创建新的 PeerConnection
    if (peerConnection.value) {
      peerConnection.value.close();
    }

    peerConnection.value = new RTCPeerConnection({
      //iceServers: [],
      iceServers: [
        {
          urls: ["stun:172.0.34.94:3478"],
          username: "user1",
          credential: "test",
        },
      ],
      iceTransportPolicy: "all",
      bundlePolicy: "max-bundle",
      //rtcpMuxPolicy: "require",
    });

    // 设置 ICE 相关事件处理器
    peerConnection.value.onicecandidate = handleIceCandidate;

    peerConnection.value.oniceconnectionstatechange = () => {
      console.log(
        "ICE Connection State:",
        peerConnection.value.iceConnectionState
      );
      switch (peerConnection.value.iceConnectionState) {
        case "checking":
          console.log("ICE checking...");
          break;
        case "connected":
          console.log("ICE connected");
          loading.value = false;
          break;
        case "failed":
          console.error("ICE connection failed");
          handleConnectionFailure();
          break;
        case "disconnected":
          console.warn("ICE disconnected");
          loading.value = true;
          attemptReconnection();
          break;
      }
    };

    peerConnection.value.onicegatheringstatechange = () => {
      console.log(
        "ICE Gathering State:",
        peerConnection.value.iceGatheringState
      );
    };

    peerConnection.value.ontrack = (event) => {
      if (event.track.kind === "video") {
        videoRef.value.srcObject = event.streams[0];
      }
    };

    // 创建 offer
    const offer = await peerConnection.value.createOffer({
      offerToReceiveVideo: true,
      offerToReceiveAudio: false,
    });

    // 修改 offer SDP
    let modifiedSdp = offer.sdp;
    if (!modifiedSdp.includes("a=setup:")) {
      modifiedSdp = modifiedSdp.replace(
        /(m=video.*\r\n)/g,
        "\$1a=setup:actpass\r\n"
      );
    } else {
      modifiedSdp = modifiedSdp.replace(
        /a=setup:(active|passive|actpass)/g,
        "a=setup:actpass"
      );
    }

    const modifiedOffer = new RTCSessionDescription({
      type: "offer",
      sdp: modifiedSdp,
    });

    // 设置本地描述
    await peerConnection.value.setLocalDescription(modifiedOffer);

    // 发送 offer 到服务器
    const response = await axios.post("/webrtc/stream/offer", {
      sdp: modifiedOffer.sdp,
      camera_id: props.cameraId,
      overlay_info: props.drawingEnabled
        ? {
            texts: [],
            shapes: shapes.value,
          }
        : null,
    });

    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }

    streamId.value = response.data.data.stream_id;

    // 设置远程描述(answer)
    const remoteDesc = new RTCSessionDescription({
      type: "answer",
      sdp: response.data.data.sdp,
    });

    await peerConnection.value.setRemoteDescription(remoteDesc);

    // 处理之前缓存的 ICE 候选者
    while (iceCandidatesQueue.length > 0) {
      const candidate = iceCandidatesQueue.shift();
      await peerConnection.value.addIceCandidate(candidate);
    }

    loading.value = false;
  } catch (error) {
    console.error("Failed to start stream:", error);
    loading.value = false;
    ElMessage.error("启动视频流失败: " + error.message);
    emit("error", error);
  }
};

// 添加重连机制
const startStreamWithRetry = async (retryCount = 0) => {
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000;

  try {
    await startStream();
  } catch (error) {
    if (retryCount < MAX_RETRIES) {
      console.warn(`Retry attempt ${retryCount + 1} after error:`, error);
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      return startStreamWithRetry(retryCount + 1);
    }
    throw error;
  }
};

// 监控连接状态
const monitorConnection = () => {
  if (!peerConnection.value) return;

  peerConnection.value.onconnectionstatechange = () => {
    console.log("Connection state:", peerConnection.value.connectionState);
    switch (peerConnection.value.connectionState) {
      case "connected":
        loading.value = false;
        break;
      case "failed":
        handleConnectionFailure();
        break;
      case "disconnected":
        loading.value = true;
        attemptReconnection();
        break;
    }
  };
};

// 处理连接失败
const handleConnectionFailure = async () => {
  loading.value = false;
  ElMessage.error("视频连接失败");
  emit("error", new Error("Connection failed"));

  if (peerConnection.value) {
    peerConnection.value.close();
    peerConnection.value = null;
  }

  await new Promise((resolve) => setTimeout(resolve, 3000));
  startStreamWithRetry();
};

// 尝试重新连接
const attemptReconnection = async () => {
  console.log("Attempting to reconnect...");
  try {
    await startStreamWithRetry();
  } catch (error) {
    console.error("Reconnection failed:", error);
    ElMessage.error("重新连接失败");
  }
};

onMounted(() => {
  monitorConnection();
  startStreamWithRetry();
});

onBeforeUnmount(() => {
  if (peerConnection.value) {
    peerConnection.value.close();
    peerConnection.value = null;
  }
});

// 心跳检测函数
const startHeartbeat = (dataChannel) => {
  const heartbeatInterval = setInterval(() => {
    if (dataChannel?.readyState === "open") {
      dataChannel.send(
        JSON.stringify({
          type: "Heartbeat",
          timestamp: Date.now(),
        })
      );
    } else if (peerConnection.value?.connectionState !== "connected") {
      clearInterval(heartbeatInterval);
    }
  }, 15000);

  // 清理函数
  return () => clearInterval(heartbeatInterval);
};

// 组件卸载时的清理函数
onBeforeUnmount(() => {
  if (peerConnection.value) {
    peerConnection.value.close();
    peerConnection.value = null;
  }
});

// 发送叠加信息的辅助函数
const sendOverlayInfo = () => {
  if (dataChannel.value?.readyState === "open") {
    dataChannel.value.send(
      JSON.stringify({
        type: "UpdateOverlay",
        data: {
          texts: [], // 如果有文字叠加
          shapes: shapes.value.map((shape) => ({
            shape_type: shape.type,
            points: shape.points,
            color: shape.color || "#FF0000",
            filled: shape.filled || false,
          })),
        },
      })
    );
  }
};

// 监听绘图状态变化
watch(
  shapes,
  () => {
    if (props.drawingEnabled) {
      sendOverlayInfo();
    }
  },
  { deep: true }
);

// 监听绘图模式变化
watch(
  () => props.drawingEnabled,
  (enabled) => {
    if (enabled) {
      sendOverlayInfo();
    }
  }
);

// 停止视频流
const stopStream = async () => {
  try {
    if (wsConnection.value) {
      wsConnection.value.close();
    }
    if (streamId.value) {
      await axios.post(`/webrtc/stream/stop/${streamId.value}`);
    }
  } catch (error) {
    console.error("Failed to stop stream:", error);
  }
};

// 绘图相关方法
const initCanvas = () => {
  const canvas = canvasRef.value;
  const video = videoRef.value;
  if (canvas && video) {
    canvas.width = video.clientWidth;
    canvas.height = video.clientHeight;
  }
};

// 鼠标事件处理
const handleMouseDown = (event) => {
  if (!isDrawingMode.value) return;

  isDrawing.value = true;
  const rect = canvasRef.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  startPoint.value = { x, y };

  if (drawingType.value === "polygon") {
    currentShape.value.push({ x, y });
    drawShapes();
  }
};

const handleMouseMove = (event) => {
  if (!isDrawingMode.value || !isDrawing.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  const ctx = canvasRef.value.getContext("2d");

  if (drawingType.value === "rectangle") {
    // 清除画布并重绘所有形状
    ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    drawShapes();

    // 绘制当前矩形
    ctx.strokeStyle = "#FF0000";
    ctx.lineWidth = 2;
    ctx.strokeRect(
      startPoint.value.x,
      startPoint.value.y,
      x - startPoint.value.x,
      y - startPoint.value.y
    );
  } else if (drawingType.value === "polygon" && currentShape.value.length > 0) {
    // 绘制多边形预览线
    ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    drawShapes();

    ctx.strokeStyle = "#FF0000";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(
      currentShape.value[currentShape.value.length - 1].x,
      currentShape.value[currentShape.value.length - 1].y
    );
    ctx.lineTo(x, y);
    ctx.stroke();
  }
};

const handleMouseUp = (event) => {
  if (!isDrawingMode.value || !isDrawing.value) return;

  isDrawing.value = false;

  if (drawingType.value === "rectangle") {
    const rect = canvasRef.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const shape = {
      type: "rectangle",
      points: [
        { x: startPoint.value.x, y: startPoint.value.y },
        { x, y },
      ],
      color: "#FF0000",
      filled: false,
    };

    shapes.value.push(shape);
    emit("shape-added", shape);
    drawShapes();
  }
};

const finishDrawing = () => {
  if (drawingType.value === "polygon" && currentShape.value.length > 2) {
    const shape = {
      type: "polygon",
      points: [...currentShape.value],
      color: "#FF0000",
      filled: false,
    };

    shapes.value.push(shape);
    emit("shape-added", shape);
    currentShape.value = [];
    drawShapes();
  }
};

const drawShapes = () => {
  const ctx = canvasRef.value?.getContext("2d");
  if (!ctx) return;

  ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);

  shapes.value.forEach((shape) => {
    ctx.strokeStyle = shape.color || "#FF0000";
    ctx.lineWidth = 2;

    if (shape.type === "rectangle" && shape.points.length >= 2) {
      const [start, end] = shape.points;
      ctx.strokeRect(start.x, start.y, end.x - start.x, end.y - start.y);
    } else if (shape.type === "polygon" && shape.points.length > 2) {
      ctx.beginPath();
      ctx.moveTo(shape.points[0].x, shape.points[0].y);
      for (let i = 1; i < shape.points.length; i++) {
        ctx.lineTo(shape.points[i].x, shape.points[i].y);
      }
      ctx.closePath();
      ctx.stroke();

      if (shape.filled) {
        ctx.fillStyle = shape.color + "40"; // 添加透明度
        ctx.fill();
      }
    }
  });

  // 绘制当前正在绘制的多边形
  if (drawingType.value === "polygon" && currentShape.value.length > 0) {
    ctx.strokeStyle = "#FF0000";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(currentShape.value[0].x, currentShape.value[0].y);
    for (let i = 1; i < currentShape.value.length; i++) {
      ctx.lineTo(currentShape.value[i].x, currentShape.value[i].y);
    }
    ctx.stroke();

    // 绘制点
    currentShape.value.forEach((point) => {
      ctx.fillStyle = "#FF0000";
      ctx.beginPath();
      ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI);
      ctx.fill();
    });
  }
};

// 公开方法
const toggleDrawingMode = (enabled) => {
  isDrawingMode.value = enabled;
  if (enabled) {
    initCanvas();
  }
};

const clearDrawing = () => {
  const ctx = canvasRef.value.getContext("2d");
  ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
  shapes.value = [];
  currentShape.value = [];
  emit("shapes-cleared");
};

const setShapes = (newShapes) => {
  shapes.value = newShapes;
  drawShapes();
};

const getShapes = () => {
  return shapes.value;
};

// 监听器
watch(
  () => props.drawingEnabled,
  (val) => {
    toggleDrawingMode(val);
  }
);

onBeforeUnmount(() => {
  stopStream();
});

// 暴露公共方法
defineExpose({
  toggleDrawingMode,
  clearDrawing,
  setShapes,
  getShapes,
});
</script>

<style scoped>
.video-container {
  position: relative;
  background: #000;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.tools-bar {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 4px;
  z-index: 10;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: white;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
