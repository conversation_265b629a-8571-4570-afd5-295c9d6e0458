<!-- 报警历史页面,src/views/analysis/AlarmHistory.vue -->
<template>
  <div class="flex flex-col h-full p-4">
    <!-- 查询条件 -->
    <div class="mb-4">
      <el-form :model="queryForm" inline>
        <el-form-item label="报警时间">
          <el-date-picker
            v-model="queryForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="defaultTime"
          />
        </el-form-item>
        <el-form-item label="摄像头">
          <el-input
            v-model="queryForm.camera_name"
            placeholder="请输入摄像头名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="报警类型">
          <el-select
            v-model="queryForm.algorithm_id"
            placeholder="请选择报警类型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in algorithmOptions"
              :key="item.algorithm_id"
              :label="item.algorithm_name"
              :value="item.algorithm_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select
            v-model="queryForm.is_confirmed"
            placeholder="请选择处理状态"
            clearable
            style="width: 100px"
          >
            <el-option label="已处理" :value="true" />
            <el-option label="未处理" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
      class="flex-1"
    >
      <el-table-column prop="alarm_time" label="报警时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.alarm_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="area_name" label="行政区划" width="100" />
      <el-table-column prop="office_name" label="机构名称" width="200" />
      <el-table-column prop="location_name" label="位置" width="100" />
      <el-table-column prop="room_name" label="房间名称" width="100" />
      <el-table-column
        prop="room_purpose_type_name"
        label="房间类型"
        width="100"
      />
      <el-table-column prop="camera_name" label="摄像头" />
      <el-table-column prop="algorithm_name" label="报警类型" />
      <el-table-column prop="is_pushed" label="推送状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_pushed ? 'success' : 'info'">
            {{ row.is_pushed ? "已推送" : "未推送" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_confirmed" label="处理状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_confirmed ? 'success' : 'warning'">
            {{ row.is_confirmed ? "已处理" : "未处理" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              size="small"
              type="primary"
              @click="handleViewImage(row)"
            >
              查看截图
            </el-button>
            <el-button
              size="small"
              type="success"
              @click="handlePlayVideo(row)"
            >
              播放视频
            </el-button>
            <el-button
              size="small"
              type="warning"
              :disabled="row.is_confirmed"
              @click="handleConfirm(row)"
            >
              确认处理
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 确认处理对话框 -->
    <el-dialog v-model="confirmDialogVisible" title="确认处理" width="500px">
      <el-form :model="confirmForm" label-width="80px">
        <el-form-item label="处理备注">
          <el-input
            v-model="confirmForm.confirm_note"
            type="textarea"
            :rows="3"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="confirmDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog
      v-model="imageDialogVisible"
      fullscreen
      :show-close="false"
      :append-to-body="true"
      class="image-preview-dialog"
    >
      <ImageViewer
        v-if="currentImage"
        :image-url="currentImage"
        @close="imageDialogVisible = false"
      />
    </el-dialog>
    <!-- 视频播放 -->
    <el-dialog v-model="videoDialogVisible" title="报警视频" width="800px">
      <video :src="currentVideo" controls class="w-full" v-if="currentVideo" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import axios from "@/utils/axios";
import { format } from "date-fns";
import ImageViewer from "@/components/ImageViewer.vue";

// 状态定义
const loading = ref(false);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 设置默认的具体时间点
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0), // 开始时间点 00:00:00
  new Date(2000, 1, 1, 23, 59, 59), // 结束时间点 23:59:59
];

// 查询表单
const queryForm = ref({
  timeRange: [
    new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), // 30天前
    new Date(), // 当前时间
  ],
  camera_name: "",
  algorithm_id: "",
  is_confirmed: null,
});

// 确认处理相关
const confirmDialogVisible = ref(false);
const confirmForm = ref({
  alarm_id: "",
  confirm_note: "",
});

// 图片预览相关
const imageDialogVisible = ref(false);
const currentImage = ref("");

// 视频播放相关
const videoDialogVisible = ref(false);
const currentVideo = ref("");

// 算法选项
const algorithmOptions = ref([]);

// 加载算法选项
const loadAlgorithmOptions = async () => {
  try {
    const res = await axios.get("/algorithm_dictionary/list");
    if (res.data.code === 200) {
      //algorithmOptions.value = res.data.data;
      for (const item of res.data.data) {
        algorithmOptions.value.push({
          algorithm_id: item.algorithm_id,
          algorithm_name: item.algorithm_name,
        });
      }
    }
  } catch (error) {
    console.error("加载算法选项失败:", error);
  }
};

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  try {
    const res = await axios.post("/alarm_record/page", {
      page: currentPage.value,
      page_size: pageSize.value,
      start_time: queryForm.value.timeRange[0],
      end_time: queryForm.value.timeRange[1],
      camera_name: queryForm.value.camera_name,
      algorithm_id: queryForm.value.algorithm_id,
      is_confirmed: queryForm.value.is_confirmed,
    });

    if (res.data.code === 200) {
      tableData.value = res.data.data.items;
      total.value = res.data.data.total;
    }
  } catch (error) {
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 查看图片
const handleViewImage = (row) => {
  currentImage.value = row.alarm_image_url;
  imageDialogVisible.value = true;
};

// 播放视频
const handlePlayVideo = (row) => {
  currentVideo.value = row.alarm_video_url;
  videoDialogVisible.value = true;
};

const handleSearch = () => {
  loadTableData();
};

// 确认处理
const handleConfirm = (row) => {
  confirmForm.value.alarm_id = row.alarm_id;
  confirmForm.value.confirm_note = "";
  confirmDialogVisible.value = true;
};

// 提交确认
const submitConfirm = async () => {
  try {
    const res = await axios.post("/alarm_record/confirm", confirmForm.value);
    if (res.data.code === 200) {
      ElMessage.success("确认处理成功");
      confirmDialogVisible.value = false;
      loadTableData();
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    ElMessage.error("确认处理失败");
  }
};

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadTableData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadTableData();
};

// 重置查询条件
const handleReset = () => {
  queryForm.value = {
    timeRange: [],
    camera_name: "",
    algorithm_id: "",
    is_confirmed: null,
  };
};

// 初始化
onMounted(() => {
  loadAlgorithmOptions();
});

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return "-";
  return format(new Date(date), "yyyy-MM-dd HH:mm:ss");
};

// 监听查询条件变化
watch(
  () => queryForm.value,
  () => {
    if (queryForm.value.timeRange?.length === 2) {
      loadTableData();
    }
  },
  { deep: true }
);
</script>

<style scoped>
.flex-1 {
  flex: 1;
}

.h-full {
  height: 100%;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
}

/* 表单样式 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 普通对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px 40px;
}

/* 图片预览对话框样式 */
:deep(.image-preview-dialog) {
  background: transparent;
}
:deep(.image-preview-dialog .el-dialog__header) {
  display: none;
}
:deep(.image-preview-dialog .el-dialog__body) {
  padding: 0;
  height: 100vh;
  position: relative;
}

/* 视频播放器样式 */
:deep(.el-dialog__body video) {
  width: 100%;
  height: auto;
}

/* 按钮组样式 */
:deep(.el-button-group) {
  display: flex;
  gap: 4px;
}

/* 分页器样式 */
:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 16px 0;
}
</style>
