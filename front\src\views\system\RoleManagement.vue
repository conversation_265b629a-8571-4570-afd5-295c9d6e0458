<template>
    <div class="p-4">
      <el-container>
        <!-- 左侧角色列表 -->
        <el-aside width="700px" class="mr-4">
          <div class="mb-4 flex justify-between items-center">
            <el-button type="primary" @click="handleAdd">新增角色</el-button>
            <el-input
              v-model="searchText"
              placeholder="搜索角色名称"
              class="w-64"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
  
          <el-table
            :data="tableData"
            border
            stripe
            v-loading="loading"
            @row-click="handleRowClick"
            :highlight-current-row="true"
          >
            <el-table-column prop="role_name" label="角色名称" min-width="120" />
            <el-table-column prop="role_description" label="描述" min-width="150" />
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.is_active"
                  @change="handleStatusChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="160" fixed="right">
              <template #default="{ row }">
                <el-button-group>
                  <el-button size="small" type="primary" @click.stop="handleEdit(row)">
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click.stop="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
  
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="page"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-aside>
  
        <!-- 右侧权限分配 -->
        <el-main class="max-w-[600px]">
          <div v-if="currentRole" class="bg-white p-4 rounded shadow">
            <div class="mb-4 flex justify-between items-center">
              <h3 class="text-lg font-bold">
                {{ currentRole.role_name }} - 权限配置
              </h3>
              <el-button type="primary" @click="handleSavePermissions">
                保存权限配置
              </el-button>
            </div>
            <el-tree
              ref="permissionTree"
              :data="permissionTreeData"
              show-checkbox
              node-key="permission_id"
              :check-strictly="true"
              :props="{
                label: 'permission_name',
                children: 'children',
              }"
              v-loading="permissionLoading"
            />
          </div>
          <div v-else class="h-full flex items-center justify-center text-gray-400">
            请选择左侧角色进行权限配置
          </div>
        </el-main>
      </el-container>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="角色名称" prop="role_name">
            <el-input v-model="formData.role_name" />
          </el-form-item>
          <el-form-item label="角色描述" prop="role_description">
            <el-input
              v-model="formData.role_description"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-switch v-model="formData.is_active" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  import axios from "@/utils/axios";
  
  // 表格相关
  const loading = ref(false);
  const tableData = ref([]);
  const searchText = ref("");
  const page = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  
  // 表单相关
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增角色");
  const formRef = ref(null);
  const formData = ref({
    role_id: "",
    role_name: "",
    role_description: "",
    is_active: true,
  });
  
  const formRules = {
    role_name: [
      { required: true, message: "请输入角色名称", trigger: "blur" },
      { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
    ],
  };
  
  // 权限树相关
  const currentRole = ref(null);
  const permissionTree = ref(null);
  const permissionTreeData = ref([]);
  const permissionLoading = ref(false);
  
  // 加载表格数据
  const loadTableData = async () => {
    loading.value = true;
    try {
      const res = await axios.post("/sys_role/page", {
        page: page.value,
        page_size: pageSize.value,
        search_text: searchText.value || undefined,
      });
      const { records, total: totalCount } = res.data.data;
      tableData.value = records;
      total.value = totalCount;
    } catch (error) {
      ElMessage.error("加载数据失败");
    } finally {
      loading.value = false;
    }
  };
  
  // 加载权限树数据
  const loadPermissionTree = async () => {
    try {
      const res = await axios.post("/sys_permission/page", {
        page: 1,
        page_size: 1000,
      });
      const { records } = res.data.data;
      permissionTreeData.value = buildTree(records);
    } catch (error) {
      ElMessage.error("加载权限数据失败");
    }
  };
  
//   // 加载角色权限
//   const loadRolePermissions = async (roleId) => {
//     permissionLoading.value = true;
//     try {
//       // 确保先清空现有选中状态
//       if (permissionTree.value) {
//         permissionTree.value.setCheckedKeys([]);
//       }

//       const res = await axios.get(`/sys_role/permissions/${roleId}`);
//       const permissions = res.data.data;
//       permissionTree.value.setCheckedKeys(permissions);
//     } catch (error) {
//       ElMessage.error("加载角色权限失败");
//     } finally {
//       permissionLoading.value = false;
//     }
//   };

  const loadRolePermissions = async (roleId) => {
  permissionLoading.value = true;
  try {
    // 确保先清空现有选中状态
    if (permissionTree.value) {
      permissionTree.value.setCheckedKeys([]);
    }

    const res = await axios.get(`/sys_role/permissions/${roleId}`);
    const permissions = res.data.data;
    if (permissionTree.value) {
      permissionTree.value.setCheckedKeys(permissions);
    }
  } catch (error) {
    ElMessage.error("加载角色权限失败");
  } finally {
    permissionLoading.value = false;
  }
};  
  
  // 构建树形结构
  const buildTree = (data) => {
    const map = {};
    const result = [];
  
    data.forEach((item) => {
      map[item.permission_id] = {
        ...item,
        children: [],
      };
    });
  
    data.forEach((item) => {
      const node = map[item.permission_id];
      if (item.parent_id) {
        const parent = map[item.parent_id];
        if (parent) {
          parent.children.push(node);
        }
      } else {
        result.push(node);
      }
    });
  
    return result;
  };
  
  // 搜索处理
  const handleSearch = () => {
    page.value = 1;
    loadTableData();
  };
  
  // 分页处理
  const handleSizeChange = (val) => {
    pageSize.value = val;
    loadTableData();
  };
  
  const handleCurrentChange = (val) => {
    page.value = val;
    loadTableData();
  };
  
  // 新增
  const handleAdd = () => {
    dialogTitle.value = "新增角色";
    formData.value = {
      role_id: "",
      role_name: "",
      role_description: "",
      is_active: true,
    };
    dialogVisible.value = true;
  };
  
  // 编辑
  const handleEdit = (row) => {
    dialogTitle.value = "编辑角色";
    formData.value = { ...row };
    dialogVisible.value = true;
  };
  
  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;
  
    try {
      await formRef.value.validate();
      if (formData.value.role_id) {
        // 编辑
        await axios.post("/sys_role/update", formData.value);
        ElMessage.success("更新成功");
      } else {
        // 新增
        await axios.post("/sys_role/create", formData.value);
        ElMessage.success("创建成功");
      }
      dialogVisible.value = false;
      loadTableData();
    } catch (error) {
      ElMessage.error("操作失败");
    }
  };
  
  // 状态更改
  const handleStatusChange = async (row) => {
    try {
      await axios.post("/sys_role/update", {
        ...row,
      });
      ElMessage.success("状态更新成功");
    } catch (error) {
      row.is_active = !row.is_active;
      ElMessage.error("状态更新失败");
    }
  };
  
  // 删除
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确定要删除该角色吗？", "提示", {
        type: "warning",
      });
      await axios.delete(`/sys_role/delete/${row.role_id}`);
      ElMessage.success("删除成功");
      loadTableData();
    } catch (error) {
      if (error !== "cancel") {
        ElMessage.error("删除失败");
      }
    }
  };
  
  // 选择角色
  const handleRowClick = (row) => {
    // 如果当前已经选中了一个角色，先清空权限树
    if (permissionTree.value) {
      permissionTree.value.setCheckedKeys([]);
    }
    currentRole.value = row;
    loadRolePermissions(row.role_id);
  };
  
  // 保存权限配置
  const handleSavePermissions = async () => {
    if (!currentRole.value || !permissionTree.value) return;
  
    try {
      const checkedKeys = permissionTree.value.getCheckedKeys();
      //const halfCheckedKeys = permissionTree.value.getHalfCheckedKeys();
      //const allKeys = [...checkedKeys, ...halfCheckedKeys];
  
      await axios.post("/sys_role/assign_permissions", {
        role_id: currentRole.value.role_id,
        permission_ids: checkedKeys,
      });
      ElMessage.success("权限配置保存成功");
    } catch (error) {
      ElMessage.error("权限配置保存失败");
    }
  };
  
  // 生命周期钩子
  onMounted(() => {
    loadTableData();
    loadPermissionTree();
  });
  </script>
  
  <style scoped>
  .el-aside {
    background: white;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .el-main {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .el-pagination {
    justify-content: flex-end;
  }
  </style>