//rtsp/frame_consumer_disk.rs:

use crate::model::frame_queue::FrameQueue;
use chrono::Local;
use image::{ImageBuffer, Rgb};
use log::{error, info};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::Semaphore;
use tokio::task;
use tokio::time::Duration;
use uuid;
use std::io::Cursor;
use image::ImageFormat;
use std::ops::Deref;

use crate::config::{self};

pub struct FrameConsumer {
    consumer_threads: usize,
    frame_queue: Arc<FrameQueue>,
    config:Arc<config::Config>,
    images_dir: Arc<String>,
}

impl FrameConsumer {
    pub fn new(frame_queue: Arc<FrameQueue>, consumer_threads: usize, config:Arc<config::Config>,images_dir:Arc<String>) -> Self {
        FrameConsumer {
            consumer_threads,
            frame_queue,
            config,
            images_dir,
        }
    }

    pub async fn start(&self) -> anyhow::Result<()> {

        let queue = Arc::clone(&self.frame_queue);
        let images_dir = Arc::clone(&self.images_dir);
        let config=Arc::clone(&self.config);

        // 创建信号量来控制并发线程数
        let semaphore = Arc::new(Semaphore::new(self.consumer_threads));

        // 生成多个消费者线程
        let mut handles = Vec::new();

        for _ in 0..self.consumer_threads {
            let queue = Arc::clone(&queue);
            let images_dir = Arc::clone(&images_dir);
            let semaphore = Arc::clone(&semaphore);
            let config=Arc::clone(&config);

            let handle = tokio::spawn(async move {
                loop {
                    // 获取信号量许可
                    let _permit = semaphore.acquire().await.unwrap();
                    let images_dir = Arc::clone(&images_dir);

                    if let Some(room_frame) = queue.pop() {
                        // 为房间中的每个摄像头的帧保存图片
                        for (camera_id, camera_frame) in room_frame.frames {
                            let current_time = Local::now();
                            let formatted_time = current_time.format("%Y%m%d_%H%M%S_%3f");
                            let uuid = uuid::Uuid::new_v4().to_string();
                            let file_name = format!(
                                "frame_{}_{}_{}_{}.jpg",
                                room_frame.room_id,
                                camera_id,
                                formatted_time,
                                uuid
                            );
                            let images_dir = std::path::PathBuf::from(images_dir.as_ref());
                            let save_path: PathBuf = images_dir.join(&file_name);

                            // 使用 tokio 的 spawn_blocking 在线程池中执行 CPU 密集型任务
                            let save_path = save_path.clone();

                            // 在线程池中执行图像编码
                            let encoded_image = task::spawn_blocking(move || {
                                let data = camera_frame.data.deref();
                                match ImageBuffer::<Rgb<u8>, _>::from_raw(
                                    camera_frame.width as u32,
                                    camera_frame.height as u32,
                                    data,
                                ) {
                                    Some(img) => {
                                        let mut buffer = Cursor::new(Vec::new());
                                        match img.write_to(&mut buffer, ImageFormat::Jpeg) {
                                            Ok(_) => Some(buffer.into_inner()),
                                            Err(e) => {
                                                error!("Failed to encode image: {}", e);
                                                None
                                            }
                                        }
                                    }
                                    None => {
                                        error!(
                                            "Failed to create image buffer for frame: {}x{}",
                                            camera_frame.width, camera_frame.height
                                        );
                                        None
                                    }
                                }
                            })
                            .await
                            .unwrap();

                            // 使用 tokio::fs 异步写入文件
                            if let Some(encoded_data) = encoded_image {
                                match tokio::fs::write(&save_path, encoded_data).await {
                                    Ok(_) => {
                                        info!(
                                            "Successfully saved frame to: {}",
                                            save_path.display()
                                        );
                                    }
                                    Err(e) => {
                                        error!("Failed to write image file: {}", e);
                                    }
                                }
                            }                            

                        }

                        if config.rtsp.use_consumer_sleep {
                            tokio::time::sleep(Duration::from_millis(config.rtsp.consumer_sleep_time)).await;
                        }

                    } else {
                        // 如果队列为空，让出 CPU
                        tokio::task::yield_now().await;
                    }
                }
            });

            handles.push(handle);
        }

        // 等待所有消费者线程完成
        for handle in handles {
            handle.await?;
        }

        Ok(())
    }
}