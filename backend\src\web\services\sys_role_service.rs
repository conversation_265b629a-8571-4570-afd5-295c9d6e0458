//src/web/services/sys_role_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use log::info;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::sys_role::{
    PageRequest, PageResponse, RoleCreateRequest, RolePermissionRequest, RoleUpdateRequest,
    SysRoleEntity,
};

#[derive(<PERSON><PERSON>, Debug)]
pub struct SysRoleService {
    pool: Arc<Pool<Postgres>>,
}

impl SysRoleService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    pub async fn create(&self, req: RoleCreateRequest) -> Result<()> {
        let id = Uuid::new_v4().simple().to_string();
        info!("Creating role with id: {}", id);

        sqlx::query!(
            r#"
            INSERT INTO sys_role (
                role_id, role_name, role_description, is_active,
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            "#,
            id,
            req.role_name,
            req.role_description,
            req.is_active.unwrap_or(true),
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create role")?;

        info!("Successfully created role with id: {}", id);
        Ok(())
    }

    pub async fn update(&self, req: RoleUpdateRequest) -> Result<()> {
        info!("Updating role with id: {}", req.role_id);

        sqlx::query!(
            r#"
            UPDATE sys_role
            SET role_name = $1,
                role_description = $2,
                is_active = $3,
                updated_at = $4
            WHERE role_id = $5
            "#,
            req.role_name,
            req.role_description,
            req.is_active,
            Utc::now(),
            req.role_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update role")?;

        info!("Successfully updated role with id: {}", req.role_id);
        Ok(())
    }

    pub async fn delete(&self, id: &str) -> Result<()> {
        info!("Deleting role with id: {}", id);

        sqlx::query!("DELETE FROM sys_role WHERE role_id = $1", id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete role")?;

        info!("Successfully deleted role with id: {}", id);
        Ok(())
    }

    pub async fn get_page(&self, req: PageRequest) -> Result<PageResponse<SysRoleEntity>> {
        let offset = (req.page - 1) * req.page_size;

        let mut query = String::from("SELECT COUNT(*) FROM sys_role WHERE 1=1");
        let mut query_sql = String::from(
            r#"
            SELECT *
            FROM sys_role
            WHERE 1=1
            "#,
        );

        if let Some(search_text) = &req.search_text {
            let search_condition =
                format!(" AND (role_name ILIKE '%{}%')", search_text);
            query.push_str(&search_condition);
            query_sql.push_str(&search_condition);
        }

        query_sql.push_str(" ORDER BY created_at DESC LIMIT $1 OFFSET $2");

        let total: i64 = sqlx::query_scalar(&query)
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        let records = sqlx::query_as::<_, SysRoleEntity>(&query_sql)
            .bind(req.page_size)
            .bind(offset)
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to get roles")?;

        Ok(PageResponse {
            records,
            total,
            page: req.page,
            page_size: req.page_size,
        })
    }

    pub async fn assign_permissions(&self, req: RolePermissionRequest) -> Result<()> {
        let mut tx = self.pool.begin().await?;

        // 删除原有权限
        sqlx::query!(
            "DELETE FROM sys_role_permission WHERE role_id = $1",
            req.role_id
        )
        .execute(&mut *tx)
        .await
        .context("Failed to delete existing permissions")?;

        // 插入新权限
        for permission_id in req.permission_ids {
            sqlx::query!(
                r#"
                INSERT INTO sys_role_permission (role_id, permission_id, created_at)
                VALUES ($1, $2, $3)
                "#,
                req.role_id,
                permission_id,
                Utc::now()
            )
            .execute(&mut *tx)
            .await
            .context("Failed to insert new permission")?;
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn get_role_permissions(&self, role_id: &str) -> Result<Vec<String>> {
        let permissions = sqlx::query!(
            r#"
            SELECT permission_id
            FROM sys_role_permission
            WHERE role_id = $1
            "#,
            role_id
        )
        .fetch_all(self.pool.as_ref())
        .await
        .context("Failed to get role permissions")?;

        Ok(permissions
            .into_iter()
            .map(|row| row.permission_id)
            .collect())
    }
}