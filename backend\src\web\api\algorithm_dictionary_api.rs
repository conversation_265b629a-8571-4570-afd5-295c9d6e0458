//src/web/api/algorithm_dictionary_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete,get, post},
    Json, Router,
};
use log::error;
use std::sync::Arc;

use crate::web::model::{
    algorithm_dictionary::{
        AlgorithmCreateRequest, AlgorithmDictionary, AlgorithmUpdateRequest, PageRequest, PageResponse,
    },
    appstate::AppState,
    common_model::ApiResponse,
};

pub fn register_algorithm_dictionary_api() -> Router<Arc<AppState>> {
    Router::new().nest("/algorithm_dictionary", algorithm_dictionary_routes())
}

fn algorithm_dictionary_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create))
        .route("/update", post(update))
        .route("/delete/:id", delete(delete_algorithm))
        .route("/page", post(get_page))
        .route("/list", get(get_list))
}

async fn create(
    State(state): State<Arc<AppState>>,
    <PERSON><PERSON>(req): Json<AlgorithmCreateRequest>,
) -> <PERSON><PERSON><ApiResponse<()>> {
    match state.service_manager.algorithm_dictionary_service.create(req).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("创建失败: {:#}", e))),
    }
}

async fn update(
    State(state): State<Arc<AppState>>,
    Json(req): Json<AlgorithmUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.algorithm_dictionary_service.update(req).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("更新失败: {:#}", e))),
    }
}

async fn delete_algorithm(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.algorithm_dictionary_service.delete(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除失败: {:#}", e))),
    }
}

async fn get_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<PageRequest>,
) -> Json<ApiResponse<PageResponse<AlgorithmDictionary>>> {
    match state.service_manager.algorithm_dictionary_service.get_page(req).await {
        Ok(page) => Json(ApiResponse::success(Some(page))),
        Err(e) => {
            error!("获取算法列表失败: {:#}", e);
            Json(ApiResponse::error(format!("获取失败: {:#}", e)))
        }
    }
}

async fn get_list(
    State(state): State<Arc<AppState>>,
) -> Json<ApiResponse<Vec<AlgorithmDictionary>>> {
    match state.service_manager.algorithm_dictionary_service.list().await {
        Ok(list) => Json(ApiResponse::success(Some(list))),
        Err(e) => Json(ApiResponse::error(format!("获取算法列表失败: {:#}", e))),
    }
}
