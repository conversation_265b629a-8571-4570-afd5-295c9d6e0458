// src/web/api/auth.rs
use axum::{extract::Extension, extract::State, routing::post, Json, Router};
use std::sync::Arc;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    sys_user::LoginRequest,
    sys_permission::LoginMenuResponse,
};

use crate::web::model::sys_user::Claims;

pub fn register_sys_user_api() -> Router<Arc<AppState>> {
    Router::new().nest("/auth", auth_routes())
}

fn auth_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/login", post(login))
        .route("/logout", post(logout))
}

async fn login(
    State(state): State<Arc<AppState>>,
    Json(req): Json<LoginRequest>,
) -> <PERSON>son<ApiResponse<LoginMenuResponse>> {
    match state
        .service_manager
        .sys_user_service
        .login(&req.username, &req.password)
        .await
    {
        Ok(response) => Json(ApiResponse::success(Some(response))),
        Err(e) => Json(ApiResponse::error(format!("登录失败: {:#}", e))),
    }
}

async fn logout(
    State(state): State<Arc<AppState>>,
    claims: Extension<Claims>, 
) -> Json<ApiResponse<()>> {
    match state
        .service_manager
        .sys_user_service
        .logout(&claims.sub)
        .await
    {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("登出失败: {:#}", e))),
    }
}
