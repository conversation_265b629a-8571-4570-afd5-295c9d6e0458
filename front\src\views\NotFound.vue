<!-- src/views/NotFound.vue -->  
<template>  
    <div class="min-h-screen bg-gray-100 flex items-center justify-center">  
      <div class="max-w-xl w-full px-4">  
        <div class="text-center">  
          <!-- 404图标 -->  
          <div class="mb-8">  
            <svg class="mx-auto h-32 w-32 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">  
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"   
                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />  
            </svg>  
          </div>  
  
          <!-- 错误信息 -->  
          <div class="text-center mb-8">  
            <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>  
            <h2 class="text-3xl font-semibold text-gray-700 mb-4">页面未找到</h2>  
            <p class="text-gray-500 text-lg mb-8">  
              抱歉，您访问的页面不存在或已被移除。  
            </p>  
          </div>  
  
          <!-- 操作按钮 -->  
          <div class="flex flex-col sm:flex-row justify-center gap-4">  
            <button   
              @click="goBack"   
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm"  
            >  
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">  
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />  
              </svg>  
              返回上一页  
            </button>  
  
            <button   
              @click="goHome"   
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm"  
            >  
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">  
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />  
              </svg>  
              返回首页  
            </button>  
          </div>  
  
          <!-- 倒计时提示 -->  
          <div class="mt-8 text-sm text-gray-500">  
            {{ countdown }}秒后自动返回首页  
          </div>  
        </div>  
      </div>  
    </div>  
  </template>  
  
  <script setup>  
  import { ref, onMounted, onUnmounted } from 'vue'  
  import { useRouter } from 'vue-router'  
  
  const router = useRouter()  
  const countdown = ref(5)  
  let timer = null  
  
  // 返回上一页  
  const goBack = () => {  
    router.back()  
  }  
  
  // 返回首页  
  const goHome = () => {  
    router.push('/dashboard')  
  }  
  
  // 倒计时自动跳转  
  onMounted(() => {  
    timer = setInterval(() => {  
      countdown.value--  
      if (countdown.value <= 0) {  
        clearInterval(timer)  
        goHome()  
      }  
    }, 1000)  
  })  
  
  // 组件卸载时清除定时器  
  onUnmounted(() => {  
    if (timer) {  
      clearInterval(timer)  
    }  
  })  
  </script>  
  
  <style scoped>  
  /* 添加简单的动画效果 */  
  @keyframes bounce {  
    0%, 100% {  
      transform: translateY(0);  
    }  
    50% {  
      transform: translateY(-10px);  
    }  
  }  
  
  svg {  
    animation: bounce 2s infinite;  
  }  
  
  /* 按钮悬停效果 */  
  button {  
    transition: all 0.2s;  
  }  
  
  button:hover {  
    transform: translateY(-1px);  
  }  
  
  button:active {  
    transform: translateY(0);  
  }  
  </style>  