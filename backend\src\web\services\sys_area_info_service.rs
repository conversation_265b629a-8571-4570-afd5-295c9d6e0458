//web/services/sys_area_info_service.rs
use anyhow::{Context, Result};  
use chrono::Utc;  
use sqlx::{Pool, Postgres};  
use std::{fs::File, io::BufReader, sync::Arc};  
use crate::web::model::sys_area_info::{AreaNode, SysAreaInfo};  

#[derive(Clone, Debug)]  
pub struct SysAreaInfoService {  
    pool: Arc<Pool<Postgres>>,  
}  

impl SysAreaInfoService {  
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {  
        Self { pool }  
    }  

    pub async fn import_json_data(&self, file_path: &str) -> Result<()> {  
        // 读取JSON文件  
        let file = File::open(file_path)  
            .with_context(|| format!("Failed to open file: {}", file_path))?;  
        let reader = BufReader::new(file);  
        let areas: Vec<AreaNode> = serde_json::from_reader(reader)  
            .context("Failed to parse JSON data")?;  

        // 清空现有数据  
        sqlx::query("TRUNCATE TABLE sys_area_info CASCADE")  
            .execute(self.pool.as_ref())  
            .await  
            .context("Failed to truncate table")?;  

        // 递归处理并插入数据  
        for area in areas {  
            self.process_area_node(&area).await?;  
        }  

        Ok(())  
    }  

    async fn process_area_node(  
        &self,  
        node: &AreaNode 
    ) -> Result<()> {  
        let area = SysAreaInfo {  
            code: node.code.to_string(),  
            name: node.name.clone(),  
            parent_code: Some(node.pcode.to_string()),
            level: node.level,  
            is_active: true,  
            created_at: Some(Utc::now()),  
            updated_at: Some(Utc::now()),  
        };  

        sqlx::query!(  
            r#"  
            INSERT INTO sys_area_info (code, name, parent_code, level, is_active, created_at, updated_at)  
            VALUES ($1, $2, $3, $4, $5, $6, $7)  
            "#,  
            area.code,  
            area.name,  
            area.parent_code,  
            area.level,  
            area.is_active,  
            area.created_at,  
            area.updated_at,  
        )  
        .execute(self.pool.as_ref())  
        .await  
        .with_context(|| format!("Failed to insert area: {}", area.code))?;  

        for child in &node.children {  
            Box::pin(self.process_area_node(child)).await?;  
        }  

        Ok(())  
    }  

    pub async fn get_all_areas(&self) -> Result<Vec<SysAreaInfo>> {  
        sqlx::query_as!(  
            SysAreaInfo,  
            "SELECT code, name, parent_code, level, is_active as \"is_active!\", created_at, updated_at FROM sys_area_info ORDER BY code"  
        )  
        .fetch_all(self.pool.as_ref())  
        .await  
        .context("Failed to fetch areas")  
    }  

    pub async fn update_area_status(&self, code: &str,name: &str, is_active: bool) -> Result<()> {  
        sqlx::query!(  
            r#"  
            UPDATE sys_area_info   
            SET is_active = $1, name = $2, updated_at = $3  
            WHERE code = $4  
            "#,  
            is_active,  
            name,  
            Utc::now(),  
            code,  
        )  
        .execute(self.pool.as_ref())  
        .await  
        .with_context(|| format!("Failed to update area status for code: {}", code))?;  

        Ok(())  
    }  

    // 新增区划  
    pub async fn create_area(&self, area: SysAreaInfo) -> Result<()> {  
        sqlx::query!(  
            r#"  
            INSERT INTO sys_area_info (code, name, parent_code, level, is_active)  
            VALUES ($1, $2, $3, $4, $5)  
            "#,  
            area.code,  
            area.name,  
            area.parent_code,  
            area.level,  
            area.is_active  
        )  
        .execute(self.pool.as_ref())  
        .await  
        .context("Failed to create area")?;  
        Ok(())  
    }  
    
    // 更新区划信息  
    pub async fn update_area(&self, area: SysAreaInfo) -> Result<()> {  
        sqlx::query!(  
            r#"  
            UPDATE sys_area_info   
            SET name = $1, parent_code = $2, level = $3, is_active = $4, updated_at = $5  
            WHERE code = $6  
            "#,  
            area.name,  
            area.parent_code,  
            area.level,  
            area.is_active,  
            Utc::now(),  
            area.code  
        )  
        .execute(self.pool.as_ref())  
        .await  
        .context("Failed to update area")?;  
        Ok(())  
    }  
    
    // 删除区划  
    pub async fn delete_area(&self, code: &str) -> Result<()> {  
        sqlx::query!(  
            "DELETE FROM sys_area_info WHERE code = $1",  
            code  
        )  
        .execute(self.pool.as_ref())  
        .await      
        .context("Failed to delete area")?;  
        Ok(())  
    }  
    
    // 获取单个区划的子级单位信息  
    pub async fn get_area_by_code(&self, code: &str) -> Result<Vec<SysAreaInfo>> {  
        sqlx::query_as!(  
            SysAreaInfo,  
            "SELECT code, name, parent_code, level, is_active as \"is_active!\", created_at, updated_at   
            FROM sys_area_info WHERE parent_code = $1",  
            code  
        )  
        .fetch_all(self.pool.as_ref())  
        .await  
        .context("Failed to fetch area")  
    }      

    // 懒加载获取区划和机构数据
    pub async fn get_lazy_tree_node(&self, parent_code: Option<String>) -> Result<Vec<SysAreaInfo>> {
        match parent_code {
            None => {
                // 获取顶级区划（省级）
                sqlx::query_as!(
                    SysAreaInfo,
                    r#"
                    SELECT code, name, parent_code, level, is_active as "is_active!", created_at, updated_at
                    FROM sys_area_info 
                    WHERE level = 1 AND is_active = true
                    ORDER BY code
                    "#
                )
                .fetch_all(self.pool.as_ref())
                .await
                .context("Failed to fetch top level areas")
            }
            Some(code) => {
                // 获取下级区划
                sqlx::query_as!(
                    SysAreaInfo,
                    r#"
                    SELECT code, name, parent_code, level, is_active as "is_active!", created_at, updated_at
                    FROM sys_area_info 
                    WHERE parent_code = $1 AND is_active = true
                    ORDER BY code
                    "#,
                    code
                )
                .fetch_all(self.pool.as_ref())
                .await
                .context("Failed to fetch child areas")
            }
        }
    }

}  