//utils/excel_rtsp_source.rs

use crate::model::rtsp_model::{RtspSource, CameraSource};
use anyhow::{Context, Result};
use calamine::{open_workbook, Reader, Xlsx};
use std::collections::HashMap;

pub fn read_excel_and_build_rtsp(
    excel_filename: &str,
    sheet_name: &str,
    cameras_per_room: usize, // 每个房间的摄像头数量
) -> Result<Vec<RtspSource>> {
    // Open Excel file
    let mut workbook: Xlsx<_> = open_workbook(excel_filename)
        .with_context(|| format!("excel file {} not found", excel_filename))?;

    let mut rtsp_sources = Vec::new();
    let mut room_id_counter = 1;

    // 临时存储，用于按房间分组摄像头
    let mut temp_cameras: HashMap<String, Vec<(String, String, String)>> = HashMap::new();

    // Read worksheet range
    let range = workbook
        .worksheet_range(sheet_name)
        .with_context(|| format!("Failed to read worksheet {}", sheet_name))?;

    // Skip header, start from second row
    for row in range.rows().skip(1) {
        // Ensure row has at least 6 columns
        if row.len() >= 6 {
            // Extract values using pattern matching
            let (ip, username, password) =
                (row[2].to_string(), row[4].to_string(), row[5].to_string());

            // Construct RTSP URL
            let rtsp_url = format!(
                "rtsp://{}:{}@{}/streaming/channels/101",
                encode_auth_component(username.as_str()),
                encode_auth_component(password.as_str()),
                ip
            );

            // 计算房间ID（每cameras_per_room个摄像头一个房间）
            let room_id = format!("room_{:03}", (room_id_counter - 1) / cameras_per_room + 1);
            
            // 将摄像头信息添加到对应房间的临时存储中
            temp_cameras
                .entry(room_id)
                .or_insert_with(Vec::new)
                .push((
                    format!("camera_{:03}", room_id_counter),
                    ip.clone(),
                    rtsp_url,
                ));

            room_id_counter += 1;
        }
    }

    // 将临时存储转换为RtspSource结构
    for (room_id, cameras) in temp_cameras {
        let mut camera_sources = Vec::new();
        
        // 为每个摄像头创建CameraSource
        for (idx, (camera_id, ip, url)) in cameras.iter().enumerate() {
            let camera_source = CameraSource {
                camera_id: camera_id.clone(),
                camera_name: format!("Camera_{}", ip),
                camera_type: if idx % 2 == 0 { "panoramic".to_string() } else { "detail".to_string() },
                url: url.clone(),
            };
            camera_sources.push(camera_source);
        }

        // 创建RtspSource实例
        let source = RtspSource {
            room_id: room_id.clone(),
            room_name: format!("Room_{}", room_id),
            cameras: camera_sources,
        };

        rtsp_sources.push(source);
    }

    Ok(rtsp_sources)
}


// async fn check_rtsp_availability(source: &RtspSource) -> Result<bool, Box<dyn Error>> {
//     let connection_start = Instant::now();
//     let mut opts = ffmpeg::Dictionary::new();
//     opts.set("stimeout", "5000000");
//     opts.set("timeout", "5000000");
  
//     match input_with_dictionary(&source.url, opts) {
//         Ok(mut ictx) => {
//             // 尝试获取一帧数据来验证流是否真正可用
//             if let Some(stream) = ictx.streams().best(ffmpeg::media::Type::Video) {
//                 let _decoder = get_decoder(&stream)?;
//                 Ok(true)
//             } else {
//                 Ok(false)
//             }
//         }
//         Err(_) => Ok(false),
//     }
//   }

pub fn encode_auth_component(input: &str) -> String {
    let mut encoded = String::with_capacity(input.len() * 3);
    
    for c in input.chars() {
        match c {
            '@' => encoded.push_str("%40"),
            ':' => encoded.push_str("%3A"),
            '!' => encoded.push_str("%21"),
            '#' => encoded.push_str("%23"),
            '$' => encoded.push_str("%24"),
            '%' => encoded.push_str("%25"), 
            '&' => encoded.push_str("%26"),
            ' ' => encoded.push_str("%20"),
            '/' => encoded.push_str("%2F"),
            '?' => encoded.push_str("%3F"),
            '[' => encoded.push_str("%5B"),
            ']' => encoded.push_str("%5D"),
            '{' => encoded.push_str("%7B"),
            '}' => encoded.push_str("%7D"),
            '\\' => encoded.push_str("%5C"),
            '+' => encoded.push_str("%2B"),
            '=' => encoded.push_str("%3D"),
            '^' => encoded.push_str("%5E"),
            '|' => encoded.push_str("%7C"),
            _ => encoded.push(c),
        }
    }
    encoded
}

// use crate::RtspSource;
// use anyhow::{Context, Result};
// use calamine::{open_workbook, Reader, Xlsx};

// pub fn read_excel_and_build_rtsp(
//     excel_filename: &str,
//     sheet_name: &str,
// ) -> Result<Vec<RtspSource>> {
//     // Open Excel file
//     let mut workbook: Xlsx<_> = open_workbook(excel_filename)
//         .with_context(|| format!("excel file {} not found", excel_filename))?;

//     let mut rtsp_sources = Vec::new();
//     let mut room_id_counter = 1;

//     // Read worksheet range
//     let range = workbook
//         .worksheet_range(sheet_name)
//         .with_context(|| format!("Failed to read worksheet {}", sheet_name))?;

//     // Skip header, start from second row
//     for row in range.rows().skip(1) {
//         // Ensure row has at least 6 columns
//         if row.len() >= 6 {
//             // Extract values using pattern matching
//             let (ip, username, password) =
//                 (row[2].to_string(), row[4].to_string(), row[5].to_string());

//             // Construct RTSP URL
//             let rtsp_url = format!("rtsp://{}:{}@{}/streaming/channels/101", encode_auth_component(username.as_str()), encode_auth_component(password.as_str()), ip);

//             // Create RtspSource instance
//             let source = RtspSource {
//                 room_id: room_id_counter.to_string(),
//                 room_name: "".to_string(),
//                 camera_id: "".to_string(),
//                 camera_name: "".to_string(),
//                 camera_type: "".to_string(),
//                 url: rtsp_url,
//             };

//             rtsp_sources.push(source);
//             room_id_counter += 1;
//         }
//     }

//     Ok(rtsp_sources)
// }


// pub fn encode_auth_component(input: &str) -> String {
//     let mut encoded = String::with_capacity(input.len() * 3);
    
//     for c in input.chars() {
//         match c {
//             '@' => encoded.push_str("%40"),
//             ':' => encoded.push_str("%3A"),
//             '!' => encoded.push_str("%21"),
//             '#' => encoded.push_str("%23"),
//             '$' => encoded.push_str("%24"),
//             '%' => encoded.push_str("%25"), 
//             '&' => encoded.push_str("%26"),
//             ' ' => encoded.push_str("%20"),
//             '/' => encoded.push_str("%2F"),
//             '?' => encoded.push_str("%3F"),
//             '[' => encoded.push_str("%5B"),
//             ']' => encoded.push_str("%5D"),
//             '{' => encoded.push_str("%7B"),
//             '}' => encoded.push_str("%7D"),
//             '\\' => encoded.push_str("%5C"),
//             '+' => encoded.push_str("%2B"),
//             '=' => encoded.push_str("%3D"),
//             '^' => encoded.push_str("%5E"),
//             '|' => encoded.push_str("%7C"),
//             _ => encoded.push(c),
//         }
//     }
//     encoded
// }