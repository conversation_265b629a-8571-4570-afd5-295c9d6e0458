sudo yum install --downloadonly --downloaddir=/data/packages  \
    zlib-devel bzip2-devel libxml2-devel libxslt-devel \
    libffi-devel readline-devel sqlite-devel \
    openssl-devel libjpeg-turbo-devel libpng-devel \
    freetype-devel libass-devel lame-devel opus-devel \
    libvorbis-devel libtheora-devel libvpx-devel \
    libx264-devel libx265-devel libaom-devel \
    fdk-aac-devel SDL2-devel -y


sudo yum install --downloadonly --downloaddir=/data/packages  \
    nano wget curl net-tools htop screen python3 pip3 

sudo yum localinstall --nogpgcheck https://download1.rpmfusion.org/free/el/rpmfusion-free-release-7.noarch.rpm
sudo yum install --downloadonly --downloaddir=/data/packages   nano wget curl net-tools htop screen python3 pip3 glibc-devel  x264-devel x265-devel  openjpeg2-devel openjpeg2 nasm libaom-devel.x86_64 libass libass-devel opus opus-devel ffmpeg ffmpeg-devel clang clang-devel llvm-devel

sudo yum install clang clang-devel llvm-devel


sudo apt-get install pkg-config libssl-dev
sudo apt-get install musl-tools

cd /data/openssl/openssl-3.4.0
./Configure
make
sudo make install
export OPENSSL_DIR=/data/openssl/openssl-3.4.0


sudo nano /etc/ld.so.conf.d/openssl.conf
/usr/local/lib64
sudo ldconfig


ffmpeg 编译配置
PKG_CONFIG_PATH="/usr/local/lib64/pkgconfig" ./configure --enable-pthreads  --enable-gpl --enable-nonfree  --enable-shared --enable-libx264 --enable-libx265 --enable-libwebp --enable-libvpx  --enable-openssl --enable-libopenh264  --enable-libopenjpeg


export PKG_CONFIG_ALLOW_CROSS=1
export PKG_CONFIG_PATH=/path/to/cross/compiled/openssl/lib/pkgconfig

cargo build --target x86_64-unknown-linux-musl --release