//src/webrtc/service/webrtc_overlay_service.rs
use ffmpeg_next as ffmpeg;
use anyhow::Result;
use crate::webrtc::model::webrtc_stream::ShapeOverlay;

#[derive(Clone, Debug)]
pub struct WebrtcOverlayService;


impl WebrtcOverlayService {
    pub fn new() -> Self {
        Self {}
    }

    pub fn draw_text(
        &self,
        frame: &mut ffmpeg::frame::Video,
        text: &str,
        x: i32,
        y: i32,
        font_size: i32,
        color: &str,
    ) -> Result<()> {
        let mut filter_graph = ffmpeg::filter::graph::Graph::new();
        
        // 创建输入缓冲源
        let args = format!(
            "width={}:height={}:pix_fmt={}",
            frame.width(),
            frame.height(),
            "yuv420p"
        );
        let _buffer = filter_graph.add(&ffmpeg::filter::find("buffer").unwrap(), "in", &args)?;
        
        // 创建drawtext过滤器
        let args = format!(
            "text='{}':x={}:y={}:fontsize={}:fontcolor={}",
            text, x, y, font_size, color
        );
        let _drawtext = filter_graph.add(&ffmpeg::filter::find("drawtext").unwrap(), "text", &args)?;
        
        // 创建输出缓冲接收器
        let _buffersink = filter_graph.add(
            &ffmpeg::filter::find("buffersink").unwrap(),
            "out",
            ""
        )?;
        
        // 使用parse方法连接过滤器
        let spec = format!("[in]drawtext[out]");
        filter_graph.parse(&spec)?;
        
        // 配置图形
        filter_graph.validate()?;
        
        // 处理帧
        let mut in_ctx = filter_graph.get("in").unwrap();
        let mut source = in_ctx.source();
        source.add(frame)?;
        
        let mut out_ctx = filter_graph.get("out").unwrap();
        let mut sink = out_ctx.sink();
        let mut out_frame = ffmpeg::frame::Video::empty();
        if sink.frame(&mut out_frame).is_ok() {
            frame.clone_from(&out_frame);
        }
        
        Ok(())
    }

    pub fn draw_shape(
        &self,
        frame: &mut ffmpeg::frame::Video,
        shape: &ShapeOverlay,
    ) -> Result<()> {
        let mut filter_graph = ffmpeg::filter::graph::Graph::new();
        
        // 创建输入缓冲源
        let args = format!(
            "width={}:height={}:pix_fmt={}",
            frame.width(),
            frame.height(),
            "yuv420p"
        );
        let _buffer = filter_graph.add(&ffmpeg::filter::find("buffer").unwrap(), "in", &args)?;
        
        // 根据形状类型创建相应的过滤器
        let (filter_name, args) = match shape.shape_type.as_str() {
            "rectangle" => {
                if shape.points.len() >= 2 {
                    let (x1, y1) = (shape.points[0].x, shape.points[0].y);
                    let (x2, y2) = (shape.points[1].x, shape.points[1].y);
                    (
                        "drawbox",
                        format!(
                            "x={}:y={}:w={}:h={}:color={}:thickness=2",
                            x1, y1, x2-x1, y2-y1, shape.color
                        )
                    )
                } else {
                    return Ok(());
                }
            },
            "polygon" => {
                if shape.points.len() >= 3 {
                    let points = shape.points.iter()
                        .map(|p| format!("{}_{}", p.x, p.y))
                        .collect::<Vec<_>>()
                        .join(",");
                    (
                        "drawpolygon",
                        format!(
                            "points='{}':color={}:thickness=2",
                            points, shape.color
                        )
                    )
                } else {
                    return Ok(());
                }
            },
            _ => return Ok(()),
        };
        
        let _shape_filter = filter_graph.add(
            &ffmpeg::filter::find(filter_name).unwrap(),
            "shape",
            &args
        )?;
        
        // 创建输出缓冲接收器
        let _buffersink = filter_graph.add(
            &ffmpeg::filter::find("buffersink").unwrap(),
            "out",
            ""
        )?;
        
        // 使用parse方法连接过滤器
        let spec = format!("[in]{}[out]", filter_name);
        filter_graph.parse(&spec)?;
        
        // 配置图形
        filter_graph.validate()?;
        
        // 处理帧
        let mut in_ctx = filter_graph.get("in").unwrap();
        let mut source = in_ctx.source();
        source.add(frame)?;
        
        let mut out_ctx = filter_graph.get("out").unwrap();
        let mut sink = out_ctx.sink();
        let mut out_frame = ffmpeg::frame::Video::empty();
        if sink.frame(&mut out_frame).is_ok() {
            frame.clone_from(&out_frame);
        }
        
        Ok(())
    }
}