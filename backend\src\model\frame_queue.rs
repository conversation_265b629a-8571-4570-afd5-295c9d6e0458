//model/frame_queue.rs
use crossbeam_queue::ArrayQueue;
use std::sync::Arc;

use crate::model::rtsp_model::RoomFrame;

// 全局帧队列
#[derive(Clone, Debug)]
pub struct FrameQueue {
    pub queue: Arc<ArrayQueue<RoomFrame>>,
}

impl FrameQueue {
    pub fn new(capacity: usize) -> Self {
        FrameQueue {
            queue: Arc::new(ArrayQueue::new(capacity)),
        }
    }

    pub fn push(&self, frame: RoomFrame) -> bool {
        match self.queue.push(frame) {
            Ok(_) => true,
            Err(_) => false,
        }
    }

    pub fn pop(&self) -> Option<RoomFrame> {
        self.queue.pop()
    }

    pub fn len(&self) -> usize {
        self.queue.len()
    }

    pub fn capacity(&self) -> usize {
        self.queue.capacity()
    }

    pub fn usage_percentage(&self) -> f32 {
        self.len() as f32 / self.capacity() as f32 * 100.0
    }
}
