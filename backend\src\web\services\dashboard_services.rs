use crate::web::model::dashboard::*;
use anyhow::Result;
use sqlx::{Pool, Postgres};
use std::sync::Arc;

#[derive(Clone, Debug)]
pub struct DashboardService {
    pub pool: Arc<Pool<Postgres>>,
}

impl DashboardService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    pub async fn get_statistic(&self, login_user_id: &str) -> Result<StatisticResponse> {
        let base_sql = "SELECT COUNT(*) FROM camera c 
            inner join location l on c.location_id = l.location_id 
            inner join building b on l.building_id = b.building_id 
            inner join office_organization oo on b.office_id = oo.office_id 
            ";
        log::info!("login_user_id: {}", login_user_id);
        let base_sql = match login_user_id {
            "ADMIN_001" => format!("{} where '1'<> $1", base_sql),
            _ => format!("{} inner join sys_user su on oo.office_id = su.organization_id WHERE c.is_active = true and c.status = 'active' and su.user_id = $1", base_sql)
        };

        let camera_count = sqlx::query_scalar::<_, i64>(&base_sql)
            .bind(login_user_id)
            .fetch_one(&*self.pool)
            .await?;

        //查询有多少房间
        let base_sql = "SELECT COUNT(*) FROM location l
            inner join building b on l.building_id = b.building_id 
            inner join office_organization oo on b.office_id = oo.office_id 
            ";
        let room_sql = match login_user_id {
            "ADMIN_001" => format!("{} where '1'<> $1", base_sql),
            _ => format!("{} inner join sys_user su on oo.office_id = su.organization_id WHERE l.is_active = true and su.user_id = $1", base_sql)
        };
        let room_count = sqlx::query_scalar::<_, i64>(&room_sql)
            .bind(login_user_id)
            .fetch_one(&*self.pool)
            .await?;

        //查询最近1小时报警
        let base_sql =
            "SELECT COUNT(*) FROM alarm_record WHERE alarm_time > now() - interval '1 hour'";
        let hourly_alerts = sqlx::query_scalar::<_, i64>(&base_sql)
            .fetch_one(&*self.pool)
            .await?;

        //查询最近24小时报警
        let base_sql =
            "SELECT COUNT(*) FROM alarm_record WHERE alarm_time > now() - interval '24 hour'";
        let daily_alerts = sqlx::query_scalar::<_, i64>(&base_sql)
            .fetch_one(&*self.pool)
            .await?;

        Ok(StatisticResponse {
            camera_count: camera_count,
            room_count: room_count,
            hourly_alerts: hourly_alerts,
            daily_alerts: daily_alerts,
        })
    }

    pub async fn get_image_alarm(&self) -> Result<Vec<ImageAlarmResponse>> {
        Ok(vec![])
    }
}
