<template>
    <div class="container mx-auto px-4 py-8">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <!-- Detection Mode Configuration -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">检测模式配置</h2>
          
          <el-tabs v-model="activeMode" class="mb-6">
            <!-- Task-based Polling Mode -->
            <el-tab-pane label="任务轮询模式" name="taskBased">
              <div class="p-4 bg-gray-50 rounded-lg">
                <el-form label-position="top">
                  <el-form-item label="并行任务数">
                    <el-input-number 
                      v-model="config.taskMode.concurrentTasks" 
                      :min="1" 
                      :max="10"
                      class="w-32"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
  
            <!-- Room Type Mode -->
            <el-tab-pane label="房间类型模式" name="roomBased">
              <div class="p-4 bg-gray-50 rounded-lg">
                <el-form label-position="top">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <el-form-item label="候问室检测间隔(秒)">
                      <el-input-number v-model="config.roomMode.waitingRoom" :min="1"/>
                    </el-form-item>
                    <el-form-item label="辨认室检测间隔(秒)">
                      <el-input-number v-model="config.roomMode.identificationRoom" :min="1"/>
                    </el-form-item>
                    <el-form-item label="讯询问室检测间隔(秒)">
                      <el-input-number v-model="config.roomMode.interrogationRoom" :min="1"/>
                    </el-form-item>
                    <el-form-item label="其他房间检测间隔(秒)">
                      <el-input-number v-model="config.roomMode.otherRooms" :min="1"/>
                    </el-form-item>
                  </div>
                </el-form>
              </div>
            </el-tab-pane>
  
            <!-- Individual Camera Mode -->
            <el-tab-pane label="单摄像头模式" name="cameraBased">
              <div class="p-4 bg-gray-50 rounded-lg">
                <el-alert
                  type="warning"
                  show-icon
                  class="mb-4"
                  :closable="false"
                >
                  注意：此模式不适合大量摄像头同时检测，可能导致CPU负载过高
                </el-alert>
                <el-form label-position="top">
                  <el-table :data="config.cameraMode.cameras" border>
                    <el-table-column label="摄像头" prop="name"/>
                    <el-table-column label="检测间隔(秒)" width="200">
                      <template #default="scope">
                        <el-input-number v-model="scope.row.interval" :min="1"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="启用检测" width="120">
                      <template #default="scope">
                        <el-switch v-model="scope.row.enabled"/>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
  
        <!-- Detection Result Configuration -->
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">检测结果配置</h2>
          <div class="p-4 bg-gray-50 rounded-lg">
            <el-form label-position="top">
              <el-form-item label="YOLO人物预检">
                <el-switch v-model="config.result.enableYoloPrescreening"/>
                <span class="ml-2 text-gray-600 text-sm">启用后将先检测人物再进行AI分析</span>
              </el-form-item>
  
              <el-form-item label="结果输出方式">
                <el-radio-group v-model="config.result.outputMode">
                  <el-radio label="storage">本地存储</el-radio>
                  <el-radio label="api">外部AI接口</el-radio>
                </el-radio-group>
              </el-form-item>
  
              <el-form-item 
                label="外部API地址" 
                v-if="config.result.outputMode === 'api'"
              >
                <el-input v-model="config.result.apiEndpoint" placeholder="请输入API地址"/>
              </el-form-item>
  
              <el-form-item label="异常报警推送">
                <el-switch v-model="config.result.enableAlarmPush"/>
              </el-form-item>
  
              <el-form-item 
                label="报警推送地址" 
                v-if="config.result.enableAlarmPush"
              >
                <el-input v-model="config.result.alarmEndpoint" placeholder="请输入推送地址"/>
              </el-form-item>
            </el-form>
          </div>
        </div>
  
        <!-- Save Button -->
        <div class="flex justify-end">
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive } from 'vue'
  
  const activeMode = ref('taskBased')
  
  const config = reactive({
    taskMode: {
      concurrentTasks: 2
    },
    roomMode: {
      waitingRoom: 30,
      identificationRoom: 30,
      interrogationRoom: 20,
      otherRooms: 60
    },
    cameraMode: {
      cameras: [
        { name: '摄像头1', interval: 30, enabled: true },
        { name: '摄像头2', interval: 30, enabled: false }
      ]
    },
    result: {
      enableYoloPrescreening: true,
      outputMode: 'storage',
      apiEndpoint: '',
      enableAlarmPush: false,
      alarmEndpoint: ''
    }
  })
  
  const saveConfig = () => {
    // 实现保存配置的逻辑
    ElMessage.success('配置已保存')
  }
  </script>
  
  <style scoped>
  .el-input-number {
    width: 100%;
  }
  </style>
  