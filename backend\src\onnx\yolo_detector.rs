use crate::config::Config;
use anyhow::Result;
use async_trait::async_trait;
use image::DynamicImage;
use serde::Serialize;
use std::fmt::Display;
use std::path::Path;
use std::sync::Arc;


// 定义统一的检测结果结构体
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
pub struct DetectionBox {
    pub class_name: String, // 类别名称
    pub confidence: f32,    // 置信度 0-1
    pub bbox: [f32; 4],     // 检测框 [x1, y1, x2, y2]
}

// 定义统一的检测结果
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct ModelDetectionResult {
    pub num_persons: usize,              // 检测到的人数
    pub boxes: Vec<DetectionBox>,        // 检测框
    pub processing_time_ms: u128,        // 处理时间
    pub annotated_image: Option<String>, // 标注图像
}

// 定义模型配置结构体
#[derive(Debug, <PERSON><PERSON>)]
pub struct ModelConfig {
    pub config: Arc<Config>,
    pub confidence_threshold: f32, // 置信度阈值
    pub iou_threshold: f32,        // 非极大值抑制阈值
    pub input_width: u32,          // 输入图像宽度
    pub input_height: u32,         // 输入图像高度
}

// 定义统一的模型接口
#[async_trait]

pub trait ObjectDetector: Send + Sync {
    // 初始化模型
    async fn init(config: ModelConfig) -> Result<Self>
    where
        Self: Sized;

    // 从内存中的图像数据进行检测
    async fn detect_from_memory(&self, image_data: &[u8]) -> Result<ModelDetectionResult>;

    // 从DynamicImage进行检测
    async fn detect_from_image(&self, image: &DynamicImage) -> Result<ModelDetectionResult>;

    // 从文件路径进行检测
    async fn detect_from_file(&self,image_path:&Path) -> Result<ModelDetectionResult>;

    // 获取模型配置
    fn get_config(&self) -> &ModelConfig;
}

impl Display for ModelDetectionResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "num_persons: {}, boxes: {:?}, processing_time_ms: {}", self.num_persons, self.boxes, self.processing_time_ms)
    }
}