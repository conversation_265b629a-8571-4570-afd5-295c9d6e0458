<!-- src/views/config/BuildingManagement.vue -->
<template>
    <div class="flex h-full">
      <!-- 左侧树形区域 -->
      <div class="w-1/4 p-4 border-r overflow-hidden flex flex-col">
        <div class="mb-4">
          <el-input
            v-model="treeFilterText"
            placeholder="搜索机构"
            clearable
            prefix-icon="Search"
          />
        </div>
        <div class="flex-1 overflow-auto">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            :load="loadNode"
            lazy
            :filter-node-method="filterNode"
            node-key="id"
            highlight-current
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span class="flex items-center">
                <el-icon class="mr-1">
                  <Location v-if="data.type === 'area'" />
                  <OfficeBuilding v-else />
                </el-icon>
                <span>{{ data.name }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
  
      <!-- 右侧内容区域 -->
      <div class="flex-1 p-4 overflow-hidden flex flex-col">
        <!-- 顶部操作栏 -->
        <div class="mb-4 flex justify-between items-center">
          <div class="flex space-x-2">
            <el-button type="primary" @click="handleAdd" :disabled="!isAddEnable">新增楼宇</el-button>
            <el-button
              type="danger"
              :disabled="selectedRows.length === 0"
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
          </div>
          <div class="flex space-x-2">
            <el-input
              v-model="searchText"
              placeholder="搜索楼宇名称"
              class="w-64"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
  
        <!-- 当前位置显示 -->
        <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>
  
        <!-- 数据表格 -->
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          border
          stripe
          class="flex-1"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="building_name" label="楼宇名称" min-width="120" />
          <el-table-column prop="floors" label="楼层数" width="100" align="center" />
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-switch
                v-model="row.is_active"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" type="primary" @click="handleEdit(row)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" @click="handleDelete(row)">
                  删除
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
  
        <!-- 分页 -->
        <div class="mt-4 flex justify-end">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="楼宇名称" prop="building_name">
            <el-input v-model="formData.building_name" />
          </el-form-item>
          <el-form-item label="楼层数" prop="floors">
            <el-input-number v-model="formData.floors" :min="1" :max="200" />
          </el-form-item>
          <el-form-item label="所属机构">
            <el-input
              v-model="formData.organization_name"
              disabled
              placeholder="请在左侧树中选择机构"
            />
          </el-form-item>
          <el-form-item label="状态" v-if="formData.building_id">
            <el-switch v-model="formData.is_active" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, watch, onMounted } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { Search, OfficeBuilding } from "@element-plus/icons-vue";
  import { format } from "date-fns";
  import axios from "@/utils/axios";
  
  // 树相关
  const treeRef = ref(null);
  const treeData = ref([]);
  const treeFilterText = ref("");
  const currentNode = ref(null);
  const treeProps = {
    label: "name",
    children: "children",
    isLeaf: "leaf",
  };
  
  // 表格相关
  const loading = ref(false);
  const tableRef = ref(null);
  const tableData = ref([]);
  const selectedRows = ref([]);
  const searchText = ref("");
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const isAddEnable = ref(true);
  
  // 表单相关
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增楼宇");
  const formRef = ref(null);
  const formData = ref({
    building_id: "",
    building_name: "",
    floors: 1,
    office_id: "",
    organization_name: "",
    is_active: true,
  });
  
  const formRules = {
    building_name: [
      { required: true, message: "请输入楼宇名称", trigger: "blur" },
      { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
    ],
    floors: [
      { required: true, message: "请输入楼层数", trigger: "blur" },
      { type: "number", min: 1, max: 200, message: "楼层数在 1 到 200 之间", trigger: "blur" },
    ],
  };
  
  // 计算属性
  const currentPath = computed(() => {
    if (!currentNode.value) return "全部";
    return getNodePath(currentNode.value);
  });
  

// 懒加载树节点
const loadNode = async (node, resolve) => {
  try {
    if (node.level === 0) {
      // 加载省级行政区划数据
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map((item) => ({
        ...item,
        id: item.code,
        type: "area",
        name: item.name,
        leaf: false,
      }));
      resolve(areas);
    } else {
      const { data } = node;
      if (data.type === "area") {
        // 并行加载下级区划和机构
        const [areaRes, orgRes] = await Promise.all([
          axios.get("/sys_area_info/lazy_tree_node", {
            params: { parent_code: data.code },
          }),
          axios.get(`/office_organization/list/${data.code}`),
        ]);

        const areas = areaRes.data.data.map((item) => ({
          ...item,
          id: item.code,
          type: "area",
          name: item.name,
          // 根据级别判断是否还有子节点
          leaf: item.level >= 4, // 假设区划最多4级
        }));

        const orgs = orgRes.data.data.map((item) => ({
          id: item.office_id,
          type: "org",
          name: item.office_name,
          leaf: false, // 机构下面可能有楼宇
          // 保存原始数据
          office_id: item.office_id,
          office_name: item.office_name,
          office_address: item.office_address,
          area_code: item.area_code,
        }));

        resolve([...areas, ...orgs]);
      // } else if (data.type === "org") {
        // // 加载机构下的楼宇
        // const buildingRes = await axios.get(`/building/list`, {
        //   params: {
        //     office_id: data.office_id,
        //     page_size: 1000, // 获取全部楼宇
        //   },
        // });

        // const buildings = buildingRes.data.data.items.map((item) => ({
        //   id: item.building_id,
        //   type: "building",
        //   name: item.building_name,
        //   leaf: true,
        //   // 保存原始数据
        //   building_id: item.building_id,
        //   building_name: item.building_name,
        //   floors: item.floors,
        //   office_id: item.office_id,
        // }));

        // resolve(buildings);
      } else {
        resolve([]);
      }
    }
  } catch (error) {
    console.error("加载树节点失败:", error);
    ElMessage.error("加载数据失败");
    resolve([]);
  }
};
  
  // 树节点过滤
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.name.toLowerCase().includes(value.toLowerCase());
  };
  
  // 获取节点路径
  const getNodePath = (node) => {
    const path = [];
    let current = node;
    while (current.parent && current.parent.level !== 0) {
      path.unshift(current.label);
      current = current.parent;
    }
    path.unshift(current.label);
    return path.join(" / ");
  };
  
  // 节点点击处理
  const handleNodeClick = (data, node) => {
    currentNode.value = node;
    formData.value.office_id = data.id;
    formData.value.organization_name = data.name;
    //如果选中的不是组织机构，新增按钮不可操作
    if (data.type !== "org") {
      formData.value.office_id = "";
      formData.value.organization_name = "";
      formData.value.is_active = true;
      isAddEnable.value = false;
    } else {
      isAddEnable.value = true;
    }
    loadTableData();
  };
  
  // 加载表格数据
  const loadTableData = async () => {
    loading.value = true;
    try {
      const res = await axios.get("/building/list", {
        params: {
          page: currentPage.value,
          page_size: pageSize.value,
          office_id: formData.value.office_id,
          search_text: searchText.value,
        },
      });
      tableData.value = res.data.data.items;
      total.value = res.data.data.total;
    } catch (error) {
      ElMessage.error("加载楼宇数据失败");
    } finally {
      loading.value = false;
    }
  };
  
  // 表格选择变化
  const handleSelectionChange = (rows) => {
    selectedRows.value = rows;
  };
  
  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1;
    loadTableData();
  };
  
  // 重置搜索
  const handleReset = () => {
    searchText.value = "";
    currentPage.value = 1;
    loadTableData();
  };
  
  // 分页大小变化
  const handleSizeChange = (val) => {
    pageSize.value = val;
    loadTableData();
  };
  
  // 页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val;
    loadTableData();
  };
  
  // 新增楼宇
  const handleAdd = () => {
    if (!formData.value.office_id) {
      ElMessage.warning("请先在左侧选择一个机构");
      return;
    }
    dialogTitle.value = "新增楼宇";
    formData.value = {
      building_id: "",
      building_name: "",
      floors: 1,
      office_id: formData.value.office_id,
      organization_name: formData.value.organization_name,
      is_active: true,
    };
    dialogVisible.value = true;
  };
  
  // 编辑楼宇
  const handleEdit = (row) => {
    dialogTitle.value = "编辑楼宇";
    formData.value = {
      ...row,
      organization_name: currentNode.value?.data?.name || "",
    };
    dialogVisible.value = true;
  };
  
  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;
  
    try {
      await formRef.value.validate();
  
      if (formData.value.building_id) {
        // 编辑
        const res = await axios.put("/building/update", {
          building_id: formData.value.building_id,
          building_name: formData.value.building_name,
          floors: formData.value.floors,
          office_id: formData.value.office_id,
          is_active: formData.value.is_active,
        });
        if (res.data.code === 200) {
          ElMessage.success("更新成功");
        } else {
          ElMessage.error(res.data.message);
        }
      } else {
        // 新增，等待后台返回数据并判断是否创建成功
        const res = await axios.post("/building/create", {
          building_name: formData.value.building_name,
          floors: formData.value.floors,
          office_id: formData.value.office_id,
        });
        if (res.data.code === 200) {
          ElMessage.success("创建成功");
        } else {
          ElMessage.error(res.data.message);
        }
      }
  
      dialogVisible.value = false;
      loadTableData();
    } catch (error) {
      if (error?.message) {
        ElMessage.error(error.message);
      } else {
        ElMessage.error("操作失败");
      }
    }
  };
  
  // 删除楼宇
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确定要删除该楼宇吗？此操作不可恢复！", "警告", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      });
  
      await axios.delete(`/building/delete/${row.building_id}`);
      ElMessage.success("删除成功");
      loadTableData();
    } catch (error) {
      if (error !== "cancel") {
        ElMessage.error("删除失败");
      }
    }
  };
  
  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRows.value.length === 0) return;
  
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 个楼宇吗？此操作不可恢复！`,
        "警告",
        {
          type: "warning",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }
      );
  
      await Promise.all(
        selectedRows.value.map((row) =>
          axios.delete(`/building/delete/${row.building_id}`)
        )
      );
  
      ElMessage.success("批量删除成功");
      loadTableData();
    } catch (error) {
      if (error !== "cancel") {
        ElMessage.error("批量删除失败");
      }
    }
  };
  
  // 状态变更
  const handleStatusChange = async (row) => {
    try {
      await axios.put("/building/update", {
        building_id: row.building_id,
        is_active: row.is_active,
      });
      ElMessage.success("状态更新成功");
    } catch (error) {
      row.is_active = !row.is_active;
      ElMessage.error("状态更新失败");
    }
  };
  
  // 格式化日期时间
  const formatDateTime = (date) => {
    if (!date) return "-";
    return format(new Date(date), "yyyy-MM-dd HH:mm:ss");
  };
  
  // 监听器
  watch(treeFilterText, (val) => {
    treeRef.value?.filter(val);
  });
  
  // 生命周期钩子
  onMounted(() => {
    loadTableData();
  });
  </script>
  
  <style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

.el-dialog {
  max-width: 90%;
}

:deep(.el-table) {
  height: 100%;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 表格内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
}

/* 按钮组样式 */
:deep(.el-button-group) {
  display: flex;
  gap: 4px;
}

/* 分页器样式 */
:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 16px 0;
}

/* 搜索框样式 */
.search-input {
  width: 240px;
}

/* 表单项样式 */
:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

/* 对话框表单样式 */
:deep(.el-dialog__body) {
  padding: 20px 40px;
}

/* 开关按钮样式 */
:deep(.el-switch) {
  margin: 0 8px;
}

/* 树形控件滚动条样式 */
.el-tree::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-tree::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

.el-tree::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #dcdfe6;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f7fa;
}

/* 响应式布局
@media screen and (max-width: 1200px) {
  .flex {
    flex-direction: column;
  }
  
  .w-1/4 {
    width: 100%;
    height: 300px;
    margin-bottom: 16px;
  }
  
  .border-r {
    border-right: none;
    border-bottom: 1px solid #dcdfe6;
  }
} */

/* 加载状态样式 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行hover效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}

/* 表格选中行样式 */
:deep(.el-table__row.current-row) {
  background-color: #ecf5ff !important;
}

/* 按钮禁用状态样式 */
:deep(.el-button.is-disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 输入框focus状态样式 */
:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

/* 必填项星号样式 */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}

/* 错误提示样式 */
:deep(.el-form-item.is-error .el-input__inner) {
  border-color: #f56c6c;
}

:deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 2px;
}

/* 对话框动画效果 */
:deep(.el-dialog) {
  transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.el-dialog__wrapper) {
  transition: opacity 0.3s;
}

/* 树节点展开/收起动画 */
:deep(.el-tree-node__children) {
  transition: all 0.3s;
}

/* 工具提示样式 */
:deep(.el-tooltip__popper) {
  max-width: 300px;
}

/* 分割线样式 */
.divider {
  height: 1px;
  background-color: #dcdfe6;
  margin: 16px 0;
}

/* 卡片式布局样式 */
.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>