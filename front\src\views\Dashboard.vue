<!-- src/views/Dashboard.vue -->
<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div v-for="(item, index) in statItems" :key="index" 
           class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div :class="`p-3 bg-${item.color}-100 rounded-full`">
            <component :is="item.icon" :class="`text-${item.color}-600 text-xl`" />
          </div>
          <div class="ml-4">
            <h3 class="text-gray-500 text-sm">{{ item.label }}</h3>
            <p class="text-2xl font-semibold">{{ stats[item.key] }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近报警图片轮播 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4">最近报警截图</h2>
      <el-carousel :interval="4000" type="card" height="300px">
        <el-carousel-item v-for="alert in recentAlerts" :key="alert.id">
          <div class="relative h-full">
            <img :src="alert.image" class="w-full h-full object-cover rounded">
            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
              <p>{{ alert.time }}</p>
              <p>{{ alert.description }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 异常趋势图表 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold mb-4">最近30天异常趋势</h2>
      <div ref="chartRef" class="w-full h-[400px]"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import axios from '@/utils/axios'
import {
  CameraOutlined,
  HomeOutlined,
  AlertOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'

const stats = ref({
  cameraCount: 0,
  roomCount: 0,
  hourlyAlerts: 0,
  dailyAlerts: 0
})

const recentAlerts = ref([])
const chartRef = ref(null)

const statItems = [
  { label: '摄像头总数', key: 'cameraCount', icon: CameraOutlined, color: 'blue' },
  { label: '房间总数', key: 'roomCount', icon: HomeOutlined, color: 'green' },
  { label: '最近1小时报警', key: 'hourlyAlerts', icon: AlertOutlined, color: 'red' },
  { label: '24小时报警', key: 'dailyAlerts', icon: WarningOutlined, color: 'yellow' }
]

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await axios.get('/dashboard/statistic')
    //console.log('HTTP状态码:', response.status)
    //console.log('完整响应:', response)    
    if (response.status === 200 && response.data) {
      stats.value = {
        cameraCount: response.data.data.camera_count,
        roomCount: response.data.data.room_count,
        hourlyAlerts: response.data.data.hourly_alerts,
        dailyAlerts: response.data.data.daily_alerts
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最近报警图片
const fetchRecentAlerts = async () => {
  try {
    // const { data } = await axios.get('/api/dashboard/recent-alerts')
    //先从本地生成模拟数据
    recentAlerts.value = Array.from({length: 10}, (_, i) => ({
      id: i,
      image: '/demo_1.jpg',
      time: new Date().toLocaleString(),
      description: "这是一条报警描述",
    }))
  } catch (error) {
    console.error('获取报警图片失败:', error)
  }
}

// 初始化异常趋势图表
const initChart = () => {
  const chart = echarts.init(chartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: 30}, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - i)
        return date.toLocaleDateString()
      }).reverse()
    },
    yAxis: {
      type: 'value',
      name: '报警次数'
    },
    series: [{
      data: Array.from({length: 30}, () => Math.floor(Math.random() * 100)),
      type: 'line',
      smooth: true,
      areaStyle: {}
    }]
  }
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => chart.resize())
}

onMounted(async () => {
  await Promise.all([
    fetchStats(),
    fetchRecentAlerts()
  ])
  initChart()
})
</script>
