// rtsp/frame_capture_trait.rs
use anyhow::Result;
use std::sync::Arc;
use crate::model::rtsp_model::{RoomFrame, RtspSource};
use crate::model::frame_queue::FrameQueue;
use crate::onnx::yolo_detector::ObjectDetector;


/// 帧捕获器trait
pub trait FrameCaptureTriat {
    fn new(
        worker_id: usize,
        room_source: RtspSource,
        frame_queue: Option<Arc<FrameQueue>>,
        detector: Option<Arc<dyn ObjectDetector + Send + Sync>>,
    ) -> Result<Self>
    where
        Self: Sized;

    async fn capture_frames(&self) -> Result<Option<RoomFrame>>;
}