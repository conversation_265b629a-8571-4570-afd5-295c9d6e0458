//src/web/model/sys_role.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct SysRoleEntity {
    pub role_id: String,
    pub role_name: String,
    pub role_description: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleCreateRequest {
    pub role_name: String,
    pub role_description: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleUpdateRequest {
    pub role_id: String,
    pub role_name: String,
    pub role_description: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RolePermissionRequest {
    pub role_id: String,
    pub permission_ids: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageRequest {
    pub page: i64,
    pub page_size: i64,
    pub search_text: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageResponse<T> {
    pub records: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
}