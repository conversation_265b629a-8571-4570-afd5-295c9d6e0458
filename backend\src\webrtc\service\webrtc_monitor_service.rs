// //src/webrtc/service/webrtc_monitor_service.rs
// use std::sync::Arc;
// use tokio::time::{interval, Duration};
// use sysinfo::{System, CpuRefreshKind, RefreshKind};
// use crate::webrtc::model::webrtc_stream::WebrtcStreamState;
// use crate::webrtc::service::webrtc_stream_service::WebrtcStreamService;

// #[derive(Debug)]
// pub struct WebrtcMonitorService {
//     stream_state: Arc<WebrtcStreamState>,
//     stream_service: Arc<WebrtcStreamService>,
//     system: System,
// }

// impl WebrtcMonitorService {
//     pub fn new(stream_state: Arc<WebrtcStreamState>, stream_service: Arc<WebrtcStreamService>) -> Self {
//         // 只初始化需要的监控项
//         let sys = System::new_with_specifics(RefreshKind::default().with_cpu(CpuRefreshKind::everything()));
        
//         Self {
//             stream_state,
//             stream_service,
//             system: sys,
//         }
//     }

//     pub async fn start_monitoring(mut self) {
//         let stream_state = self.stream_state.clone();
//         let mut interval = interval(Duration::from_secs(60));
        
//         tokio::spawn(async move {
//             loop {
//                 interval.tick().await;
                
//                 // 更新系统信息
//                 self.system.refresh_cpu_all();
                
//                 // 获取CPU使用率
//                 let cpu_usage = self.system.global_cpu_usage();
                
//                 // 如果CPU使用率过高，清理闲置流
//                 if cpu_usage > 80.0 {
//                     self.cleanup_idle_streams().await;
//                 }
                
//                 // 记录监控指标
//                 log::info!(
//                     "System monitoring - CPU: {:.2}%, Active streams: {}",
//                     cpu_usage,
//                     self.get_active_stream_count().await
//                 );
//             }
//         });
//     }

//     async fn cleanup_idle_streams(&self) {
//         let active_streams = self.stream_state.active_streams.read().await;
//         let idle_streams: Vec<String> = active_streams
//             .iter()
//             .filter(|(_, info)| info.viewer_count <= 0)
//             .map(|(id, _)| id.clone())
//             .collect();
        
//         for stream_id in idle_streams {
//             if let Err(e) = self.stream_service.stop_stream(&stream_id).await {
//                 log::error!("Failed to stop stream {}: {:?}", stream_id, e);
//             }
//         }
//     }

//     async fn get_active_stream_count(&self) -> usize {
//         self.stream_state.active_streams.read().await.len()
//     }
// }