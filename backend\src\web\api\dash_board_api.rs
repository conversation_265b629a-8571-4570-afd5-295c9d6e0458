use axum::{
    extract::{State, Extension},
    routing::get,
    <PERSON><PERSON>, Router,
};
use crate::web::model::dashboard::StatisticResponse;
use crate::web::model::sys_user::Claims;
use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
};
use std::sync::Arc;
use log::error;


pub fn register_dash_board_api() -> Router<Arc<AppState>> {
    Router::new()
        .route("/dashboard/statistic", get(get_statistic))
}

async fn get_statistic(
    State(state): State<Arc<AppState>>,
    Extension(claims): Extension<Claims>,
) -> <PERSON><PERSON><ApiResponse<StatisticResponse>> {
    let user_id = claims.sub;
    let statistic_response = state.service_manager.dashboard_service.get_statistic(user_id.as_str()).await;
    match statistic_response {  
        Ok(response) => <PERSON><PERSON>(ApiResponse::success(Some(response))),
        Err(e) => {
            error!("Get statistic failed: {}", e);
            <PERSON><PERSON>(ApiResponse::error(format!("获取统计数据失败: {:#}", e)))
        }
    }
}