{"db_name": "PostgreSQL", "query": "  \n            INSERT INTO sys_user (user_id,organization_id, username, user_pass, email, full_name, is_active, created_at, updated_at)  \n            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)  \n            RETURNING user_id, organization_id,username, user_pass, email, full_name, is_active as \"is_active!\", created_at, updated_at, last_login\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "user_pass", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "full_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "last_login", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "Timestamptz", "Timestamptz"]}, "nullable": [false, true, false, false, false, true, true, true, true, true]}, "hash": "1d3e1bb5e459b5c23679077bfe80a43b3e398b5e8744dc45457a596e71bf1d80"}