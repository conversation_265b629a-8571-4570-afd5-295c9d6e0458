//rtsp/monitor.rs
// 监控器，用于监控RTSP源和帧队列的状态
use log::debug;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::Semaphore;
use tokio::time::Duration;

use crate::model::frame_queue::FrameQueue;
use crate::model::rtsp_source_manager::RtspManager;

pub struct Monitor {
    frame_queue: Arc<FrameQueue>,          // 帧队列
    rtsp_manager: Arc<RtspManager>,        // RTSP源管理器，用于获取当前的RTSP源数据
    current_index: Arc<AtomicUsize>,       // 当前索引, 用于记录当前处理的RTSP源的索引
    semaphore: Arc<Semaphore>,             // 信号量，用于限制并发处理的数量
    total_frame_counter: Arc<AtomicUsize>, // 总帧计数器，用于记录总的处理帧数
}

impl Monitor {
    pub fn new(
        frame_queue: Arc<FrameQueue>,
        rtsp_manager: Arc<RtspManager>,
        current_index: Arc<AtomicUsize>,
        semaphore: Arc<Semaphore>,
        total_frame_counter: Arc<AtomicUsize>,
    ) -> Self {
        Self {
            frame_queue,
            rtsp_manager,
            current_index,
            semaphore,
            total_frame_counter,
        }
    }

    pub fn spawn(&self) -> tokio::task::JoinHandle<()> {
        let monitor_queue = Arc::clone(&self.frame_queue);
        let rtsp_manager = Arc::clone(&self.rtsp_manager);
        let monitor_index = Arc::clone(&self.current_index);
        let monitor_sem = Arc::clone(&self.semaphore);
        let total_frame_counter = Arc::clone(&self.total_frame_counter);

        tokio::spawn(async move {
            loop {
                let current_pos = monitor_index.load(Ordering::Relaxed);

                // 安全地获取当前RTSP源的数量
                let total_urls = {
                    let sources = rtsp_manager.get_sources();
                    let sources_guard = sources.read().unwrap();
                    sources_guard.len()
                };

                // 获取队列的使用情况   
                let queue_usage = monitor_queue.usage_percentage();
                // 获取信号量的可用许可数
                let available_permits = monitor_sem.available_permits();

                debug!(
                    "Progress: {}/{} urls ({:.2}%), Queue usage: {:.2}%, Available workers: {} total frames: {}",
                    current_pos,
                    total_urls,
                    (current_pos as f32 / total_urls as f32) * 100.0,
                    queue_usage,
                    available_permits,
                    total_frame_counter.load(Ordering::SeqCst)
                );

                // 每秒打印一次
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        })
    }
}
