// src/web/model/sys_user.rs
use chrono::{DateTime, Utc};  
use serde::{Deserialize, Serialize};  

#[derive(Debug,<PERSON><PERSON>,Serialize, Deserialize, sqlx::FromRow)]  
pub struct SysUser {  
    pub user_id: String,  
    pub organization_id: Option<String>,
    pub username: String,  
    #[serde(skip_serializing)]  
    pub user_pass: String,  
    pub email: String,  
    pub full_name: Option<String>,  
    pub is_active: bool,  
    pub last_login: Option<DateTime<Utc>>,  
    pub created_at: Option<DateTime<Utc>>,  
    pub updated_at: Option<DateTime<Utc>>,  
}  

#[derive(Debug,Clone, Deserialize)]  
pub struct LoginRequest {  
    pub username: String,  
    pub password: String,  
}  

#[derive(Debug,Clone, Serialize)]  
pub struct LoginResponse {  
    pub token: String,  
    pub user: SysUser,  
}  

#[derive(Debug, <PERSON>lone,Serialize, Deserialize)]  
pub struct Claims {  
    pub sub: String,  // user_id  
    pub exp: usize,   // 过期时间  
    pub iat: usize,   // 签发时间  
}  

// 用户查询请求
#[derive(Debug, Deserialize)]
pub struct UserQueryRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub organization_id: Option<String>,
    pub search_text: Option<String>,
}

// 用户创建请求
#[derive(Debug, Deserialize)]
pub struct UserCreateRequest {
    pub username: String,
    pub password: String,
    pub email: String,
    pub full_name: Option<String>,
    pub organization_id: Option<String>,
}

// 用户更新请求
#[derive(Debug, Deserialize)]
pub struct UserUpdateRequest {
    pub user_id: String,
    pub email: Option<String>,
    pub full_name: Option<String>,
    pub organization_id: Option<String>,
    pub is_active: Option<bool>,
}

// 分页响应
#[derive(Debug, Serialize)]
pub struct PageResponse<T> {
    pub total: i64,
    pub items: Vec<T>,
}