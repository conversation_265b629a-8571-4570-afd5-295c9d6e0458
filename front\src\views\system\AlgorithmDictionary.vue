<template>
    <div class="p-4">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <el-button type="primary" @click="handleAdd">新增算法</el-button>
        <el-input
          v-model="searchText"
          placeholder="搜索算法名称或描述"
          class="w-64"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
  
      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        class="w-full"
      >
        <el-table-column
          prop="algorithm_id"
          label="算法ID"
          width="220"
        />
        <el-table-column
          prop="algorithm_name"
          label="算法名称"
          min-width="150"
        />
        <el-table-column
          prop="algorithm_description"
          label="算法描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(row)"
              >编辑</el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="算法名称" prop="algorithm_name">
            <el-input v-model="formData.algorithm_name" />
          </el-form-item>
          <el-form-item label="算法描述" prop="algorithm_description">
            <el-input
              v-model="formData.algorithm_description"
              type="textarea"
              :rows="4"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-switch v-model="formData.is_active" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  import axios from "@/utils/axios";
  
  // 表格相关
  const loading = ref(false);
  const tableData = ref([]);
  const searchText = ref("");
  const page = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  
  // 表单相关
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增算法");
  const formRef = ref(null);
  const formData = ref({
    algorithm_id: "",
    algorithm_name: "",
    algorithm_description: "",
    is_active: true,
  });
  
  const formRules = {
    algorithm_name: [
      { required: true, message: "请输入算法名称", trigger: "blur" },
      { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
    ],
  };
  
  // 加载表格数据
  const loadTableData = async () => {
    loading.value = true;
    try {
      const res = await axios.post("/algorithm_dictionary/page", {
        page: page.value,
        page_size: pageSize.value,
        search_text: searchText.value || undefined,
      });
      const { records, total: totalCount } = res.data.data;
      tableData.value = records;
      total.value = totalCount;
    } catch (error) {
      ElMessage.error("加载数据失败");
    } finally {
      loading.value = false;
    }
  };
  
  // 搜索处理
  const handleSearch = () => {
    page.value = 1;
    loadTableData();
  };
  
  // 分页处理
  const handleSizeChange = (val) => {
    pageSize.value = val;
    loadTableData();
  };
  
  const handleCurrentChange = (val) => {
    page.value = val;
    loadTableData();
  };
  
  // 新增
  const handleAdd = () => {
    dialogTitle.value = "新增算法";
    formData.value = {
      algorithm_id: "",
      algorithm_name: "",
      algorithm_description: "",
      is_active: true,
    };
    dialogVisible.value = true;
  };
  
  // 编辑
  const handleEdit = (row) => {
    dialogTitle.value = "编辑算法";
    formData.value = { ...row };
    dialogVisible.value = true;
  };
  
  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;
  
    try {
      await formRef.value.validate();
      if (formData.value.algorithm_id) {
        // 编辑
        await axios.post("/algorithm_dictionary/update", formData.value);
        ElMessage.success("更新成功");
      } else {
        // 新增
        await axios.post("/algorithm_dictionary/create", {
          algorithm_name: formData.value.algorithm_name,
          algorithm_description: formData.value.algorithm_description,
          is_active: formData.value.is_active,
        });
        ElMessage.success("创建成功");
      }
      dialogVisible.value = false;
      loadTableData();
    } catch (error) {
      ElMessage.error("操作失败");
    }
  };
  
  // 状态更改
  const handleStatusChange = async (row) => {
    try {
      await axios.post("/algorithm_dictionary/update", {
        ...row,
      });
      ElMessage.success("状态更新成功");
    } catch (error) {
      row.is_active = !row.is_active;
      ElMessage.error("状态更新失败");
    }
  };
  
  // 删除
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确定要删除该算法吗？", "提示", {
        type: "warning",
      });
      await axios.delete(`/algorithm_dictionary/delete/${row.algorithm_id}`);
      ElMessage.success("删除成功");
      loadTableData();
    } catch (error) {
      if (error !== "cancel") {
        ElMessage.error("删除失败");
      }
    }
  };
  
  // 生命周期钩子
  onMounted(() => {
    loadTableData();
  });
  </script>
  
  <style scoped>
  .el-pagination {
    justify-content: flex-end;
  }
  </style>