// src/web/model/building.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct Building {
    pub building_id: String,
    pub building_name: String,
    pub office_id: Option<String>,
    pub floors: i32,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

// 楼宇查询请求
#[derive(Debug, Deserialize)]
pub struct BuildingQueryRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub office_id: Option<String>,
    pub search_text: Option<String>,
}

// 楼宇创建请求
#[derive(Debug, Deserialize)]
pub struct BuildingCreateRequest {
    pub building_name: String,
    pub office_id: Option<String>,
    pub floors: i32,
}

// 楼宇更新请求
#[derive(Debug, Deserialize)]
pub struct BuildingUpdateRequest {
    pub building_id: String,
    pub building_name: Option<String>,
    pub office_id: Option<String>,
    pub floors: Option<i32>,
    pub is_active: Option<bool>,
}

// 分页响应
#[derive(Debug, Serialize)]
pub struct PageResponse<T> {
    pub total: i64,
    pub items: Vec<T>,
}