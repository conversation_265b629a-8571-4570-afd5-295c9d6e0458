// src/web/api/mod.rs
pub mod sys_area_info_api;
pub mod auth;
pub mod office_organization_api;
pub mod room_purpose_type_api;
pub mod algorithm_dictionary_api;
pub mod sys_permission_api;
pub mod sys_role_api;
pub mod sys_user_api;
pub mod building_api;
pub mod location_api;
pub mod camera_api;
pub mod alarm_record_api;
pub mod dash_board_api;


use axum::Router;
use std::sync::Arc;
use crate::web::model::appstate::AppState;
use crate::webrtc::api::webrtc_stream_api;

pub fn register_all_routes() -> Router<Arc<AppState>> {
    Router::new()
        .merge(register_v1_routes())
        // 可以添加其他版本的路由
        //.merge(register_v2_routes())
}

// V1版本API路由
fn register_v1_routes() -> Router<Arc<AppState>> {
    Router::new().nest(
        "/api/v1",
        Router::new()
            .merge(sys_area_info_api::register_sys_area_info_api())
            .merge(auth::register_sys_user_api())
            .merge(office_organization_api::register_office_organization_api())
            .merge(room_purpose_type_api::register_room_purpose_type_api())
            .merge(algorithm_dictionary_api::register_algorithm_dictionary_api())
            .merge(sys_permission_api::register_sys_permission_api())
            .merge(sys_role_api::register_sys_role_api())
            .merge(sys_user_api::register_sys_user_api())
            .merge(building_api::register_building_api())
            .merge(location_api::register_location_api())
            .merge(camera_api::register_camera_api())
            .merge(webrtc_stream_api::register_webrtc_stream_api())
            .merge(alarm_record_api::register_alarm_record_api())
            .merge(dash_board_api::register_dash_board_api())
    )
}



// fn register_v2_routes() -> Router<Arc<AppState>> {
//     Router::new().nest(
//         "/api/v2",
//         Router::new(), // V2版本的路由注册
//     )
// }
