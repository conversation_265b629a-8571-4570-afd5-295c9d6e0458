<template>
    <div class="p-4">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <el-button type="primary" @click="handleAdd">新增菜单</el-button>
        <el-input
          v-model="searchText"
          placeholder="搜索菜单名称或标识符"
          class="w-64"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
  
      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        row-key="permission_id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="permission_name" label="菜单名称" min-width="150" />
        <el-table-column prop="permission_code" label="菜单标识符" min-width="150" />
        <el-table-column prop="permission_type" label="菜单类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getPermissionTypeTag(row.permission_type)">
              {{ getPermissionTypeLabel(row.permission_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由路径" min-width="150" />
        <el-table-column prop="component_path" label="组件路径" min-width="150" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="success" @click="handleAdd(row)">
                添加子项
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="菜单名称" prop="permission_name">
            <el-input v-model="formData.permission_name" />
          </el-form-item>
          <el-form-item label="菜单标识符" prop="permission_code">
            <el-input v-model="formData.permission_code" />
          </el-form-item>
          <el-form-item label="菜单类型" prop="permission_type">
            <el-select v-model="formData.permission_type" class="w-full">
              <el-option label="菜单" value="menu" />
              <el-option label="按钮" value="button" />
              <el-option label="接口" value="api" />
            </el-select>
          </el-form-item>
          <el-form-item label="路由路径" prop="path">
            <el-input v-model="formData.path" />
          </el-form-item>
          <el-form-item label="组件路径" prop="component_path">
            <el-input v-model="formData.component_path" />
          </el-form-item>
          <el-form-item label="重定向" prop="redirect">
            <el-input v-model="formData.redirect" />
          </el-form-item>
          <el-form-item label="图标" prop="icon">
            <el-input v-model="formData.icon" />
          </el-form-item>
          <el-form-item label="排序号" prop="sort_order">
            <el-input-number v-model="formData.sort_order" :min="0" />
          </el-form-item>
          <el-form-item label="是否隐藏">
            <el-switch v-model="formData.is_hidden" />
          </el-form-item>
          <el-form-item label="状态">
            <el-switch v-model="formData.is_active" />
          </el-form-item>
          <el-form-item label="菜单描述" prop="permission_description">
            <el-input
              v-model="formData.permission_description"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from "vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  import axios from "@/utils/axios";
  
  // 表格相关
  const loading = ref(false);
  const tableData = ref([]);
  const searchText = ref("");
  const page = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  
  // 表单相关
  const dialogVisible = ref(false);
  const dialogTitle = ref("新增菜单");
  const formRef = ref(null);
  const formData = ref({
    permission_id: "",
    parent_id: "",
    permission_name: "",
    permission_code: "",
    permission_type: "menu",
    component_path: "",
    path: "",
    redirect: "",
    icon: "",
    sort_order: 0,
    is_hidden: false,
    is_active: true,
    permission_description: "",
  });
  
  const formRules = {
    permission_name: [
      { required: true, message: "请输入菜单名称", trigger: "blur" },
      { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
    ],
    permission_type: [
      { required: true, message: "请选择菜单类型", trigger: "change" },
    ],
  };
  
  // 菜单类型标签
  const getPermissionTypeTag = (type) => {
    const map = {
      menu: "success",
      button: "warning",
      api: "info",
    };
    return map[type] || "info";
  };
  
  const getPermissionTypeLabel = (type) => {
    const map = {
      menu: "菜单",
      button: "按钮",
      api: "接口",
    };
    return map[type] || type;
  };
  
  // 加载表格数据
  const loadTableData = async () => {
    loading.value = true;
    try {
      const res = await axios.post("/sys_permission/page", {
        page: page.value,
        page_size: pageSize.value,
        search_text: searchText.value || undefined,
      });
      const { records, total: totalCount } = res.data.data;
      tableData.value = records;
      total.value = totalCount;
    } catch (error) {
      ElMessage.error("加载数据失败");
    } finally {
      loading.value = false;
    }
  };
  
  // 搜索处理
  const handleSearch = () => {
    page.value = 1;
    loadTableData();
  };
  
  // 分页处理
  const handleSizeChange = (val) => {
    pageSize.value = val;
    loadTableData();
  };
  
  const handleCurrentChange = (val) => {
    page.value = val;
    loadTableData();
  };
  
  // 新增
  const handleAdd = (row) => {
    dialogTitle.value = row ? "新增子菜单" : "新增菜单";
    formData.value = {
      permission_id: "",
      parent_id: row ? row.permission_id : "",
      permission_name: "",
      permission_code: "",
      permission_type: "menu",
      component_path: "",
      path: "",
      redirect: "",
      icon: "",
      sort_order: 0,
      is_hidden: false,
      is_active: true,
      permission_description: "",
    };
    dialogVisible.value = true;
  };
  
  // 编辑
  const handleEdit = (row) => {
    dialogTitle.value = "编辑菜单";
    formData.value = { ...row };
    dialogVisible.value = true;
  };
  
  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return;
  
    try {
      await formRef.value.validate();
      if (formData.value.permission_id) {
        // 编辑
        await axios.post("/sys_permission/update", formData.value);
        ElMessage.success("更新成功");
      } else {
        // 新增
        await axios.post("/sys_permission/create", formData.value);
        ElMessage.success("创建成功");
      }
      dialogVisible.value = false;
      loadTableData();
    } catch (error) {
      ElMessage.error("操作失败");
    }
  };
  
  // 状态更改
  const handleStatusChange = async (row) => {
    try {
      await axios.post("/sys_permission/update", {
        ...row,
      });
      ElMessage.success("状态更新成功");
    } catch (error) {
      row.is_active = !row.is_active;
      ElMessage.error("状态更新失败");
    }
  };
  
  // 删除
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm("确定要删除该菜单吗？", "提示", {
        type: "warning",
      });
      await axios.delete(`/sys_permission/delete/${row.permission_id}`);
      ElMessage.success("删除成功");
      loadTableData();
    } catch (error) {
      if (error !== "cancel") {
        ElMessage.error("删除失败");
      }
    }
  };
  
  // 生命周期钩子
  onMounted(() => {
    loadTableData();
  });
  </script>
  
  <style scoped>
  .el-pagination {
    justify-content: flex-end;
  }
  </style>