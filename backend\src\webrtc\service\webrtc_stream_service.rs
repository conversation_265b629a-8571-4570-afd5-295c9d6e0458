// src/webrtc/service/webrtc_stream_service.rs
use anyhow::{Context, Result};
use ffmpeg_next as ffmpeg;
use std::sync::Arc;
use std::time::{Duration, Instant};
use webrtc::api::media_engine::MediaEngine;
use webrtc::api::APIBuilder;
use webrtc::peer_connection::configuration::RTCConfiguration;
use webrtc::peer_connection::sdp::session_description::RTCSessionDescription;
use webrtc::rtp_transceiver::rtp_codec::RTCRtpCodecCapability;
use webrtc::track::track_local::track_local_static_sample::TrackLocalStaticSample;
use webrtc::track::track_local::TrackLocal;
use std::sync::atomic::Ordering;
use crate::webrtc::model::webrtc_stream::*;
use crate::webrtc::service::webrtc_overlay_service::WebrtcOverlayService;
use crate::webrtc::model::webrtc_stream::SharedStreamInfo;
use log::info;
const INACTIVITY_TIMEOUT: Duration = Duration::from_secs(30);
const CLEANUP_INTERVAL: Duration = Duration::from_secs(30);

#[derive(Clone, Debug)]
pub struct WebrtcStreamService {
    stream_state: Arc<WebrtcStreamState>,
    overlay_service: Arc<WebrtcOverlayService>,
}

impl WebrtcStreamService {
    pub fn new(
        stream_state: Arc<WebrtcStreamState>,
        overlay_service: Arc<WebrtcOverlayService>,
    ) -> Self {
        let service = Self {
            stream_state,
            overlay_service,
        };

        // 启动清理任务
        let service_clone = service.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(CLEANUP_INTERVAL);
            loop {
                interval.tick().await;
                service_clone.cleanup_inactive_streams().await;
            }
        });

        service
    }

    pub async fn create_peer_connection(
        &self,
        camera_id: &str,
        rtsp_url: &str,
        offer_sdp: String,
        overlay_info: Option<OverlayInfo>,
    ) -> Result<String> {
        info!("Creating peer connection for camera: {}", camera_id);
        // 1. 快速创建视频轨道和应答
        let video_track = Arc::new(TrackLocalStaticSample::new(
            RTCRtpCodecCapability {
                mime_type: "video/h264".to_owned(),
                clock_rate: 90000,
                channels: 0,
                sdp_fmtp_line: "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f"
                    .to_owned(),
                rtcp_feedback: vec![],
            },
            format!("video-{}", camera_id),
            format!("webrtc-rs-{}", camera_id),
        ));

        info!("Video track created");
    
        // 2. 检查是否已存在共享流
        let stream_exists = self.stream_state.active_streams.read().await.contains_key(camera_id);
        
        if !stream_exists {
            info!("No shared stream found, creating new one");
            // 3. 创建新的共享流信息
            let shared_stream = SharedStreamInfo::new(
                camera_id.to_string(),
                rtsp_url.to_string(),
                video_track.clone(),
                overlay_info.clone(),
            );

            info!("Shared stream created");
            
            self.stream_state
                .active_streams
                .write()
                .await
                .insert(camera_id.to_string(), shared_stream);

            info!("Shared stream inserted into active streams");
    
            // 4. 异步启动 RTSP 处理
            let stream_state = Arc::clone(&self.stream_state);
            let overlay_service = Arc::clone(&self.overlay_service);
            let camera_id = camera_id.to_string();
            let rtsp_url = rtsp_url.to_string();
            
            tokio::spawn(async move {
                let stream_state_clone = Arc::clone(&stream_state);
                if let Err(e) = Self::process_rtsp_stream(&camera_id, &rtsp_url, stream_state, overlay_service).await {
                    log::error!("Failed to start RTSP stream for camera {}: {}", camera_id, e);
                    stream_state_clone.active_streams.write().await.remove(&camera_id);
                } else {
                    log::info!("RTSP stream processing started for camera {}", camera_id);
                }
            });
        } else {
            info!("Shared stream found, increasing viewer count");
            // 5. 如果流已存在，增加观看者计数
            if let Some(shared_stream) = self.stream_state.active_streams.read().await.get(camera_id) {
                shared_stream.viewer_count.fetch_add(1, Ordering::SeqCst);
                *shared_stream.last_activity.write().await = Instant::now();
            }
        }
    
        // 6. 创建 peer connection 并返回应答
        let answer_sdp = self.create_peer_connection_with_track(
            camera_id,
            video_track,
            offer_sdp,
        ).await?;

        info!("Peer connection created");
    
        Ok(answer_sdp)
    }


    async fn create_peer_connection_with_track(
        &self,
        camera_id: &str,
        video_track: Arc<TrackLocalStaticSample>,
        offer_sdp: String,
    ) -> Result<String> {
        info!("Creating peer connection with track for camera: {}", camera_id);
        // 创建 MediaEngine
        let mut m = MediaEngine::default();
        m.register_default_codecs()?;

        // 创建 API
        let api = APIBuilder::new()
            .with_media_engine(m)
            .with_setting_engine({
                let mut s = webrtc::api::setting_engine::SettingEngine::default();
                s.set_ip_filter(Box::new(|ip| ip.is_ipv4()));
                s.disable_certificate_fingerprint_verification(true);
                //s.set_answering_dtls_role(webrtc::dtls_transport::dtls_role::DTLSRole::Server);
                s
            })
            .build();

        info!("API created");

        let ice_servers = vec![
            webrtc::ice_transport::ice_server::RTCIceServer {
                urls: vec!["stun:***********:3478".to_string()],
                username: "user1".to_string(),
                credential: "test".to_string(),
                ..Default::default()
            },
            
        ];
        

        // 创建 PeerConnection
        let peer_connection = api
            .new_peer_connection(RTCConfiguration {
                ice_servers: ice_servers,
                //ice_servers: vec![],
                ..Default::default()
            })
            .await?;

        info!("PeerConnection created");

        let peer_connection = Arc::new(peer_connection);
        let connection_id = uuid::Uuid::new_v4().simple().to_string();
        if let Some(stream) = self.stream_state.active_streams.read().await.get(camera_id) {
            stream.peer_connections.write().await.insert(connection_id.clone(), Arc::clone(&peer_connection));
        }
        info!("PeerConnection added to active streams");

        // 添加 track
        let rtp_sender = Arc::clone(&peer_connection)
            .add_track(Arc::clone(&video_track) as Arc<dyn TrackLocal + Send + Sync>)
            .await?;

        info!("Track added to PeerConnection");
        // 处理 RTCP
        tokio::spawn(async move {
            let mut rtcp_buf = vec![0u8; 1500];
            while let Ok((_, _)) = rtp_sender.read(&mut rtcp_buf).await {}
        });

        info!("RTCP handler spawned");

        // 设置远程描述（offer）
        let offer = RTCSessionDescription::offer(offer_sdp)?;
        peer_connection.set_remote_description(offer).await?;

        info!("Remote description set");

        // 创建 answer
        let answer = peer_connection.create_answer(None).await?;
        let mut answer_sdp = answer.sdp.clone();

        info!("Answer created");
        // 确保 answer 中使用 active 作为 setup 值
        // if !answer_sdp.contains("a=setup:active") {
        //     answer_sdp = answer_sdp.replace("a=setup:passive", "a=setup:active");
        //     answer_sdp = answer_sdp.replace("a=setup:actpass", "a=setup:active");
        // }

        info!("Answer modified");

        let modified_answer = RTCSessionDescription::answer(answer_sdp.clone())?;
        peer_connection.set_local_description(modified_answer).await?;

        let mut gather_complete = peer_connection.gathering_complete_promise().await;
        let _ = gather_complete.recv().await;

        info!("Local description set");

        Ok(answer_sdp)
    }

    async fn process_rtsp_stream(
        stream_id: &str,
        rtsp_url: &str,
        stream_state: Arc<WebrtcStreamState>,
        overlay_service: Arc<WebrtcOverlayService>,
    ) -> Result<()> {
        info!("Processing RTSP stream for camera: {}", stream_id);
        let stream_id = stream_id.to_string();
        let rtsp_url = rtsp_url.to_string();
        
        tokio::task::spawn_blocking(move || {
            ffmpeg::init()?;
            info!("FFmpeg initialized");
            let mut input_ctx = open_input_stream(&rtsp_url, 5)?;
            let input = input_ctx
                .streams()
                .best(ffmpeg::media::Type::Video)
                .ok_or_else(|| anyhow::anyhow!("No video stream found"))?;
            let video_stream_index = input.index();

            let decoder_ctx = ffmpeg::codec::Context::from_parameters(input.parameters())?;
            let mut decoder = decoder_ctx.decoder().video()?;
            let mut encoder = create_h264_encoder(decoder.width(), decoder.height(), decoder.format())?;
  
            let mut scaler = ffmpeg::software::scaling::Context::get(
                decoder.format(),
                decoder.width(),
                decoder.height(),
                ffmpeg::format::Pixel::YUV420P,
                decoder.width(),
                decoder.height(),
                ffmpeg::software::scaling::Flags::BILINEAR,
            )?;
  
            let mut decoded_frame = ffmpeg::frame::Video::empty();
            let mut scaled_frame = ffmpeg::frame::Video::new(
                ffmpeg::format::Pixel::YUV420P,
                decoder.width(),
                decoder.height(),
            );
  
            let rt = tokio::runtime::Handle::current();

            'main: loop {
                // 检查是否还有观看者
                let should_continue = {
                    let active_streams = rt.block_on(stream_state.active_streams.read());
                    if let Some(stream) = active_streams.get(&stream_id.clone()) {
                        let viewer_count = stream.viewer_count.load(Ordering::SeqCst);
                        let last_activity = *rt.block_on(stream.last_activity.read());
                        info!("Viewer count: {}, Last activity: {:?}", viewer_count, last_activity);
                        viewer_count > 0 && last_activity.elapsed() < INACTIVITY_TIMEOUT
                    } else {
                        false
                    }
                };
    
                if !should_continue {
                    info!("No viewers, stopping stream");
                    break 'main;
                }
    
                // 重置输入上下文，以防止流中断
                if input_ctx.packets().count() == 0 {
                    info!("Input context is empty, reopening stream");
                    input_ctx = open_input_stream(&rtsp_url, 5)?;
                    continue;
                }
    
                for (stream, packet) in input_ctx.packets() {
                    if stream.index() == video_stream_index {
                        info!("Received packet for video stream");
                        decoder.send_packet(&packet)?;
                        while decoder.receive_frame(&mut decoded_frame).is_ok() {
                            info!("Received frame for video stream");
                            scaler.run(&decoded_frame, &mut scaled_frame)?;
                            info!("Scaled frame for video stream");
                            // 获取并应用叠加信息
                            if let Some(overlay_info) = rt.block_on(Self::get_overlay_info(
                                Arc::clone(&stream_state),
                                &String::from(stream_id.clone()),
                            )) {
                                apply_overlay(&mut scaled_frame, &overlay_info, &overlay_service)?;
                            }
                            info!("Applied overlay for video stream");
                            encoder.send_frame(&scaled_frame)?;
                            info!("Sent frame to encoder");
                            let mut encoded_packet = ffmpeg::Packet::empty();
                            while encoder.receive_packet(&mut encoded_packet).is_ok() {
                                if let Some(data) = encoded_packet.data() {
                                    info!("Received encoded packet for video stream");
                                    rt.block_on(send_to_webrtc(
                                        Arc::clone(&stream_state),
                                        &String::from(stream_id.clone()),
                                        data.to_vec(),
                                    ))?;
                                    info!("Sent frame to WebRTC");
                                }
                            }
                        }
                    }
                }
            }
    
            Ok(())
        })
        .await?
    }    

    pub async fn disconnect_viewer(&self, camera_id: &str) -> Result<()> {
        info!("Disconnecting viewer for camera: {}", camera_id);
        if let Some(shared_stream) = self.stream_state.active_streams.read().await.get(camera_id) {
            let new_count = shared_stream.viewer_count.fetch_sub(1, Ordering::SeqCst) - 1;
            if new_count <= 0 {
                info!("No viewers, stopping stream");
                // 标记最后活动时间，清理任务会在超时后停止流
                *shared_stream.last_activity.write().await = Instant::now();
            }
        }
        Ok(())
    }

    async fn cleanup_inactive_streams(&self) {
        info!("Cleaning up inactive streams");
        let mut active_streams = self.stream_state.active_streams.write().await;
        let mut to_remove = Vec::new();
        
        for (id, stream) in active_streams.iter() {
            let viewer_count = stream.viewer_count.load(Ordering::SeqCst);
            let last_activity = stream.last_activity.read().await;
            info!("Viewer count: {}, Last activity: {:?}", viewer_count, last_activity);
            
            if viewer_count <= 0 && last_activity.elapsed() >= INACTIVITY_TIMEOUT {
                to_remove.push(id.clone());
            }
        }
        
        for id in to_remove {
            active_streams.remove(&id);
            info!("Removed inactive stream: {}", id);
        }
    }    


    pub async fn handle_answer(&self, stream_id: &str, answer_sdp: String) -> Result<()> {
        info!("Handling answer for stream: {}", stream_id);
        let active_streams = self.stream_state.active_streams.read().await;
        if let Some(stream) = active_streams.get(stream_id) {
            info!("Found stream: {}", stream_id);
            // 获取对应的 PeerConnection
            if let Some(peer_connection) = stream.peer_connections.read().await.values().next() {
                info!("Found peer connection for stream: {}", stream_id);
                let answer = RTCSessionDescription::answer(answer_sdp)?;
                peer_connection.set_remote_description(answer).await?;
            } else {
                return Err(anyhow::anyhow!("No peer connection found"));
            }
        } else {
            return Err(anyhow::anyhow!("Stream not found"));
        }
        Ok(())
    }
    
    pub async fn handle_ice_candidate(
        &self,
        stream_id: &str,
        candidate: String,
        sdp_mid: Option<String>,
        sdp_mline_index: Option<u16>,
    ) -> Result<()> {
        info!("Handling ICE candidate for stream: {}", stream_id);
        let active_streams = self.stream_state.active_streams.read().await;
        if let Some(stream) = active_streams.get(stream_id) {
            info!("Found stream: {}", stream_id);
            // 获取对应的 PeerConnection
            if let Some(peer_connection) = stream.peer_connections.read().await.values().next() {
                info!("Found peer connection for stream: {}", stream_id);
                // 检查远程描述是否已设置
                if peer_connection.remote_description().await.is_none() {
                    info!("Remote description is not set");
                    return Err(anyhow::anyhow!("Remote description is not set"));
                }
    
                peer_connection
                    .add_ice_candidate(webrtc::ice_transport::ice_candidate::RTCIceCandidateInit {
                        candidate,
                        sdp_mid,
                        sdp_mline_index,
                        username_fragment: None,
                    })
                    .await?;
            } else {
                return Err(anyhow::anyhow!("No peer connection found"));
            }
        } else {
            return Err(anyhow::anyhow!("Stream not found"));
        }
        Ok(())
    }


    async fn get_overlay_info(
        stream_state: Arc<WebrtcStreamState>,
        stream_id: &str,
    ) -> Option<OverlayInfo> {
        let active_streams = stream_state.active_streams.read().await;
        active_streams
            .get(stream_id)
            .and_then(|stream| {
                let overlay_info = stream.overlay_info.blocking_read();
                (*overlay_info).clone()
            })
    }

}


async fn send_to_webrtc(
    stream_state: Arc<WebrtcStreamState>,
    stream_id: &str,
    data: Vec<u8>,
) -> Result<()> {
    let active_streams = stream_state.active_streams.read().await;
    if let Some(stream) = active_streams.get(stream_id) {
        let sample = webrtc::media::Sample {
            data: data.into(),
            duration: Duration::from_millis(33), // ~30fps
            ..Default::default()
        };
        stream.video_track.write_sample(&sample).await?;
    }
    Ok(())
}

fn open_input_stream(rtsp_url: &str, retry_count: i32) -> Result<ffmpeg::format::context::Input> {
    let mut dict = ffmpeg::Dictionary::new();
    dict.set("rtsp_transport", "tcp");
    dict.set("stimeout", "5000000");
    dict.set("buffer_size", "1024000");
    dict.set("max_delay", "500000"); // 500ms 最大延迟
    dict.set("fflags", "nobuffer"); // 禁用输入缓冲

    for i in 0..retry_count {
        match ffmpeg::format::input_with_dictionary(rtsp_url, dict.clone()) {
            Ok(ictx) => return Ok(ictx),
            Err(e) => {
                if i == retry_count - 1 {
                    return Err(e.into());
                }
                log::warn!("Failed to open input stream (attempt {}): {}", i + 1, e);
                std::thread::sleep(std::time::Duration::from_secs(1));
            }
        }
    }

    Err(anyhow::anyhow!(
        "Failed to open input stream after {} retries",
        retry_count
    ))
}

//应用叠加信息
fn apply_overlay(
    frame: &mut ffmpeg::frame::Video,
    overlay: &OverlayInfo,
    overlay_service: &WebrtcOverlayService,
) -> Result<()> {
    // 叠加文字
    for text in &overlay.texts {
        overlay_service
            .draw_text(
                frame,
                &text.text,
                text.x,
                text.y,
                text.font_size,
                &text.color,
            )
            .with_context(|| format!("Failed to draw text: {}", text.text))?;
    }

    // 叠加形状
    for shape in &overlay.shapes {
        overlay_service
            .draw_shape(frame, shape)
            .with_context(|| "Failed to draw shape")?;
    }

    Ok(())
}

fn create_h264_encoder(
    width: u32,
    height: u32,
    _format: ffmpeg::format::Pixel,
) -> Result<ffmpeg::encoder::Video> {

    let codec = ffmpeg::encoder::find(ffmpeg::codec::Id::H264)
    .ok_or_else(|| anyhow::anyhow!("H264 encoder not found"))?;

    let context = ffmpeg::codec::Context::new_with_codec(codec);
    let mut encoder = context.encoder().video()?;

    encoder.set_width(width);
    encoder.set_height(height);
    //encoder.set_level(40);
    encoder.set_format(ffmpeg::format::Pixel::YUV420P);
    encoder.set_time_base(ffmpeg::Rational(1, 90000));
    encoder.set_bit_rate(1_000_000);
    encoder.set_gop(30);
    encoder.set_max_b_frames(0);

    let mut opts = ffmpeg::Dictionary::new();
    opts.set("preset", "ultrafast");
    opts.set("tune", "zerolatency");
    opts.set("profile", "baseline");
    //opts.set("x264opts", "bframes=0:force-cfr=1");
    opts.set("x264opts", "bframes=0:force-cfr=1:keyint=30:min-keyint=30"); // 添加关键帧间隔控制

    let encoder=encoder.open_with(opts)?;

    Ok(encoder)
}