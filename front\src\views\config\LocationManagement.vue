<!-- src/views/config/LocationManagement.vue -->
<template>
  <div class="flex h-full">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 p-4 border-r overflow-hidden flex flex-col">
      <div class="mb-4">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索节点"
          clearable
          prefix-icon="Search"
        />
      </div>
      <div class="flex-1 overflow-auto">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="flex items-center">
              <el-icon class="mr-1">
                <Location v-if="data.type === 'area'" />
                <OfficeBuilding v-else-if="data.type === 'org'" />
                <House v-else-if="data.type === 'building'" />
              </el-icon>
              <span>{{ data.name }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 p-4 overflow-hidden flex flex-col">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleAdd" :disabled="!isAddEnable">
            新增位置
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
        <div class="flex space-x-2">
          <el-select
            v-model="queryParams.location_type"
            placeholder="位置类型"
            clearable
          >
            <el-option label="房间" value="room" />
            <el-option label="楼道" value="corridor" />
            <el-option label="大院" value="yard" />
            <el-option label="其他" value="other" />
          </el-select>
          <el-input
            v-model="queryParams.search_text"
            placeholder="搜索位置名称"
            class="w-64"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>

      <!-- 当前位置显示 -->
      <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        border
        stripe
        class="flex-1"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="location_name"
          label="位置名称"
          min-width="120"
        />
        <el-table-column prop="location_type" label="位置类型" width="100">
          <template #default="{ row }">
            {{ formatLocationType(row.location_type) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="purpose_type_id"
          label="用途类型"
          min-width="120"
        >
          <template #default="{ row }">
            {{ getPurposeTypeName(row.purpose_type_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="floor" label="楼层" width="80" align="center" />
        <el-table-column prop="area" label="面积(㎡)" width="100" align="right">
          <template #default="{ row }">
            {{ row.area ? row.area.toFixed(2) : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="capacity"
          label="容量"
          width="80"
          align="center"
        />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="位置名称" prop="location_name">
          <el-input v-model="formData.location_name" />
        </el-form-item>
        <el-form-item label="位置类型" prop="location_type">
          <el-select
            v-model="formData.location_type"
            placeholder="请选择位置类型"
          >
            <el-option label="房间" value="room" />
            <el-option label="楼道" value="corridor" />
            <el-option label="大院" value="yard" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="用途类型" prop="purpose_type_id">
          <el-select
            v-model="formData.purpose_type_id"
            placeholder="请选择用途类型"
            clearable
            :loading="purposeTypeLoading"
          >
            <el-option
              v-for="item in purposeTypeOptions"
              :key="item.room_purpose_type_id"
              :label="item.room_purpose_type_name"
              :value="item.room_purpose_type_id"
            >
              <span>{{ item.room_purpose_type_name }}</span>
              <span class="text-gray-400 text-sm ml-2">
                {{ item.room_purpose_type_description || "-" }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所属楼宇">
          <el-input
            v-model="formData.building_name"
            disabled
            placeholder="请在左侧树中选择楼宇"
          />
        </el-form-item>
        <el-form-item label="楼层" prop="floor">
          <el-input-number v-model="formData.floor" :min="1" :max="200" />
        </el-form-item>
        <el-form-item label="面积" prop="area">
          <el-input-number
            v-model="formData.area"
            :min="0"
            :max="100000"
            style="width: 180px"
          >
            <template #append>㎡</template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="容量" prop="capacity">
          <el-input-number v-model="formData.capacity" :min="0" :max="1000" />
        </el-form-item>
        <el-form-item label="状态" v-if="formData.location_id">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { format } from "date-fns";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const currentNode = ref(null);
const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

// 表格相关
const loading = ref(false);
const tableRef = ref(null);
const tableData = ref([]);
const selectedRows = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const isAddEnable = ref(false);

// 用途类型相关
const purposeTypeOptions = ref([]);
const purposeTypeLoading = ref(false);
const purposeTypeMap = ref(new Map()); // 用于快速查找用途类型名称

// 加载用途类型数据
const loadPurposeTypes = async () => {
  purposeTypeLoading.value = true;
  try {
    const res = await axios.post("/room_purpose_type/page", {
      page: 1,
      page_size: 1000, // 假设用途类型数量不会太多
    });

    if (res.data.code === 200) {
      purposeTypeOptions.value = res.data.data.records;
      // 更新Map以便快速查找
      purposeTypeMap.value = new Map(
        res.data.data.records.map((item) => [
          item.room_purpose_type_id,
          item.room_purpose_type_name,
        ])
      );
    }
  } catch (error) {
    console.error("加载用途类型失败:", error);
    ElMessage.error("加载用途类型数据失败");
  } finally {
    purposeTypeLoading.value = false;
  }
};

// 获取用途类型名称
const getPurposeTypeName = (purposeTypeId) => {
  if (!purposeTypeId) return "-";
  return purposeTypeMap.value.get(purposeTypeId) || "-";
};

// 查询参数
const queryParams = ref({
  building_id: "",
  location_type: "",
  search_text: "",
});

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增位置");
const formRef = ref(null);
const formData = ref({
  location_id: "",
  location_name: "",
  location_type: "",
  building_id: "",
  building_name: "",
  floor: 1,
  area: null,
  capacity: null,
  purpose_type_id: "", // 用途类型ID
  is_active: true,
});

const formRules = {
  location_name: [
    { required: true, message: "请输入位置名称", trigger: "blur" },
    { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
  ],
  location_type: [
    { required: true, message: "请选择位置类型", trigger: "change" },
  ],
  floor: [
    { required: true, message: "请输入楼层", trigger: "blur" },
    { type: "number", message: "楼层必须为数字", trigger: "blur" },
  ],
  purpose_type_id: [
    { required: true, message: "请选择用途类型", trigger: "change" },
  ],
};

// 计算属性
const currentPath = computed(() => {
  if (!currentNode.value) return "全部";
  return getNodePath(currentNode.value);
});

// 修改 loadNode 函数
const loadNode = async (node, resolve) => {
  try {
    if (node.level === 0) {
      // 加载省级行政区划
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map((item) => ({
        ...item,
        id: item.code,
        type: "area",
        name: item.name,
        leaf: false,
      }));
      resolve(areas);
    } else {
      const { data } = node;
      let children = [];

      // 根据节点类型分别处理
      switch (data.type) {
        case "area":
          // 加载区划下的子区划和机构
          const [areaRes, orgRes] = await Promise.all([
            axios.get("/sys_area_info/lazy_tree_node", {
              params: { parent_code: data.code },
            }),
            axios.get(`/office_organization/list/${data.code}`),
          ]);

          // 处理子区划
          const areas = areaRes.data.data.map((item) => ({
            ...item,
            id: item.code,
            type: "area",
            name: item.name,
            leaf: item.level >= 4,
          }));

          // 处理机构
          const orgs = orgRes.data.data.map((item) => ({
            id: item.office_id,
            type: "org",
            name: item.office_name,
            leaf: false,
            office_id: item.office_id,
            office_name: item.office_name,
            area_code: data.code, // 保存所属区划代码
          }));

          children = [...areas, ...orgs];
          break;

        case "org":
          // 只加载机构下的楼宇，不再加载机构
          const buildingRes = await axios.get("/building/list", {
            params: {
              office_id: data.office_id,
              page_size: 1000,
            },
          });

          children = buildingRes.data.data.items.map((item) => ({
            id: item.building_id,
            type: "building",
            name: item.building_name,
            leaf: true,
            building_id: item.building_id,
            building_name: item.building_name,
            office_id: data.office_id,
          }));
          break;

        case "building":
          // 楼宇节点是叶子节点，不需要加载子节点
          children = [];
          break;
      }

      resolve(children);
    }
  } catch (error) {
    console.error("加载树节点失败:", error);
    ElMessage.error("加载数据失败");
    resolve([]);
  }
};
// 树节点过滤
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

// 获取节点路径
const getNodePath = (node) => {
  const path = [];
  let current = node;
  while (current.parent && current.parent.level !== 0) {
    path.unshift(current.label);
    current = current.parent;
  }
  path.unshift(current.label);
  return path.join(" / ");
};

// 节点点击处理
const handleNodeClick = (data, node) => {
  currentNode.value = node;

  // 只有在选中楼宇节点时才允许操作位置信息
  if (data.type === "building") {
    queryParams.value.building_id = data.building_id;
    formData.value.building_id = data.building_id;
    formData.value.building_name = data.building_name;
    isAddEnable.value = true;
    loadTableData();
  } else {
    queryParams.value.building_id = "";
    formData.value.building_id = "";
    formData.value.building_name = "";
    isAddEnable.value = false;
    tableData.value = [];
  }
};

// 继续上文...

// 加载表格数据
const loadTableData = async () => {
  if (!queryParams.value.building_id) return;

  loading.value = true;
  try {
    const res = await axios.get("/location/list", {
      params: {
        page: currentPage.value,
        page_size: pageSize.value,
        building_id: queryParams.value.building_id,
        location_type: queryParams.value.location_type,
        search_text: queryParams.value.search_text,
      },
    });
    tableData.value = res.data.data.items;
    total.value = res.data.data.total;
  } catch (error) {
    ElMessage.error("加载位置数据失败");
  } finally {
    loading.value = false;
  }
};

// 表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  queryParams.value.location_type = "";
  queryParams.value.search_text = "";
  currentPage.value = 1;
  loadTableData();
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadTableData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadTableData();
};

// 新增位置
const handleAdd = () => {
  if (!formData.value.building_id) {
    ElMessage.warning("请先在左侧选择一个楼宇");
    return;
  }
  dialogTitle.value = "新增位置";
  formData.value = {
    location_id: "",
    location_name: "",
    location_type: "",
    building_id: formData.value.building_id,
    building_name: formData.value.building_name,
    purpose_type_id: "",
    floor: 1,
    area: null,
    capacity: null,
    is_active: true,
  };
  dialogVisible.value = true;
};

// 编辑位置
const handleEdit = (row) => {
  dialogTitle.value = "编辑位置";
  formData.value = {
    ...row,
    building_name: currentNode.value?.data?.name || "",
  };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (formData.value.location_id) {
      // 编辑
      const res = await axios.put("/location/update", {
        location_id: formData.value.location_id,
        location_name: formData.value.location_name,
        location_type: formData.value.location_type,
        building_id: formData.value.building_id,
        floor: formData.value.floor,
        area: formData.value.area,
        capacity: formData.value.capacity,
        is_active: formData.value.is_active,
        purpose_type_id: formData.value.purpose_type_id,
      });
      if (res.data.code === 200) {
        ElMessage.success("更新成功");
      } else {
        ElMessage.error(res.data.message);
      }
    } else {
      // 新增
      const res = await axios.post("/location/create", {
        location_name: formData.value.location_name,
        location_type: formData.value.location_type,
        building_id: formData.value.building_id,
        floor: formData.value.floor,
        area: formData.value.area,
        capacity: formData.value.capacity,
        purpose_type_id: formData.value.purpose_type_id,
      });
      if (res.data.code === 200) {
        ElMessage.success("创建成功");
      } else {
        ElMessage.error(res.data.message);
      }
    }

    dialogVisible.value = false;
    loadTableData();
  } catch (error) {
    if (error?.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error("操作失败");
    }
  }
};

// 删除位置
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确定要删除该位置吗？此操作不可恢复！", "警告", {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    });

    const res = await axios.delete(`/location/delete/${row.location_id}`);
    if (res.data.code === 200) {
      ElMessage.success("删除成功");
      loadTableData();
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个位置吗？此操作不可恢复！`,
      "警告",
      {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }
    );

    const promises = selectedRows.value.map((row) =>
      axios.delete(`/location/delete/${row.location_id}`)
    );
    await Promise.all(promises);

    ElMessage.success("批量删除成功");
    loadTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
    }
  }
};

// 状态变更
const handleStatusChange = async (row) => {
  try {
    const res = await axios.put("/location/update", {
      location_id: row.location_id,
      is_active: row.is_active,
    });
    if (res.data.code === 200) {
      ElMessage.success("状态更新成功");
    } else {
      row.is_active = !row.is_active;
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    row.is_active = !row.is_active;
    ElMessage.error("状态更新失败");
  }
};

// 格式化位置类型
const formatLocationType = (type) => {
  const typeMap = {
    room: "房间",
    corridor: "楼道",
    yard: "大院",
    other: "其他",
  };
  return typeMap[type] || type;
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return "-";
  return format(new Date(date), "yyyy-MM-dd HH:mm:ss");
};

// 监听器
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val);
});

// 生命周期钩子
onMounted(() => {
  loadPurposeTypes();
});
</script>

<style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

.el-dialog {
  max-width: 90%;
}

:deep(.el-table) {
  height: 100%;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 表格内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
}

/* 按钮组样式 */
:deep(.el-button-group) {
  display: flex;
  gap: 4px;
}

/* 分页器样式 */
:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 16px 0;
}

/* 搜索框样式 */
.search-input {
  width: 240px;
}

/* 表单项样式 */
:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

/* 对话框表单样式 */
:deep(.el-dialog__body) {
  padding: 20px 40px;
}

/* 开关按钮样式 */
:deep(.el-switch) {
  margin: 0 8px;
}

/* 树形控件滚动条样式 */
.el-tree::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-tree::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

.el-tree::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #dcdfe6;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f7fa;
}

/* 用途类型选择器样式 */
:deep(.el-select-dropdown__item) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 20px;
}

:deep(.el-select-dropdown__item .text-gray-400) {
  font-size: 12px;
  color: #909399;
}
</style>
