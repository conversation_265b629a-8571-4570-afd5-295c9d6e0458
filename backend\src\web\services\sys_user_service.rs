// src/web/services/sys_user_service.rs
use anyhow::{Context, Result};  
use argon2::{  
    password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},  
    Argon2,  
};  
use chrono::Utc;  
use jsonwebtoken::{encode, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>};  
use sqlx::{Pool, Postgres};  
use std::sync::Arc;  
use uuid::Uuid;  


use crate::web::model::sys_user::{<PERSON><PERSON><PERSON>, SysUser, UserQueryRequest, UserUpdateRequest, PageResponse};  
use crate::web::model::sys_permission::{LoginMenuResponse, SysPermission, SysPermissionRow};
#[derive(Clone, Debug)]  
pub struct SysUserService {  
    pool: Arc<Pool<Postgres>>,  
    jwt_secret: String,  
}  

impl SysUserService {  
    pub fn new(pool: Arc<Pool<Postgres>>, jwt_secret: String) -> Self {  
        Self { pool, jwt_secret }  
    }  

    pub async fn login(&self, username: &str, password: &str) -> Result<LoginMenuResponse> {  
        let user = sqlx::query_as!(  
            SysUser,  
            r#"  
            SELECT user_id, organization_id,username, user_pass, email, full_name, is_active as "is_active!", created_at, updated_at, last_login
            FROM sys_user   
            WHERE username = $1 AND is_active = true  
            "#,  
            username  
        )  
        .fetch_optional(self.pool.as_ref())  
        .await  
        .context("Failed to fetch user")?  
        .ok_or_else(|| anyhow::anyhow!("User not found"))?;  

        // 验证密码  
        let parsed_hash = PasswordHash::new(&user.user_pass)  
            .map_err(|e| anyhow::anyhow!("Failed to parse password hash: {}", e))?;  

        Argon2::default()  
            .verify_password(password.as_bytes(), &parsed_hash)  
            .map_err(|_| anyhow::anyhow!("Invalid password"))?;  

        // 更新最后登录时间  
        sqlx::query!(  
            r#"  
            UPDATE sys_user   
            SET last_login = $1   
            WHERE user_id = $2  
            "#,  
            Utc::now(),  
            user.user_id  
        )  
        .execute(self.pool.as_ref())  
        .await  
        .context("Failed to update last login time")?;  

        // 生成JWT token  
        let exp = Utc::now()  
            .timestamp() as usize + 24 * 3600; // 24小时后过期  
        let claims = Claims {  
            sub: user.user_id.clone(),  
            exp,  
            iat: Utc::now().timestamp() as usize,  
        };  

        let token = encode(  
            &Header::default(),  
            &claims,  
            &EncodingKey::from_secret(self.jwt_secret.as_bytes()),  
        )  
        .context("Failed to create token")?;  

            // 获取用户的角色和权限  
            let menu_rows = sqlx::query_as!(  
                SysPermissionRow,  
                r#"  
                WITH RECURSIVE menu_tree AS (  
                    -- 基础查询：获取用户的顶级菜单  
                    SELECT DISTINCT p.*  
                    FROM sys_permission p  
                    JOIN sys_role_permission rp ON p.permission_id = rp.permission_id  
                    JOIN sys_user_role ur ON rp.role_id = ur.role_id  
                    WHERE ur.user_id = $1   
                    AND p.parent_id IS NULL  
                    AND p.permission_type = 'menu'  
        
                    UNION ALL  
        
                    -- 递归查询：获取子菜单  
                    SELECT p.*  
                    FROM sys_permission p  
                    JOIN sys_role_permission rp ON p.permission_id = rp.permission_id  
                    JOIN sys_user_role ur ON rp.role_id = ur.role_id  
                    JOIN menu_tree mt ON p.parent_id = mt.permission_id  
                    WHERE ur.user_id = $1  
                    AND p.permission_type = 'menu'  
                )  
                SELECT   
                    permission_id,  
                    parent_id,  
                    permission_name,  
                    permission_code,  
                    permission_type,  
                    component_path,  
                    path,  
                    redirect,  
                    icon,  
                    sort_order,  
                    permission_description  
                FROM menu_tree  
                ORDER BY sort_order  
                "#,  
                user.user_id  
            )  
            .fetch_all(self.pool.as_ref())  
            .await  
            .context("Failed to fetch user menus")?;  
        
            // 将查询结果转换为 SysPermission  
            let menus: Vec<SysPermission> = menu_rows.into_iter().map(Into::into).collect();  
        
            // 构建菜单树  
            let menu_tree = build_menu_tree(menus);  
        
    Ok(LoginMenuResponse {  
        token,  
        user,  
        menus: menu_tree,  
    })  
    }  


    pub async fn logout(&self, _user_id: &str) -> Result<()> {  
        // 这里可以实现一些登出逻辑，比如记录日志等  
        Ok(())  
    }  

    pub async fn create_user(&self, user: SysUser) -> Result<SysUser> {  
        let salt = SaltString::generate(&mut OsRng);  
        let argon2 = Argon2::default();  
        let password_hash = argon2  
            .hash_password(user.user_pass.as_bytes(), &salt)  
            .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?  
            .to_string();  

        let user_id = Uuid::new_v4().simple().to_string();
        let now = Utc::now();  

        let user = sqlx::query_as!(  
            SysUser,  
            r#"  
            INSERT INTO sys_user (user_id,organization_id, username, user_pass, email, full_name, is_active, created_at, updated_at)  
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)  
            RETURNING user_id, organization_id,username, user_pass, email, full_name, is_active as "is_active!", created_at, updated_at, last_login
            "#,  
            user_id,  
            user.organization_id,  
            user.username,  
            password_hash,  
            user.email,  
            user.full_name,  
            true,  
            now,  
            now  
        )  
        .fetch_one(self.pool.as_ref())  
        .await  
        .context("Failed to create user")?;  

        Ok(user)  
    }  


    // 创建测试用户的函数  
    pub async fn create_default_admin_user(&self) -> Result<SysUser> {  
        let password = "shinow"; // 测试密码  
        let salt = SaltString::generate(&mut OsRng);  
        let argon2 = Argon2::default();  
        let password_hash = argon2  
            .hash_password(password.as_bytes(), &salt)  
            .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?  
            .to_string();  

        let user_id = Uuid::new_v4().to_string();  
        let now = Utc::now();  

        let test_user = sqlx::query_as!(  
            SysUser,  
            r#"  
            INSERT INTO sys_user (user_id,organization_id, username, user_pass, email, full_name, is_active, created_at, updated_at)  
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)  
            RETURNING user_id, organization_id,username, user_pass, email, full_name, is_active as "is_active!", created_at, updated_at, last_login  
            "#,  
            user_id,  
            "-1", // -1 表示系统管理员
            "admin", // 测试用户名  
            password_hash,  
            "<EMAIL>",  
            "系统管理员",  
            true,  
            now,  
            now  
        )  
        .fetch_one(self.pool.as_ref())  
        .await  
        .context("Failed to create test user")?;  

        Ok(test_user)  
    }

    pub async fn init_test_data(&self) -> Result<()> {  
        // 检查是否已存在测试用户  
        let exists = sqlx::query!(  
            "SELECT COUNT(*) as count FROM sys_user WHERE username = $1",  
            "admin"  
        )  
        .fetch_one(self.pool.as_ref())  
        .await?  
        .count  
        .unwrap_or(0) > 0;  

        if !exists {  
            self.create_default_admin_user().await?;  
            println!("测试用户已创建 - 用户名: admin, 密码: shinow");  
        }  

        Ok(())  
    }  

    // 分页查询用户
    pub async fn query_users(&self, query: UserQueryRequest) -> Result<PageResponse<SysUser>> {
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = ((page - 1) * page_size) as i64;
    
        // 构建基础SQL
        let mut sql = String::from(
            "SELECT user_id, organization_id, username, user_pass, email, 
            full_name, is_active, last_login, created_at, updated_at 
            FROM sys_user WHERE 1=1"
        );
        
        // 使用 Vec 存储实际的参数值
        let mut bind_values = vec![];
        let mut param_count = 1;
    
        // 动态添加条件
        if let Some(org_id) = query.organization_id {
            sql.push_str(&format!(" AND organization_id = ${}", param_count));
            bind_values.push(org_id);
            param_count += 1;
        }
    
        if let Some(search) = query.search_text {
            sql.push_str(&format!(" AND (username ILIKE ${0} OR email ILIKE ${0} OR full_name ILIKE ${0})", 
                param_count));
            bind_values.push(format!("%{}%", search));
            param_count += 1;
        }
    
        // 获取总数
        let count_sql = format!("SELECT COUNT(*) FROM ({}) t", sql);
        let mut count_query = sqlx::query_scalar::<_, i64>(&count_sql);
        
        // 绑定参数到count查询
        for value in &bind_values {
            count_query = count_query.bind(value);
        }
        
        let total = count_query
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;
    
        // 添加分页
        sql.push_str(&format!(" ORDER BY created_at DESC LIMIT ${} OFFSET ${}", 
            param_count, param_count + 1));
        
        // 构建最终查询
        let mut query = sqlx::query_as::<_, SysUser>(&sql);
        
        // 绑定所有参数
        for value in bind_values {
            query = query.bind(value);
        }
        
        // 绑定分页参数
        query = query.bind(page_size as i64).bind(offset);
    
        let users = query
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to fetch users")?;
    
        Ok(PageResponse {
            total,
            items: users,
        })
    }

    // 更新用户信息
    pub async fn update_user(&self, req: UserUpdateRequest) -> Result<SysUser> {
        let mut query_builder = sqlx::QueryBuilder::new(
            "UPDATE sys_user SET "
        );
        
        let mut first = true;
        
        if let Some(email) = req.email {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("email = ");
            query_builder.push_bind(email);
            first = false;
        }
    
        if let Some(full_name) = req.full_name {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("full_name = ");
            query_builder.push_bind(full_name);
            first = false;
        }
    
        if let Some(org_id) = req.organization_id {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("organization_id = ");
            query_builder.push_bind(org_id);
            first = false;
        }
    
        if let Some(is_active) = req.is_active {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("is_active = ");
            query_builder.push_bind(is_active);
            first = false;
        }
    
        if !first {
            query_builder.push(", ");
        }
        query_builder.push("updated_at = ");
        query_builder.push_bind(Utc::now());
    
        query_builder.push(" WHERE user_id = ");
        query_builder.push_bind(req.user_id);
        query_builder.push(" RETURNING *");
    
        let user = query_builder
            .build_query_as::<SysUser>()
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to update user")?;
    
        Ok(user)
    }
    
    // 删除用户
    pub async fn delete_user(&self, user_id: &str) -> Result<()> {
        sqlx::query!("DELETE FROM sys_user WHERE user_id = $1", user_id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete user")?;

        Ok(())
    }    

}  

// 辅助函数：构建菜单树  
fn build_menu_tree(menus: Vec<SysPermission>) -> Vec<SysPermission> {  
    let mut menu_map: std::collections::HashMap<String, Vec<SysPermission>> = std::collections::HashMap::new();  

    // 按parent_id分组  
    for menu in menus.iter() {  
        menu_map  
            .entry(menu.parent_id.clone().unwrap_or_default())  
            .or_default()  
            .push(menu.clone());  
    }  

    // 构建树形结构  
    let root_menus = menu_map.remove("").unwrap_or_default();  
    root_menus  
        .into_iter()  
        .map(|mut menu| {  
            if let Some(children) = menu_map.get(&menu.permission_id.clone().unwrap_or_default()) {  
                menu.children = Some(children.clone());  
            }  
            menu
        })  
        .collect()  
}  
