// src/web/service/location_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use log::info;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::location::{
    Location, LocationQueryRequest, LocationUpdateRequest, PageResponse,
};

#[derive(Clone, Debug)]
pub struct LocationService {
    pool: Arc<Pool<Postgres>>,
}

impl LocationService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    // 创建位置
    pub async fn create_location(&self, location: Location) -> Result<Location> {
        let location_id = Uuid::new_v4().simple().to_string();
        let now = Utc::now();

        let location = sqlx::query_as!(
            Location,
            r#"
            INSERT INTO location (
                location_id, location_name, location_type, building_id, 
                floor, purpose_type_id, area, capacity, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
            "#,
            location_id,
            location.location_name,
            location.location_type,
            location.building_id,
            location.floor,
            location.purpose_type_id,
            location.area,
            location.capacity,
            location.is_active,
            now,
            now
        )
        .fetch_one(self.pool.as_ref())
        .await
        .context("Failed to create location")?;

        Ok(location)
    }

    // 分页查询位置
    pub async fn query_locations(
        &self,
        query: LocationQueryRequest,
    ) -> Result<PageResponse<Location>> {
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = ((page - 1) * page_size) as i64;

        let mut sql = String::from("SELECT * FROM location WHERE 1=1");

        let mut bind_values = vec![];
        let mut param_count = 1;

        if let Some(building_id) = query.building_id {
            sql.push_str(&format!(" AND building_id = ${}", param_count));
            bind_values.push(building_id);
            param_count += 1;
        }

        if let Some(location_type) = query.location_type {
            if location_type != "" {
                sql.push_str(&format!(" AND location_type = ${}", param_count));
                bind_values.push(location_type);
                param_count += 1;
            }
        }

        if let Some(search) = query.search_text {
            if search != "" {
                sql.push_str(&format!(" AND location_name ILIKE ${}", param_count));
                bind_values.push(format!("%{}%", search));
                param_count += 1;
            }
        }

        let count_sql = format!("SELECT COUNT(*) FROM ({}) t", sql);
        let mut count_query = sqlx::query_scalar::<_, i64>(&count_sql);

        for value in &bind_values {
            count_query = count_query.bind(value);
        }

        let total = count_query
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to get total count")?;

        sql.push_str(&format!(
            " ORDER BY created_at DESC LIMIT ${} OFFSET ${}",
            param_count,
            param_count + 1
        ));

        info!("SQL: {}", sql);
        info!("Parameters: {:?}", bind_values);
        info!("Page size: {}, Offset: {}", page_size, offset);

        let mut query = sqlx::query_as::<_, Location>(&sql);

        for value in bind_values {
            query = query.bind(value);
        }

        query = query.bind(page_size as i64).bind(offset);

        let locations = query
            .fetch_all(self.pool.as_ref())
            .await
            .context("Failed to fetch locations")?;

        Ok(PageResponse {
            total,
            items: locations,
        })
    }

    // 更新位置
    pub async fn update_location(&self, req: LocationUpdateRequest) -> Result<Location> {
        let mut query_builder = sqlx::QueryBuilder::new("UPDATE location SET ");

        let mut first = true;

        if let Some(name) = req.location_name {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("location_name = ");
            query_builder.push_bind(name);
            first = false;
        }

        if let Some(location_type) = req.location_type {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("location_type = ");
            query_builder.push_bind(location_type);
            first = false;
        }

        if let Some(building_id) = req.building_id {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("building_id = ");
            query_builder.push_bind(building_id);
            first = false;
        }

        if let Some(floor) = req.floor {
            if !first { 
                query_builder.push(", ");
            }
            query_builder.push("floor = ");
            query_builder.push_bind(floor);
            first = false;
        }


        if let Some(purpose_type_id) = req.purpose_type_id {                
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("purpose_type_id = ");
            query_builder.push_bind(purpose_type_id);
            first = false;
        }

        if let Some(area) = req.area {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("area = ");
            query_builder.push_bind(area);
            first = false;
        }

        if let Some(capacity) = req.capacity {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("capacity = ");
            query_builder.push_bind(capacity);
            first = false;
        }

        if let Some(is_active) = req.is_active {
            if !first {
                query_builder.push(", ");
            }
            query_builder.push("is_active = ");
            query_builder.push_bind(is_active);
            first = false;
        }

        if !first {
            query_builder.push(", ");
        }
        query_builder.push("updated_at = ");
        query_builder.push_bind(Utc::now());

        query_builder.push(" WHERE location_id = ");
        query_builder.push_bind(req.location_id);
        query_builder.push(" RETURNING *");

        let location = query_builder
            .build_query_as::<Location>()
            .fetch_one(self.pool.as_ref())
            .await
            .context("Failed to update location")?;

        Ok(location)
    }

    // 删除位置
    pub async fn delete_location(&self, location_id: &str) -> Result<()> {
        sqlx::query!("DELETE FROM location WHERE location_id = $1", location_id)
            .execute(self.pool.as_ref())
            .await
            .context("Failed to delete location")?;

        Ok(())
    }
}
