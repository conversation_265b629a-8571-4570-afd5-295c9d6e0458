<template>
    <div class="flex flex-col p-4 space-y-6">
      <!-- 模板下载区域 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-bold mb-4">模板下载</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <el-button 
            v-for="template in templates" 
            :key="template.name"
            type="primary"
            @click="downloadTemplate(template)"
          >
            <el-icon class="mr-2"><Download /></el-icon>
            {{ template.label }}
          </el-button>
        </div>
      </div>
  
      <!-- 数据导入区域 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-bold mb-4">数据导入</h2>
        <el-upload
          class="w-full"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
        >
          <el-icon class="text-gray-400 text-3xl"><Upload /></el-icon>
          <div class="mt-2 text-gray-600">点击或拖拽文件到此处上传</div>
          <template #tip>
            <div class="text-gray-400 mt-2">
              仅支持 .xlsx, .xls 格式文件
            </div>
          </template>
        </el-upload>
  
        <!-- 导入进度 -->
        <div v-if="importing" class="mt-4">
          <el-progress 
            :percentage="importProgress"
            :status="importProgress === 100 ? 'success' : ''"
          />
          <div class="text-gray-600 mt-2">
            正在导入: {{ currentFileName }}
          </div>
        </div>
  
        <!-- 导入结果 -->
        <div v-if="importResult" class="mt-4">
          <el-alert
            :title="importResult.success ? '导入成功' : '导入失败'"
            :type="importResult.success ? 'success' : 'error'"
            :description="importResult.message"
            show-icon
          />
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { Download, Upload } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  
  const templates = [
    { name: 'organization', label: '组织机构模板' },
    { name: 'building', label: '建筑物模板' },
    { name: 'office', label: '办公场所模板' },
    { name: 'camera', label: '摄像头模板' }
  ]
  
  const importing = ref(false)
  const importProgress = ref(0)
  const currentFileName = ref('')
  const importResult = ref(null)
  
  const downloadTemplate = (template) => {
    // 这里实现模板下载逻辑
    const downloadUrl = `/api/templates/${template.name}`
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = `${template.label}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  const beforeUpload = (file) => {
    const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase())
    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件!')
      return false
    }
    return true
  }
  
  const handleFileChange = async (file) => {
    importing.value = true
    currentFileName.value = file.name
    importProgress.value = 0
  
    try {
      // 模拟上传进度
      const timer = setInterval(() => {
        if (importProgress.value < 90) {
          importProgress.value += 10
        }
      }, 300)
  
      // 这里实现文件上传和导入逻辑
      const formData = new FormData()
      formData.append('file', file.raw)
      
      const response = await fetch('/api/import', {
        method: 'POST',
        body: formData
      })
      
      clearInterval(timer)
      importProgress.value = 100
      
      const result = await response.json()
      importResult.value = {
        success: result.success,
        message: result.message
      }
    } catch (error) {
      importResult.value = {
        success: false,
        message: '导入失败：' + error.message
      }
    } finally {
      setTimeout(() => {
        importing.value = false
      }, 1000)
    }
  }
  </script>
  