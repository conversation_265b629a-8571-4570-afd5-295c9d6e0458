// src/web/model/sys_permission.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};


//这个是因为给前端返回登录用户可以访问哪些菜单用的
#[derive(Debug, Clone, Serialize, Deserialize)]  
pub struct SysPermission {  
    pub permission_id: Option<String>,  
    pub parent_id: Option<String>,  
    pub permission_name: Option<String>,  
    pub permission_code: Option<String>,  
    pub permission_type: Option<String>,  
    pub component_path: Option<String>,  
    pub path: Option<String>,  
    pub redirect: Option<String>,  
    pub icon: Option<String>,  
    pub sort_order: Option<i32>,  
    pub permission_description: Option<String>,  
    pub children: Option<Vec<SysPermission>>,  
}  
  
// 用于 sqlx 查询的中间结构体  
#[derive(Debug, Clone, sqlx::FromRow)]  
pub struct SysPermissionRow {  
    pub permission_id: Option<String>,  
    pub parent_id: Option<String>,  
    pub permission_name: Option<String>,  
    pub permission_code: Option<String>,  
    pub permission_type: Option<String>,  
    pub component_path: Option<String>,  
    pub path: Option<String>,  
    pub redirect: Option<String>,  
    pub icon: Option<String>,  
    pub sort_order: Option<i32>,  
    pub permission_description: Option<String>,  
}  
  
impl From<SysPermissionRow> for SysPermission {  
    fn from(row: SysPermissionRow) -> Self {  
        Self {  
            permission_id: row.permission_id,  
            parent_id: row.parent_id,  
            permission_name: row.permission_name,  
            permission_code: row.permission_code,  
            permission_type: row.permission_type,  
            component_path: row.component_path,  
            path: row.path,  
            redirect: row.redirect,  
            icon: row.icon,  
            sort_order: row.sort_order,  
            permission_description: row.permission_description,  
            children: None,  
        }  
    }  
}  
  
#[derive(Debug, Clone, Serialize, Deserialize)]  
pub struct LoginMenuResponse {  
    pub token: String,  
    pub user: super::sys_user::SysUser,  
    pub menus: Vec<SysPermission>,  
}  

//这个是数据库表对应的结构体，用于数据库查询列表
#[derive(Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct SysPermissionEntiry {
    pub permission_id: String,
    pub parent_id: Option<String>,
    pub permission_name: String,
    pub permission_code: Option<String>,
    pub permission_type: String,
    pub component_path: Option<String>,
    pub path: Option<String>,
    pub redirect: Option<String>,
    pub icon: Option<String>,
    pub sort_order: Option<i32>,
    pub is_hidden: Option<bool>,
    pub permission_description: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PageResponse<T> {
    pub records: Vec<T>,
    pub total: i64,
    pub page: i64,
    pub page_size: i64,
}

//这个是创建权限请求的结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct PermissionCreateRequest {
    pub parent_id: Option<String>,
    pub permission_name: String,
    pub permission_code: Option<String>,
    pub permission_type: String,
    pub component_path: Option<String>,
    pub path: Option<String>,
    pub redirect: Option<String>,
    pub icon: Option<String>,
    pub sort_order: Option<i32>,
    pub is_hidden: Option<bool>,
    pub permission_description: Option<String>,
    pub is_active: Option<bool>,
}

//这个是更新权限请求的结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct PermissionUpdateRequest {
    pub permission_id: String,
    pub parent_id: Option<String>,
    pub permission_name: String,
    pub permission_code: Option<String>,
    pub permission_type: String,
    pub component_path: Option<String>,
    pub path: Option<String>,
    pub redirect: Option<String>,
    pub icon: Option<String>,
    pub sort_order: Option<i32>,
    pub is_hidden: Option<bool>,
    pub permission_description: Option<String>,
    pub is_active: Option<bool>,
}

//这个是分页请求的结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct PageRequest {
    pub page: i64,
    pub page_size: i64,
    pub search_text: Option<String>,
}