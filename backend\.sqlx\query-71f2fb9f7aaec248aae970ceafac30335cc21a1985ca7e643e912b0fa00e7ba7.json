{"db_name": "PostgreSQL", "query": "\n            SELECT \n                room_purpose_type_id, room_purpose_type_name,\n                room_purpose_type_description, is_active as \"is_active!\",\n                created_at, updated_at\n            FROM room_purpose_type\n            ORDER BY created_at DESC\n            LIMIT $1 OFFSET $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "room_purpose_type_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "room_purpose_type_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "room_purpose_type_description", "type_info": "Text"}, {"ordinal": 3, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 4, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 5, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "Int8"]}, "nullable": [false, false, true, true, true, true]}, "hash": "71f2fb9f7aaec248aae970ceafac30335cc21a1985ca7e643e912b0fa00e7ba7"}