//src/web/services/office_organization_service.rs
use anyhow::{Context, Result};
use chrono::Utc;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use uuid::Uuid;

use crate::web::model::office_organization::{OfficeCreateRequest, OfficeOrganization, OfficeUpdateRequest};

#[derive(Clone, Debug)]
pub struct OfficeOrganizationService {
    pool: Arc<Pool<Postgres>>,
}

impl OfficeOrganizationService {
    pub fn new(pool: Arc<Pool<Postgres>>) -> Self {
        Self { pool }
    }

    // 创建办公机构
    pub async fn create_office(&self, req: OfficeCreateRequest) -> Result<()> {
        let office_id = Uuid::new_v4().simple().to_string();

        sqlx::query!(
            r#"
            INSERT INTO office_organization (
                office_id, office_name, office_address, area_code, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            "#,
            office_id,
            req.office_name,
            req.office_address,
            req.area_code,
            req.is_active,
            Utc::now(),
            Utc::now()
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to create office")?;

        Ok(())
    }

    // 更新办公机构
    pub async fn update_office(&self, req: OfficeUpdateRequest) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE office_organization
            SET office_name = $1,
                office_address = $2,
                area_code = $3,
                is_active = $4,
                updated_at = $5
            WHERE office_id = $6
            "#,
            req.office_name,
            req.office_address,
            req.area_code,
            req.is_active,
            Utc::now(),
            req.office_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to update office")?;

        Ok(())
    }

    // 删除办公机构
    pub async fn delete_office(&self, office_id: &str) -> Result<()> {
        sqlx::query!(
            "DELETE FROM office_organization WHERE office_id = $1",
            office_id
        )
        .execute(self.pool.as_ref())
        .await
        .context("Failed to delete office")?;

        Ok(())
    }

    // 获取所有办公机构
    pub async fn get_all_offices(&self) -> Result<Vec<OfficeOrganization>> {
        sqlx::query_as!(
            OfficeOrganization,
            r#"
            SELECT 
                office_id, office_name, office_address, area_code,
                is_active as "is_active!", created_at, updated_at
            FROM office_organization
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.pool.as_ref())
        .await
        .context("Failed to fetch offices")
    }

    // 根据区划编码获取办公机构
    pub async fn get_offices_by_area(&self, area_code: &str) -> Result<Vec<OfficeOrganization>> {
        sqlx::query_as!(
            OfficeOrganization,
            r#"
            SELECT 
                office_id, office_name, office_address, area_code,
                is_active as "is_active!", created_at, updated_at
            FROM office_organization
            WHERE area_code = $1
            ORDER BY created_at DESC
            "#,
            area_code
        )
        .fetch_all(self.pool.as_ref())
        .await
        .context("Failed to fetch offices by area")
    }
}