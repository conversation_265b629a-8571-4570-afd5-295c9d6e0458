// src/utils/route-generator.js
import { asyncComponents } from "@/router/async-components";
// 处理组件路径
export function loadComponent(componentPath) {
  // 移除开头的 ../
  const cleanPath = componentPath.replace(/^\.\.\//, "");
  // 确保路径格式正确
  const formattedPath = cleanPath.startsWith("views/")
    ? cleanPath
    : `views/${cleanPath}`;

  return () =>
    import(
      /* @vite-ignore */ `../views/${formattedPath.replace("views/", "")}`
    );
}

// 生成路由配置
export function generateRoutes(menus) {
  return menus.map((menu) => {
    // 检查组件是否在映射表中存在
    if (!asyncComponents[menu.component_path]) {
      console.warn(`组件 ${menu.component_path} 未在 async-components.js 中定义`);
    }

    return {
      path: menu.path || "",
      name: menu.permission_name,
      component: asyncComponents[menu.component_path],
      meta: {
        title: menu.permission_name,
        icon: menu.icon,
        permissions: menu.permission_code,
        requiresAuth: true,
      },
      children: menu.children ? generateRoutes(menu.children) : [],
    };
  });
}

// export function generateRoutes(menus) {
//   const dynamicRoutes = [];

//   function generateRoute(menu) {
//     const route = {
//       path: menu.path || "",
//       name: menu.permission_code,
//       meta: {
//         title: menu.permission_name,
//         icon: menu.icon,
//         permission: menu.permission_code,
//         requiresAuth: true,
//       },
//     };

//     // 如果有组件路径，添加组件配置
//     if (menu.component_path) {
//       try {
//         route.component = loadComponent(menu.component_path);
//       } catch (error) {
//         console.error(
//           `Failed to load component for ${menu.permission_name}:`,
//           error
//         );
//       }
//     }

//     // 如果有子菜单，递归生成子路由
//     if (menu.children && menu.children.length > 0) {
//       route.children = menu.children.map((child) => generateRoute(child));
//       // 如果没有组件路径但有子菜单，设置重定向到第一个子菜单
//       if (!menu.component_path && route.children.length > 0) {
//         route.redirect = `/${route.children[0].path}`;
//       }
//     }

//     return route;
//   }

//   // 处理所有菜单
//   menus.forEach((menu) => {
//     const mainRoute = generateRoute(menu);
//     dynamicRoutes.push(mainRoute);
//   });

//   return dynamicRoutes;
// }
