<template>
  <div class="region-config-container">
    <!-- 页面头部 -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">AI算法区域配置</h1>
          <p class="text-gray-600 mt-1">
            摄像头: {{ cameraName }} (ID: {{ cameraId }})
          </p>
        </div>
        <div class="flex space-x-3">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回预览
          </el-button>
          <el-button type="primary" @click="saveAllConfigs" :loading="saving">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 左侧：视频预览和区域显示 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold mb-4">检测区域预览</h3>
        <div class="aspect-video bg-gray-100 rounded-lg overflow-hidden relative">
          <RtspPlayer
            v-if="cameraId"
            :camera-id="cameraId"
            :drawing-enabled="false"
            width="100%"
            height="100%"
            ref="videoPlayerRef"
          />
        </div>
        
        <!-- 区域列表 -->
        <div class="mt-4">
          <h4 class="font-medium mb-2">检测区域列表 ({{ regions.length }}个)</h4>
          <div class="space-y-2 max-h-40 overflow-y-auto">
            <div
              v-for="(region, index) in regions"
              :key="region.id"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100"
              :class="{ 'ring-2 ring-blue-500': selectedRegionIndex === index }"
              @click="selectRegion(index)"
            >
              <div class="flex items-center space-x-3">
                <div
                  class="w-4 h-4 rounded"
                  :style="{ backgroundColor: region.shape.color || '#FF0000' }"
                ></div>
                <span class="font-medium">区域 {{ index + 1 }}</span>
                <el-tag
                  :type="region.algorithmConfig ? 'success' : 'warning'"
                  size="small"
                >
                  {{ region.algorithmConfig ? '已配置' : '未配置' }}
                </el-tag>
              </div>
              <el-button
                type="danger"
                size="small"
                text
                @click.stop="removeRegion(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：AI算法配置 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold mb-4">AI算法配置</h3>
        
        <div v-if="selectedRegionIndex !== null && selectedRegion">
          <div class="mb-6">
            <h4 class="font-medium mb-2">区域 {{ selectedRegionIndex + 1 }} 配置</h4>
            <p class="text-sm text-gray-600">
              形状: {{ selectedRegion.shape.type === 'rectangle' ? '矩形' : '多边形' }}
            </p>
          </div>

          <!-- 算法选择 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              检测算法
            </label>
            <el-select
              v-model="currentConfig.algorithmId"
              placeholder="请选择检测算法"
              class="w-full"
              @change="handleAlgorithmChange"
            >
              <el-option
                v-for="algorithm in algorithms"
                :key="algorithm.algorithm_id"
                :label="algorithm.algorithm_name"
                :value="algorithm.algorithm_id"
              >
                <div>
                  <div class="font-medium">{{ algorithm.algorithm_name }}</div>
                  <div class="text-sm text-gray-500">{{ algorithm.algorithm_description }}</div>
                </div>
              </el-option>
            </el-select>
          </div>

          <!-- 算法参数配置 -->
          <div v-if="currentConfig.algorithmId && selectedAlgorithm">
            <h5 class="font-medium mb-3">算法参数</h5>
            <div class="space-y-4">
              <!-- 置信度阈值 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  置信度阈值 ({{ currentConfig.confidence }})
                </label>
                <el-slider
                  v-model="currentConfig.confidence"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  show-input
                  :show-input-controls="false"
                />
              </div>

              <!-- 检测间隔 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  检测间隔 (秒)
                </label>
                <el-input-number
                  v-model="currentConfig.detectionInterval"
                  :min="1"
                  :max="60"
                  class="w-full"
                />
              </div>

              <!-- 报警阈值 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  报警阈值 (连续检测次数)
                </label>
                <el-input-number
                  v-model="currentConfig.alarmThreshold"
                  :min="1"
                  :max="10"
                  class="w-full"
                />
              </div>

              <!-- 是否启用 -->
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">
                  启用此区域检测
                </label>
                <el-switch v-model="currentConfig.enabled" />
              </div>
            </div>

            <!-- 应用配置按钮 -->
            <div class="mt-6">
              <el-button
                type="primary"
                @click="applyConfigToRegion"
                class="w-full"
              >
                应用配置到区域 {{ selectedRegionIndex + 1 }}
              </el-button>
            </div>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 py-8">
          <el-icon class="text-4xl mb-2"><Select /></el-icon>
          <p>请选择一个检测区域进行配置</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from '@/utils/axios';

const route = useRoute();
const router = useRouter();

// 从路由参数获取摄像头信息
const cameraId = ref(route.query.cameraId);
const cameraName = ref(route.query.cameraName || '未知摄像头');

// 组件状态
const regions = ref([]);
const algorithms = ref([]);
const selectedRegionIndex = ref(null);
const saving = ref(false);
const videoPlayerRef = ref(null);

// 当前配置
const currentConfig = ref({
  algorithmId: '',
  confidence: 0.5,
  detectionInterval: 5,
  alarmThreshold: 3,
  enabled: true
});

// 计算属性
const selectedRegion = computed(() => {
  return selectedRegionIndex.value !== null ? regions.value[selectedRegionIndex.value] : null;
});

const selectedAlgorithm = computed(() => {
  return algorithms.value.find(alg => alg.algorithm_id === currentConfig.value.algorithmId);
});

// 方法
const goBack = () => {
  router.back();
};

const selectRegion = (index) => {
  selectedRegionIndex.value = index;
  const region = regions.value[index];
  if (region.algorithmConfig) {
    // 加载已有配置
    Object.assign(currentConfig.value, region.algorithmConfig);
  } else {
    // 重置为默认配置
    currentConfig.value = {
      algorithmId: '',
      confidence: 0.5,
      detectionInterval: 5,
      alarmThreshold: 3,
      enabled: true
    };
  }
};

const removeRegion = async (index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除区域 ${index + 1} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    regions.value.splice(index, 1);
    
    // 调整选中索引
    if (selectedRegionIndex.value === index) {
      selectedRegionIndex.value = null;
    } else if (selectedRegionIndex.value > index) {
      selectedRegionIndex.value--;
    }
    
    ElMessage.success('区域删除成功');
  } catch {
    // 用户取消
  }
};

const handleAlgorithmChange = () => {
  // 算法改变时可以重置某些参数
  if (selectedAlgorithm.value) {
    // 可以根据算法类型设置默认参数
  }
};

const applyConfigToRegion = () => {
  if (selectedRegionIndex.value !== null && currentConfig.value.algorithmId) {
    regions.value[selectedRegionIndex.value].algorithmConfig = { ...currentConfig.value };
    ElMessage.success(`配置已应用到区域 ${selectedRegionIndex.value + 1}`);
  }
};

const saveAllConfigs = async () => {
  saving.value = true;
  try {
    await axios.post(`/camera/${cameraId.value}/regions`, {
      regions: regions.value.map(region => ({
        shape: region.shape,
        algorithmConfig: region.algorithmConfig
      }))
    });
    
    ElMessage.success('配置保存成功');
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message);
  } finally {
    saving.value = false;
  }
};

// 加载数据
const loadAlgorithms = async () => {
  try {
    const response = await axios.get('/algorithm_dictionary/list');
    if (response.data.code === 200) {
      algorithms.value = response.data.data.items || [];
    }
  } catch (error) {
    ElMessage.error('加载算法列表失败');
  }
};

const loadRegions = async () => {
  try {
    const response = await axios.get(`/camera/${cameraId.value}/regions`);
    if (response.data.code === 200 && response.data.data) {
      const savedRegions = response.data.data.regions || [];
      regions.value = savedRegions.map((region, index) => ({
        id: Date.now() + index,
        shape: region.shape,
        algorithmConfig: region.algorithmConfig
      }));
      
      // 在视频播放器中显示区域
      if (videoPlayerRef.value && savedRegions.length > 0) {
        setTimeout(() => {
          videoPlayerRef.value.setShapes(savedRegions.map(r => r.shape));
        }, 1000);
      }
    }
  } catch (error) {
    ElMessage.error('加载区域配置失败');
  }
};

onMounted(() => {
  if (!cameraId.value) {
    ElMessage.error('缺少摄像头信息');
    router.back();
    return;
  }
  
  loadAlgorithms();
  loadRegions();
});
</script>

<style scoped>
.region-config-container {
  padding: 24px;
  min-height: calc(100vh - 120px);
}

.aspect-video {
  aspect-ratio: 16 / 9;
}
</style>
