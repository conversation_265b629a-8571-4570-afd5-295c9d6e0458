<!-- src/views/system/OfficeManagement.vue -->
<template>
  <div class="flex h-full p-4">
    <!-- 左侧树形区域 -->
    <div class="w-1/4 pr-4 border-r">
      <div class="mb-4">
        <el-input
          v-model="treeFilterText"
          placeholder="搜索区划"
          clearable
          prefix-icon="Search"
        />
      </div>
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        lazy
        :load="loadNode"
        :filter-node-method="filterNode"
        node-key="code"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="flex items-center">
            <el-icon class="mr-1">
              <Location />
            </el-icon>
            <span>{{ data.name }}</span>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 pl-4">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleAdd">新增机构</el-button>
        </div>
        <el-input
          v-model="searchText"
          placeholder="搜索机构"
          class="w-64"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 当前位置显示 -->
      <div class="mb-4 text-gray-600">当前位置：{{ currentPath }}</div>

      <!-- 数据表格 -->
      <el-table
        :data="filteredTableData"
        border
        stripe
        class="w-full"
        v-loading="loading"
      >
        <el-table-column prop="office_id" label="机构ID" width="220" />
        <el-table-column prop="office_name" label="机构名称" min-width="150" />
        <el-table-column
          prop="office_address"
          label="机构地址"
          min-width="200"
        />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" type="primary" @click="handleEdit(row)"
                >编辑</el-button
              >
              <el-button size="small" type="danger" @click="handleDelete(row)"
                >删除</el-button
              >
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="机构名称" prop="office_name">
          <el-input v-model="formData.office_name" />
        </el-form-item>
        <el-form-item label="机构地址" prop="office_address">
          <el-input v-model="formData.office_address" type="textarea" />
        </el-form-item>
        <el-form-item label="所属区划">
          <el-input v-model="formData.area_code" disabled />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="formData.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import axios from "@/utils/axios";

// 树相关
const treeRef = ref(null);
const treeData = ref([]);
const treeFilterText = ref("");
const treeProps = {
  label: "name",
  children: "children",
  isLeaf: "leaf",
};

// 表格相关
const loading = ref(false);
const tableData = ref([]);
const searchText = ref("");
const currentNode = ref(null);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref("新增机构");
const formRef = ref(null);
const formData = ref({
  office_id: "",
  office_name: "",
  office_address: "",
  area_code: "",
  is_active: true,
});

const formRules = {
  office_name: [{ required: true, message: "请输入机构名称", trigger: "blur" }],
};

// 计算属性
const currentPath = computed(() => {
  if (!currentNode.value) return "未选择区划";
  return getNodePath(currentNode.value);
});

const filteredTableData = computed(() => {
  const data = tableData.value;
  if (!searchText.value) return data;
  return data.filter(
    (item) =>
      item.office_name.includes(searchText.value) ||
      item.office_address?.includes(searchText.value)
  );
});

// 监听器
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val);
});

const loadNode = async (node, resolve) => {
  const loading = ElLoading.service({
    target: treeRef.value?.$el,
  });
  
  try {
    if (node.level === 0) {
      // 加载省级数据
      const res = await axios.get("/sys_area_info/lazy_tree_node");
      const areas = res.data.data.map(item => ({
        ...item,
        id: item.code,
        leaf: false
      }));
      resolve(areas);
    } else {
      // 加载下级区划
      const { data } = node;
      const res = await axios.get("/sys_area_info/lazy_tree_node", {
        params: { parent_code: data.code }
      });
      const areas = res.data.data.map(item => ({
        ...item,
        id: item.code,
        // 根据级别判断是否还有子节点
        leaf: item.level >= 4  // 假设区划最多4级
      }));
      resolve(areas);
    }
  } catch (error) {
    ElMessage.error("加载数据失败");
    resolve([]);
  } finally {
    loading.close();
  }
};


const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value) || data.code.includes(value);
};

const getNodePath = (node) => {
  const path = [];
  let current = node;
  while (current) {
    path.unshift(current.name);
    current = current.parent;
  }
  return path.join(" / ");
};

const handleNodeClick = (data) => {
  currentNode.value = data;
  loadTableData(data.code);
};

const loadTableData = async (areaCode) => {
  loading.value = true;
  try {
    const res = await axios.get(`/office_organization/list/${areaCode}`);
    tableData.value = res.data.data;
  } catch (error) {
    ElMessage.error("加载机构数据失败");
  } finally {
    loading.value = false;
  }
};

const handleAdd = () => {
  if (!currentNode.value) {
    ElMessage.warning("请先选择一个区划节点");
    return;
  }

  dialogTitle.value = "新增机构";
  formData.value = {
    office_id: "",
    office_name: "",
    office_address: "",
    area_code: currentNode.value.code,
    is_active: true,
  };
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  dialogTitle.value = "编辑机构";
  formData.value = { ...row };
  dialogVisible.value = true;
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    if (formData.value.office_id) {
      // 编辑
      await axios.post("/office_organization/update", formData.value);
      ElMessage.success("更新成功");
    } else {
      // 新增
      await axios.post("/office_organization/create", {
        office_name: formData.value.office_name,
        office_address: formData.value.office_address,
        area_code: formData.value.area_code,
        is_active: formData.value.is_active,
      });
      ElMessage.success("创建成功");
    }
    dialogVisible.value = false;
    loadTableData(currentNode.value.code);
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

const handleStatusChange = async (row) => {
  try {
    await axios.post("/office_organization/update", {
      ...row,
    });
    ElMessage.success("状态更新成功");
  } catch (error) {
    row.is_active = !row.is_active;
    ElMessage.error("状态更新失败");
  }
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("确定要删除该机构吗？", "提示", {
      type: "warning",
      appendTo: ".el-table",
    });
    await axios.delete(`/office_organization/delete/${row.office_id}`);
    ElMessage.success("删除成功");
    loadTableData(currentNode.value.code);
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 生命周期钩子
onMounted(() => {
  //loadTreeData();
});
</script>

<style scoped>
.el-tree {
  height: 100%;
  overflow-y: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* .el-tree {
  height: calc(100vh - 150px);
  overflow-y: auto;
} */
.el-message-box__wrapper {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}
</style>
