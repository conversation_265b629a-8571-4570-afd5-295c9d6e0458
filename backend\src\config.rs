//config.rs
use anyhow::Context;
use anyhow::Result;
use serde::Deserialize;
use std::fs;


#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub rtsp: RtspConfig,       //rtsp 配置
    pub jcpt: JcptConfig,       //jcpt 配置
    pub grpcConfig: GrpcConfig, //grpc 配置
    pub web: Web,               //web 配置
    pub database: Database,     //数据库配置
}

#[derive(Debug, Deserialize,Clone)]
pub struct RtspConfig {
    pub cap_thread_count: usize,      // tokio 截图job数
    pub consumer_thread_count: usize, // 视频流消费job数
    pub queue_size: usize,            // 视频流截图图片结果缓存内存队列大小
    pub consumer_target: String,      // 视频流消费目标，可选："disk"，"grpc"
    pub rtsp_data_source: String,     // rtsp 视频流地址来源,可选值: "jcptapi", "tsexcel","simulate"
    pub cap_time_out: usize,          // 截图超时时间,单位秒
    pub use_cap_sleep: bool,          // 是否启用sleep,用于降低cpu资源占用
    pub cap_sleep_time: u64,          // sleep时间,单位毫秒
    pub use_consumer_sleep: bool,     // 是否启用sleep,用于降低cpu资源占用
    pub consumer_sleep_time: u64,      // sleep时间,单位毫秒
    pub use_yolo_detection: bool,      // 是否启用yolo人员检测
    pub yolo_model_path: String,      // yolo模型路径
    pub yolo_iou_threshold: f32,      // yolo iou阈值
    pub yolo_confidence_threshold: f32, // yolo 置信度阈值
    pub yolo_detector: String, // yolo 检测器类型,可选值: "ort", "tract"
    pub yolo_device: String, // yolo 设备类型,可选值: "cuda", "cpu"
}

#[derive(Debug, Deserialize,Clone)]
pub struct JcptConfig {
    pub server_ip: String,
    pub server_port: u16,
    pub app_id: String,
}

#[derive(Debug, Deserialize,Clone)]
pub struct Web {
    pub host: String, //web服务监听地址
    pub port: u16,    //web服务监听端口
}

#[derive(Debug, Deserialize,Clone)]
pub struct Database {
    pub url:String,                 // 数据库连接字符串
    pub max_conn: u32,            // 数据库连接池最大连接数
}

#[derive(Debug, Deserialize,Clone)]
pub struct GrpcConfig {
    pub endpoint: String,  // grpc服务端地址及端口
    pub max_retries: u32,  // grpc服务调用重试次数
    pub batch_size: usize, // grpc批量调用携带的图片数据量
    pub grpc_call_timeout: u32, // grpc调用超时时间,单位秒
    pub grpc_connect_timeout: u32, // grpc连接超时时间,单位秒
    pub grpc_keepalive_interval: u32, // grpc连接保持时间,单位秒
    pub initial_stream_window_size: u32, // grpc初始化流窗口大小,单位MB
    pub initial_connection_window_size: u32, // grpc初始化连接窗口大小,单位MB
}

impl Config {
    pub fn new() -> Result<Self> {
        let config_str = fs::read_to_string("config.yaml")?;
        let config: Self = serde_yaml::from_str(&config_str).context("无法解析config.yaml文件")?;
        Ok(config)
    }
}
