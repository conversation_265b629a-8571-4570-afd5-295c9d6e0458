// src/web/api/sys_user_api.rs
use axum::{
    extract::{Path, Query, State},
    routing::{delete, get, post, put},
    Json, Router,
};
use std::sync::Arc;
use log::error;
use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    sys_user::{UserCreateRequest, UserQueryRequest, UserUpdateRequest, SysUser, PageResponse},
};

pub fn register_sys_user_api() -> Router<Arc<AppState>> {
    Router::new()
        .route("/sys_user/list", get(query_users))
        .route("/sys_user/create", post(create_user))
        .route("/sys_user/update", put(update_user))
        .route("/sys_user/delete/:id", delete(delete_user))
}

async fn query_users(
    State(state): State<Arc<AppState>>,
    Query(query): Query<UserQueryRequest>,
) -> <PERSON>son<ApiResponse<PageResponse<SysUser>>> {
    match state.service_manager.sys_user_service.query_users(query).await {
        Ok(response) => Json(ApiResponse::success(Some(response))),
        Err(e) => {
            error!("Query users failed: {}", e);
            Json(ApiResponse::error(format!("查询用户失败: {:#}", e)))
        }
    }
}

async fn create_user(
    State(state): State<Arc<AppState>>,
    Json(req): Json<UserCreateRequest>,
) -> Json<ApiResponse<SysUser>> {
    let user = SysUser {
        user_id: "".to_string(),
        organization_id: req.organization_id,
        username: req.username,
        user_pass: req.password,
        email: req.email,
        full_name: req.full_name,
        is_active: true,
        last_login: None,
        created_at: None,
        updated_at: None,
    };

    match state.service_manager.sys_user_service.create_user(user).await {
        Ok(user) => Json(ApiResponse::success(Some(user))),
        Err(e) => {
            error!("Create user failed: {}", e);
            Json(ApiResponse::error(format!("创建用户失败: {:#}", e)))
        }
    }
}

async fn update_user(
    State(state): State<Arc<AppState>>,
    Json(req): Json<UserUpdateRequest>,
) -> Json<ApiResponse<SysUser>> {
    match state.service_manager.sys_user_service.update_user(req).await {
        Ok(user) => Json(ApiResponse::success(Some(user))),
        Err(e) => {
            error!("Update user failed: {}", e);
            Json(ApiResponse::error(format!("更新用户失败: {:#}", e)))
        }
    }
}

async fn delete_user(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.sys_user_service.delete_user(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => {
            error!("Delete user failed: {}", e);
            Json(ApiResponse::error(format!("删除用户失败: {:#}", e)))
        }
    }
}