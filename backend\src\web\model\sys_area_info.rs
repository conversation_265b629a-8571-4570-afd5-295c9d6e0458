//src/web/model/sys_area_info.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// 行政区划数据库模型
#[derive(Debug, Serialize, Deserialize)]
pub struct SysAreaInfo {
    pub code: String,
    pub name: String,
    pub parent_code: Option<String>,
    pub level: i32,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

// 导入外部github 行政区划JSON数据模型
#[derive(Debug, Serialize, Deserialize)]
pub struct AreaNode {
    pub code: i64,
    pub name: String,
    pub level: i32,
    pub pcode: i64,
    #[serde(default)]
    pub children: Vec<AreaNode>,
}

// 请求/响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct AreaUpdateRequest {
    pub code: String,
    pub name: String,
    pub is_active: bool,
}


#[derive(Debug, Deserialize)]
pub struct LazyLoadQuery {
    pub parent_code: Option<String>,
}