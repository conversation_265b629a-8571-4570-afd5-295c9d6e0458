// src/web/api/location_api.rs
use axum::{
    extract::{Path, Query, State},
    routing::{delete, get, post, put},
    Json, Router,
};
use std::sync::Arc;
use log::error;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    location::{Location, LocationCreateRequest, LocationQueryRequest, LocationUpdateRequest, PageResponse},
};

pub fn register_location_api() -> Router<Arc<AppState>> {
    Router::new()
        .route("/location/list", get(query_locations))
        .route("/location/create", post(create_location))
        .route("/location/update", put(update_location))
        .route("/location/delete/:id", delete(delete_location))
}
// 分页查询位置
async fn query_locations(
    State(state): State<Arc<AppState>>,
    Query(query): Query<LocationQueryRequest>,
) -> <PERSON>son<ApiResponse<PageResponse<Location>>> {
    match state.service_manager.location_service.query_locations(query).await {
        Ok(response) => Json(ApiResponse::success(Some(response))),
        Err(e) => {
            error!("Query locations failed: {}", e);
            Json(ApiResponse::error(format!("查询位置失败: {:#}", e)))
        }
    }
}

// 创建位置
async fn create_location(
    State(state): State<Arc<AppState>>,
    Json(req): Json<LocationCreateRequest>,
) -> Json<ApiResponse<Location>> {
    let location = Location {
        location_id: "".to_string(),
        location_name: req.location_name,
        building_id: req.building_id,
        is_active: Some(true),
        created_at: None,
        updated_at: None,
        location_type: req.location_type,
        floor: req.floor,
        purpose_type_id: req.purpose_type_id,
        area: req.area,
        capacity: req.capacity,
    };
    match state.service_manager.location_service.create_location(location).await {
        Ok(response) => Json(ApiResponse::success(Some(response))),
        Err(e) => {
            error!("Create location failed: {}", e);
            Json(ApiResponse::error(format!("创建位置失败: {:#}", e)))
        }
    }
}

// 更新位置
async fn update_location(
    State(state): State<Arc<AppState>>,
    Json(location): Json<LocationUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.location_service.update_location(location).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => {
            error!("Update location failed: {}", e);
            Json(ApiResponse::error(format!("更新位置失败: {:#}", e)))
        }
    }
}

// 删除位置
async fn delete_location(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.location_service.delete_location(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => {
            error!("Delete location failed: {}", e);
            Json(ApiResponse::error(format!("删除位置失败: {:#}", e)))
        }
    }
}

