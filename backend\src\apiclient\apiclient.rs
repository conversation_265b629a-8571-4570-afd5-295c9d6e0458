#![allow(unused_variables)]

use anyhow::{anyhow, Context, Result};
use log::debug;
use reqwest::{self, Client};
use serde::{Deserialize, Serialize};
use std::fmt::Display;

use crate::config::Config;
use crate::model::rtsp_model::{RtspSource,CameraSource};


#[allow(non_snake_case)]
#[derive(Deserialize, Debug)]
pub struct GetApiKeyResponse {
    pub msg: String,
    pub code: i32,
    pub success: bool,
    pub key: Option<String>,
}

// 定义响应的结构体
#[allow(non_snake_case)]
#[derive(Debug, Serialize, Deserialize)]
struct GetClientInfoResponse {
    msg: String,
    code: i32,
    success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    client: Option<ClientInfo>,
}

// 定义客户端信息的结构体
#[allow(non_snake_case)]
#[derive(Debug, Serialize, Deserialize)]
pub struct ClientInfo {
    acCode: String,
    acCreateperson: String,
    acCreatetime: String,
    acDeleteperson: String,
    acDeletetime: String,
    acEditperson: String,
    acEdittime: String,
    acIppath: String,
    acName: String,
    acPort: String,
    acRetain1: String,
    acRetain2: String,
    acRetain3: String,
    acRetain4: String,
    acRetain5: String,
    acRetain6: String,
    acRetain7: String,
    acRetain8: String,
    acRetain9: String,
    acRule: String,
    acUrl: String,
    areaName: String,
    gpuIp: String,
    msgServerIp: String,
    msgServerPass: String,
    msgServerPort: String,
    msgServerUser: String,
    orgCode: String,
    orgName: String,
    userCode: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoomApiResponse {
    code: u16,
    data: Vec<RoomData>,
    message: String,
    success: bool,
}

#[allow(non_snake_case)]
#[derive(Debug, Serialize, Deserialize)]
pub struct RoomData {
    areaName: String,
    cameraList: Vec<CameraData>,        //摄像头信息列表
    defaultCameraCode: Option<String>,
    orgCode: Option<String>,
    orgName: Option<String>,
    rAddress: Option<String>,
    rCode: Option<String>,          //房间id
    rCreateperson: Option<String>,
    rCreatetime: Option<String>,
    rDeleteperson: Option<String>,
    rDeletetime: Option<String>,
    rEditperson: Option<String>,
    rEdittime: Option<String>,
    rName: Option<String>,  //房间名称
    rParent: Option<String>,
    rRoomtype: Option<u8>,  //房间类型
    rType: Option<u8>,
    typeName: Option<String>,
    userCode: Option<String>,
}

#[allow(non_snake_case)]
#[derive(Debug, Serialize, Deserialize)]
pub struct CameraData {
    areaName: Option<String>,
    cCode: Option<String>,
    ciChanl: Option<String>,
    ciCode: Option<String>,         //摄像头id
    ciCreateperson: Option<String>,
    ciCreatetime: Option<String>,
    ciDeleteperson: Option<String>,
    ciDeletetime: Option<String>,
    ciEditperson: Option<String>,
    ciEdittime: Option<String>,
    ciInfo: Option<String>,
    ciIppath: Option<String>,
    ciModel: Option<String>,
    ciName: Option<String>,     //摄像头名称
    ciPass: Option<String>,
    ciRetain1: Option<String>,
    ciRetain2: Option<String>,
    ciRetain3: Option<String>,
    ciRetain4: Option<String>,
    ciRetain5: Option<String>,
    ciRetain6: Option<String>,
    ciRetain7: Option<String>,
    ciRetain8: Option<String>,
    ciRetain9: Option<String>,
    ciRtsppath: Option<String>,  //完整的rtsp码流地址
    ciState: Option<String>,
    ciType: Option<u8>,
    ciUser: Option<String>,
    controlInfoList: Option<String>,
    hotAreas: Vec<String>,
    nvrCode: Option<String>,
    orgCode: Option<String>,
    orgName: Option<String>,
    rCode: Option<String>,
    rName: Option<String>,
    rRoomtype: Option<u8>,
    svrCode: Option<String>,
    typeName: Option<String>,       //摄像头类型
    userCode: Option<String>,
    vCode: Option<String>,
    vName: Option<String>,
}

impl Display for GetApiKeyResponse {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "msg: {}, code: {}, success: {}, key: {:?}",
            self.msg, self.code, self.success, self.key
        )
    }
}

pub async fn get_api_key(
    server_ip_port: &str,
    app_id: &str,
) -> Result<Option<String>, anyhow::Error> {
    let api_key_url = format!(
        "http://{}/jdgl/auth/getNewKey?appId={}",
        server_ip_port, app_id
    );

    debug!("开始获取key url: {}", &api_key_url);

    let client = Client::builder()
        .no_proxy()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        .build()
        .context("Failed to build reqwest client")?;

    let response: GetApiKeyResponse = client
        .get(&api_key_url)
        .send()
        .await
        .context("Failed to fetch API key")?
        .json()
        .await
        .context("Failed to parse API Key JSON")?;

    debug!("获取key响应结果: {:?}", response);

    match response.code {
        200 => {
            debug!("成功获取 key: {:?}", response.key);
            Ok(response.key)
        }
        201 => {
            debug!("原秘钥未过期或该应用不存在,响应: {:?}", response);
            Ok(response.key)
        }
        204 => {
            debug!("无效的密钥");
            Ok(None)
        }
        _ => {
            debug!("获取api key失败: {}, code: {}", response.msg, response.code);
            Err(anyhow!(
                "获取api key失败: {}, code: {}",
                response.msg,
                response.code
            ))
        }
    }
}

/// 获取客户端信息
/// # Arguments
/// * `ip` - 客户端IP
/// * `key` - 访问密钥
/// # Returns
///
/// 返回客户端信息或错误
pub async fn get_client_info(server_ip_port: &str, ip: &str, key: &str) -> Result<ClientInfo> {
    // 构建请求的 URL
    let url = format!(
        "http://{}/jdgl/client/getInfo?ip={}&key={}",
        server_ip_port, ip, key
    );

    // 发送 GET 请求
    let client = reqwest::Client::new();
    let response = client.get(&url).send().await.context("发送请求失败")?;

    // 解析响应
    let result: GetClientInfoResponse = response.json().await.context("解析响应JSON失败")?;

    // 根据响应处理结果
    match result.code {
        200 => result
            .client
            .ok_or_else(|| anyhow::anyhow!("成功响应但未返回客户端信息")),
        204 => Err(anyhow::anyhow!("无效的密钥")),
        _ => Err(anyhow::anyhow!("查询失败: {}", result.msg)),
    }
}

pub async fn fetch_room_data(
    server_ip_port: &str,
    app_id: &str,
    key: &str,
) -> Result<RoomApiResponse> {
    let client = Client::builder()
        .no_proxy()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        .build()
        .context("Failed to build reqwest client")?;

    let url = format!(
        "http://{}/jdgl/room/allListForClient?ip={}&key={}",
        server_ip_port, app_id, key
    );

    debug!("开始获取房间数据 url: {}", &url);

    let response = client.get(&url).send().await?;

    debug!("获取房间数据响应结果: {:?}", response);

    if response.status().is_success() {
        let api_response: RoomApiResponse = response.json().await?;
        if api_response.success {
            Ok(api_response)
        } else {
            Err(anyhow!("API returned an error: {}", api_response.message))
        }
    } else if response.status().as_u16() == 204 {
        Err(anyhow!("Invalid key: API returned 204 status code"))
    } else {
        Err(anyhow!("Request failed with status: {}", response.status()))
    }
}


pub async fn get_rtsp_urls(config: &Config) -> Result<Vec<RtspSource>> {
    let server_ip_port = format!("{}:{}", config.jcpt.server_ip, config.jcpt.server_port);
    //先获取api key
    let api_key = get_api_key(&server_ip_port, config.jcpt.app_id.as_str()).await?;

    match api_key {
        Some(api_key) => {
            debug!(
                "Key used for room data fetch: {:?} (length: {})",
                api_key,
                api_key.len()
            );
            let room_data = fetch_room_data(
                &server_ip_port,
                config.jcpt.app_id.as_str(),
                api_key.as_str(),
            )
            .await?;
            
            let mut rtsp_sources: Vec<RtspSource> = Vec::new();
            
            for room in room_data.data {
                let mut cameras = Vec::new();
                
                // 处理房间内的所有摄像头
                for camera in room.cameraList {
                    let camera_source = CameraSource {
                        camera_id: camera.ciCode.unwrap_or_default(),
                        camera_name: camera.ciName.unwrap_or_default(),
                        camera_type: camera.ciType.map_or_else(
                            || "unknown".to_string(),
                            |t| t.to_string()
                        ),
                        url: camera.ciRtsppath.unwrap_or_default(),
                    };
                    cameras.push(camera_source);
                }

                // 只有当房间有摄像头时才创建 RtspSource
                if !cameras.is_empty() {
                    let rtsp_source = RtspSource {
                        room_id: room.rCode.unwrap_or_default(),
                        room_name: room.rName.unwrap_or_default(),
                        cameras,
                    };
                    rtsp_sources.push(rtsp_source);
                }
            }
            
            Ok(rtsp_sources)
        }
        None => Err(anyhow!("获取api key失败")),
    }
}

// pub async fn get_rtsp_urls(config: &Config) -> Result<Vec<RtspSource>> {
//     let server_ip_port = format!("{}:{}", config.jcpt.server_ip, config.jcpt.server_port);
//     //先获取api key
//     let api_key = get_api_key(&server_ip_port, config.jcpt.app_id.as_str()).await?;

//     match api_key {
//         Some(api_key) => {
//             debug!(
//                 "Key used for room data fetch: {:?} (length: {})",
//                 api_key,
//                 api_key.len()
//             );
//             let room_data = fetch_room_data(
//                 &server_ip_port,
//                 config.jcpt.app_id.as_str(),
//                 api_key.as_str(),
//             )
//             .await?;
//             let mut rtsp_list: Vec<RtspSource> = Vec::new();
//             for room in room_data.data {
//                 for camera in room.cameraList {
//                     let rtsp_source = RtspSource {
//                         room_id: camera.cCode.unwrap(),
//                         room_name: room.rName.clone().unwrap(),
//                         camera_id: camera.ciCode.unwrap(),
//                         camera_name: camera.ciName.unwrap(),
//                         camera_type: camera.ciType.unwrap().to_string(),
//                         url: camera.ciRtsppath.unwrap(),
//                     };
//                     rtsp_list.push(rtsp_source);
//                 }
//             }
//             Ok(rtsp_list)
//         }
//         None => Err(anyhow!("获取api key失败")),
//     }
// }
