//src/web/api/camera_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete,get, post},
    Json, Router,
};
use std::sync::Arc;

use crate::web::model::{
    camera::{CameraCreateRequest, CameraQueryRequest, CameraUpdateRequest,PageResponse,Camera},
    common_model::ApiResponse,
};
use crate::web::model::appstate::AppState;

pub fn register_camera_api() -> Router<Arc<AppState>> {
    Router::new().nest("/camera", camera_routes())
}

fn camera_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create_camera))
        .route("/update", post(update_camera))
        .route("/delete/:id", delete(delete_camera))
        .route("/page", post(get_camera_page))
        .route("/detail/:id", get(get_camera_by_id))
        .route("/list/:location_id", get(get_camera_list))
}

async fn create_camera(
    State(state): State<Arc<AppState>>,
    Json(req): Json<CameraCreateRequest>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.camera_service.create(req).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("创建摄像头失败: {:#}", e))),
    }
}

async fn update_camera(
    State(state): State<Arc<AppState>>,
    Json(req): Json<CameraUpdateRequest>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.camera_service.update(req).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("更新摄像头失败: {:#}", e))),
    }
}

async fn delete_camera(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    match state.service_manager.camera_service.delete(&id).await {
        Ok(_) => Json(ApiResponse::success(None)),
        Err(e) => Json(ApiResponse::error(format!("删除摄像头失败: {:#}", e))),
    }
}

async fn get_camera_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<CameraQueryRequest>,
) -> Json<ApiResponse<PageResponse<Camera>>> {
    match state.service_manager.camera_service.get_page(req).await {
        Ok(page) => Json(ApiResponse::success(Some(page))),
        Err(e) => Json(ApiResponse::error(format!("获取摄像头列表失败: {:#}", e))),
    }
}

async fn get_camera_by_id(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<Camera>> {
    match state.service_manager.camera_service.get_camera_by_id(&id).await {
        Ok(camera) => Json(ApiResponse::success(Some(camera))),
        Err(e) => Json(ApiResponse::error(format!("获取摄像头失败: {:#}", e))),
    }
}

async fn get_camera_list(
    State(state): State<Arc<AppState>>,
    Path(location_id): Path<String>,
) -> Json<ApiResponse<Vec<Camera>>> {
    match state.service_manager.camera_service.list(&location_id).await {
        Ok(list) => Json(ApiResponse::success(Some(list))),
        Err(e) => Json(ApiResponse::error(format!("获取摄像头列表失败: {:#}", e))),
    }
}