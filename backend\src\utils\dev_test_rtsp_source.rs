//utils/dev_test_rtsp_source.rs

use crate::model::rtsp_model::{CameraSource, RtspSource};

pub fn generate_test_rtsp_sources(count: usize, cameras_per_room: usize) -> Vec<RtspSource> {
    (0..count)
        .map(|i| {
            let room_id = format!("{}", i + 1);
            let cameras = (0..cameras_per_room)
                .map(|j| {
                    let camera_index = i * cameras_per_room + j + 1;
                    CameraSource {
                        camera_id: format!("{}", camera_index),
                        camera_name: format!("摄像机{}", camera_index),
                        camera_type: if j % 2 == 0 {
                            "全景".to_string()
                        } else {
                            "特写".to_string()
                        },
                        url: format!("rtsp://172.0.0.130:8554/stream"),
                    }
                })
                .collect();

            RtspSource {
                room_id,
                room_name: format!("测试房间{}", i + 1),
                cameras,
            }
        })
        .collect()
}
