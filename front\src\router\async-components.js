// src/router/async-components.js
export const asyncComponents = {
  AlgorithmDictionary: () =>
    import(
      /* webpackChunkName: "algorithm" */ "@/views/system/AlgorithmDictionary.vue"
    ),
  AreaManagement: () =>
    import(/* webpackChunkName: "area" */ "@/views/system/AreaManagement.vue"),
  UserManagement: () =>
    import(/* webpackChunkName: "user" */ "@/views/system/UserManagement.vue"),
  RoleManagement: () =>
    import(/* webpackChunkName: "role" */ "@/views/system/RoleManagement.vue"),
  OfficeManagement: () =>
    import(
      /* webpackChunkName: "office" */ "@/views/system/OfficeManagement.vue"
    ),
  Permission: () =>
    import(
      /* webpackChunkName: "permission" */ "@/views/system/Permission.vue"
    ),
  RoomPurposeType: () =>
    import(
      /* webpackChunkName: "roomPurposeType" */ "@/views/system/RoomPurposeType.vue"
    ),
  SystemSettings: () =>
    import(
      /* webpackChunkName: "systemSettings" */ "@/views/system/SystemSettings.vue"
    ),
  BuildingManagement: () =>
    import(
      /* webpackChunkName: "building" */ "@/views/config/BuildingManagement.vue"
    ),
  CameraManagement: () =>
    import(
      /* webpackChunkName: "camera" */ "@/views/config/CameraManagement.vue"
    ),
  LocationManagement: () =>
    import(
      /* webpackChunkName: "location" */ "@/views/config/LocationManagement.vue"
    ),
  Dashboard: () =>
    import(/* webpackChunkName: "dashboard" */ "@/views/Dashboard.vue"),
  NotFound: () =>
    import(/* webpackChunkName: "notFound" */ "@/views/NotFound.vue"),
  Login: () => import(/* webpackChunkName: "login" */ "@/views/Login.vue"),
  AlarmHistory: () =>
    import(
      /* webpackChunkName: "alarmHistory" */ "@/views/analysis/AlarmHistory.vue"
    ),
  ImportExport: () =>
    import(
      /* webpackChunkName: "importExport" */ "@/views/analysis/ImportExport.vue"
    ),
  Preview: () =>
    import(/* webpackChunkName: "preview" */ "@/views/analysis/Preview.vue"),
  RegionConfiguration: () =>
    import(
      /* webpackChunkName: "regionConfiguration" */ "@/views/analysis/RegionConfiguration.vue"
    ),
};
