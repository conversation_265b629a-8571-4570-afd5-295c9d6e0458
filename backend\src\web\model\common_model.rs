use crate::web::model::web_error::WebError;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fmt::Display;

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: i32,       // 状态码
    pub message: String, // 消息
    pub data: Option<T>, // 数据
}

impl<T> ApiResponse<T> {
    pub fn new(code: i32, message: String, data: Option<T>) -> Self {
        Self {
            code,
            message,
            data,
        }
    }

    pub fn success(data: Option<T>) -> Self {
        Self {
            code: 200,
            message: "成功".to_string(),
            data,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            code: 500,
            message,
            data: None,
        }
    }
}

impl<T> Default for ApiResponse<T> {
    fn default() -> Self {
        Self {
            code: 0,
            message: String::new(),
            data: None,
        }
    }
}

impl<T> From<Result<T, WebError>> for ApiResponse<T> {
    fn from(result: Result<T, WebError>) -> Self {
        match result {
            Ok(data) => Self::new(0, "success".to_string(), Some(data)),
            Err(e) => Self::new(e.code, e.message, None),
        }
    }
}

impl<T: std::fmt::Debug> Display for ApiResponse<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "ApiResponse {{ code: {}, message: {}, data: {:?} }}",
            self.code, self.message, self.data
        )
    }
}
