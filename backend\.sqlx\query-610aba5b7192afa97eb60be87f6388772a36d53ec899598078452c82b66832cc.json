{"db_name": "PostgreSQL", "query": "\n            INSERT INTO location (\n                location_id, location_name, location_type, building_id, \n                floor, purpose_type_id, area, capacity, is_active, created_at, updated_at\n            )\n            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\n            RETURNING *\n            ", "describe": {"columns": [{"ordinal": 0, "name": "location_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "location_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "location_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "building_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "floor", "type_info": "Int4"}, {"ordinal": 5, "name": "purpose_type_id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "area", "type_info": "Int4"}, {"ordinal": 7, "name": "capacity", "type_info": "Int4"}, {"ordinal": 8, "name": "is_active", "type_info": "Bool"}, {"ordinal": 9, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 10, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int4", "<PERSON><PERSON><PERSON><PERSON>", "Int4", "Int4", "Bool", "Timestamptz", "Timestamptz"]}, "nullable": [false, false, false, true, true, true, true, true, true, true, true]}, "hash": "610aba5b7192afa97eb60be87f6388772a36d53ec899598078452c82b66832cc"}