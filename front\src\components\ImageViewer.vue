<template>
    <div class="image-viewer">
      <div class="h-full flex flex-col">
        <!-- 工具栏 -->
        <div class="flex justify-center space-x-4 py-4">
          <el-button-group>
            <el-button @click="handleZoomIn" :icon="ZoomIn">放大</el-button>
            <el-button @click="handleZoomOut" :icon="ZoomOut">缩小</el-button>
            <el-button @click="handleRotate" :icon="Refresh">旋转</el-button>
            <el-button @click="handleReset" :icon="RefreshRight">重置</el-button>
            <el-button @click="$emit('close')" :icon="Close">关闭</el-button>
          </el-button-group>
        </div>
  
        <!-- 图片展示区域 -->
        <div 
          class="flex-1 flex items-center justify-center overflow-hidden"
          @wheel.prevent="handleWheel"
        >
          <img
            ref="viewerImage"
            :src="imageUrl"
            :alt="alt"
            class="transition-transform duration-200"
            :style="{
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              maxWidth: '100%',
              maxHeight: '100%'
            }"
            @error="handleError"
          />
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { ZoomIn, ZoomOut, Refresh, RefreshRight, Close } from '@element-plus/icons-vue'
  import defaultImage from '@/assets/default_image.png'
  
  const props = defineProps({
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: '图片'
    }
  })
  
  defineEmits(['close'])
  
  const scale = ref(1)
  const rotation = ref(0)
  const viewerImage = ref(null)
  
  const MIN_SCALE = 0.1
  const MAX_SCALE = 3
  const SCALE_STEP = 0.1
  
  const handleError = () => {
    if (viewerImage.value) {
      viewerImage.value.src = defaultImage
    }
  }
  
  const handleZoomIn = () => {
    if (scale.value < MAX_SCALE) {
      scale.value += SCALE_STEP
    }
  }
  
  const handleZoomOut = () => {
    if (scale.value > MIN_SCALE) {
      scale.value -= SCALE_STEP
    }
  }
  
  const handleWheel = (e) => {
    if (e.deltaY < 0) {
      handleZoomIn()
    } else {
      handleZoomOut()
    }
  }
  
  const handleRotate = () => {
    rotation.value = (rotation.value + 90) % 360
  }
  
  const handleReset = () => {
    scale.value = 1
    rotation.value = 0
  }
  </script>
  
  <style scoped>
  .image-viewer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2000;
  }
  </style>
  