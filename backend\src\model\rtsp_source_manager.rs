//model/rtsp_source_manager.rs
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use anyhow::Result;
use log::error;

use crate::model::rtsp_model::RtspSource;
use crate::config::{self};
use crate::utils;
use crate::apiclient;

pub struct RtspManager {
    sources: Arc<RwLock<Vec<RtspSource>>>,
    last_update: RwLock<Instant>,
    update_interval: Duration,
    config: Arc<config::Config>,
}

impl RtspManager {
    pub async fn new(
        update_interval: Duration,
        config: Arc<config::Config>
    ) -> Result<Self> {
        // 创建manager实例
        let manager = Self {
            sources: Arc::new(RwLock::new(Vec::new())), // 初始化为空
            last_update: RwLock::new(Instant::now()),
            update_interval,
            config,
        };
        
        // 立即加载初始数据
        let initial_sources = manager.get_source_by_config().await?;
        {
            let mut sources = manager.sources.write().unwrap();
            *sources = initial_sources;
        }
        
        Ok(manager)
    }

    pub async fn update_sources(&self) -> Result<()> {
        // Check if update is needed using a separate scope for the write lock
        {
            let last_update = self.last_update.read().map_err(|e| {
                error!("Failed to acquire read lock for last_update: {}", e);
                anyhow::anyhow!("Lock acquisition failed: {}", e)
            })?;
    
            if last_update.elapsed() < self.update_interval {
                //还不需要更新呢
                return Ok(());
            }
        }
    
        // 获取最新的rtsp摄像头信息
        let new_urls = match self.get_source_by_config().await {
            Ok(urls) => urls,
            Err(e) => {
                error!("Failed to get source from config: {}", e);
                return Err(anyhow::anyhow!("Failed to update sources: {}", e));
            }
        };
         
        // Update data and last_update time in a new scope
        {
            // Try to acquire write lock for sources
            let mut sources = self.sources.write().map_err(|e| {
                error!("Failed to acquire write lock for sources: {}", e);
                anyhow::anyhow!("Lock acquisition failed: {}", e)
            })?;

            //更新数据
            *sources = new_urls;
            
            // Try to acquire write lock for last_update
            let mut last_update = self.last_update.write().map_err(|e| {
                error!("Failed to acquire write lock for last_update: {}", e);
                anyhow::anyhow!("Lock acquisition failed: {}", e)
            })?;
            //重置最后更新时间
            *last_update = Instant::now();
        }
    
        Ok(())
    }    

    pub fn get_sources(&self) -> Arc<RwLock<Vec<RtspSource>>> {
        Arc::clone(&self.sources)
    }

    pub async fn get_source_by_config(&self) -> Result<Vec<RtspSource>> {
        let new_urls = match self.config.rtsp.rtsp_data_source.as_str() {
            "jcptapi" => apiclient::apiclient::get_rtsp_urls(&self.config).await?,
            "tsexcel" => {
                utils::excel_rtsp_source::read_excel_and_build_rtsp("tangshan.xlsx", "玉田", 2)?
            }
            "simulate" => utils::dev_test_rtsp_source::generate_test_rtsp_sources(1000, 2),
            _ => vec![],
        };
        Ok(new_urls)
    }
}