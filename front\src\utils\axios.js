// src/utils/axios.js  
import axios from 'axios'  

const instance = axios.create({  
  baseURL: window.__APP_CONFIG__.ycxwApiBaseUrl,  
  timeout: 5000,
  withCredentials: false,
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }  
})  

// 请求拦截器  
instance.interceptors.request.use(  
  config => {  
    if (!config.url.includes('/login')) {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers['x-shinow-token'] = `Bearer ${token}`
        //config.headers.authorization = `Bearer ${token}`
      }
    }    
    return config  
  },  
  error => {  
    return Promise.reject(error)  
  }  
)  

// 响应拦截器  
instance.interceptors.response.use(  
  response => {  
    return response  
  },  
  error => {  
    if (error.response.status === 401) {  
      // token过期处理  
      localStorage.removeItem('token')  
      window.location.href = '/login'  
    }  
    return Promise.reject(error)  
  }  
)  

export default instance  