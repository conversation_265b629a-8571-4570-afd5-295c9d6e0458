//src/web/model/office_organization.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// 数据库模型
#[derive(Debug, Serialize, Deserialize)]
pub struct OfficeOrganization {
    pub office_id: String,
    pub office_name: String,
    pub office_address: Option<String>,
    pub area_code: Option<String>,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

// 请求/响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct OfficeCreateRequest {
    pub office_name: String,
    pub office_address: Option<String>,
    pub area_code: Option<String>,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OfficeUpdateRequest {
    pub office_id: String,
    pub office_name: String,
    pub office_address: Option<String>,
    pub area_code: Option<String>,
    pub is_active: bool,
}