//src/web/api/sys_permission_api.rs
use axum::{
    extract::{Path, State},
    routing::{delete, post},
    Json, Router,
};
use log::{error, info};
use std::sync::Arc;

use crate::web::model::{
    appstate::AppState,
    common_model::ApiResponse,
    sys_permission::{
        PageRequest, PageResponse, PermissionCreateRequest, PermissionUpdateRequest, SysPermissionEntiry,
    },
};

pub fn register_sys_permission_api() -> Router<Arc<AppState>> {
    Router::new().nest("/sys_permission", sys_permission_routes())
}

fn sys_permission_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create", post(create))
        .route("/update", post(update))
        .route("/delete/:id", delete(delete_permission))
        .route("/page", post(get_page))
}

async fn create(
    State(state): State<Arc<AppState>>,
    <PERSON>son(req): Json<PermissionCreateRequest>,
) -> <PERSON><PERSON><ApiResponse<()>> {
    info!("Creating permission: {:?}", req);
    match state.service_manager.sys_permission_service.create(req).await {
        Ok(_) => {
            info!("Successfully created permission");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to create permission: {:#}", e);
            Json(ApiResponse::error(format!("创建失败: {:#}", e)))
        }
    }
}

async fn update(
    State(state): State<Arc<AppState>>,
    Json(req): Json<PermissionUpdateRequest>,
) -> Json<ApiResponse<()>> {
    info!("Updating permission: {:?}", req);
    match state.service_manager.sys_permission_service.update(req).await {
        Ok(_) => {
            info!("Successfully updated permission");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to update permission: {:#}", e);
            Json(ApiResponse::error(format!("更新失败: {:#}", e)))
        }
    }
}

async fn delete_permission(
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Json<ApiResponse<()>> {
    info!("Deleting permission with id: {}", id);
    match state.service_manager.sys_permission_service.delete(&id).await {
        Ok(_) => {
            info!("Successfully deleted permission");
            Json(ApiResponse::success(None))
        }
        Err(e) => {
            error!("Failed to delete permission: {:#}", e);
            Json(ApiResponse::error(format!("删除失败: {:#}", e)))
        }
    }
}

async fn get_page(
    State(state): State<Arc<AppState>>,
    Json(req): Json<PageRequest>,
) -> Json<ApiResponse<PageResponse<SysPermissionEntiry>>> {
    info!("Getting permission page: {:?}", req);
    match state.service_manager.sys_permission_service.get_page(req).await {
        Ok(page) => {
            info!("Successfully got permission page");
            Json(ApiResponse::success(Some(page)))
        }
        Err(e) => {
            error!("Failed to get permission page: {:#}", e);
            Json(ApiResponse::error(format!("获取失败: {:#}", e)))
        }
    }
}