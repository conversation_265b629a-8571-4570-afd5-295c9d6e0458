//rtsp/frame_consumer_grpc.rs:
use crate::model::frame_queue::FrameQueue;
use anyhow::{Result, Context};
use image::Rgb;
use image::{ImageBuffer, ImageFormat};
use log::{error, info};
use std::sync::Arc;
use tokio::sync::broadcast;
use tokio::sync::Semaphore;
use tonic::transport::Channel;
use std::time::Duration;
use tokio::time::sleep;

use crate::proto::shinow::image::analysis::{
    Algorithm, AlgorithmParam, CameraImageData, ImageAnalysisRequest, ImageAnalysisResponse,
    RoomImageData,
};

use crate::model::rtsp_model::RoomFrame;

const MAX_RETRY_ATTEMPTS: u32 = 3;
const RETRY_DELAY_MS: u64 = 1000;

use crate::config::Config;
use crate::proto::shinow::image::analysis::image_analysis_service_client::ImageAnalysisServiceClient;

pub struct GrpcFrameConsumer {
    config: Arc<Config>,
    frame_queue: Arc<FrameQueue>,
    grpc_endpoint: String,                  //  gRPC 服务器端端点
    shutdown_signal: broadcast::Sender<()>, // 关闭信号
}

impl GrpcFrameConsumer {
    pub fn new(config: Arc<Config>, frame_queue: Arc<FrameQueue>, grpc_endpoint: &String) -> Self {
        let (shutdown_signal, _) = broadcast::channel(1);
        GrpcFrameConsumer {
            config,
            frame_queue,                          // 图像帧队列
            grpc_endpoint: grpc_endpoint.clone(), //  gRPC 服务器端端点
            shutdown_signal,                      // 关闭信号
        }
    }


    async fn create_grpc_client(
        config: &Config,
        grpc_endpoint: &str,
        thread_id: i32,
    ) -> Result<ImageAnalysisServiceClient<Channel>> {
        let mut retry_count = 0;
        while retry_count < MAX_RETRY_ATTEMPTS {
            let channel_result = Channel::from_shared(grpc_endpoint.to_string())
                .context("Failed to create channel")?
                .timeout(Duration::from_secs(config.grpcConfig.grpc_call_timeout as u64))
                .initial_stream_window_size(config.grpcConfig.initial_stream_window_size * 1024 * 1024)
                .initial_connection_window_size(config.grpcConfig.initial_connection_window_size * 1024 * 1024)
                .connect_timeout(Duration::from_secs(config.grpcConfig.grpc_connect_timeout as u64))
                .tcp_keepalive(Some(Duration::from_secs(
                    config.grpcConfig.grpc_keepalive_interval as u64,
                )))
                .connect()
                .await;

            match channel_result {
                Ok(channel) => {
                    info!("Successfully created gRPC client in thread {}", thread_id);
                    return Ok(ImageAnalysisServiceClient::new(channel));
                }
                Err(e) => {
                    retry_count += 1;
                    error!(
                        "Failed to create gRPC client in thread {} (attempt {}/{}): {}",
                        thread_id, retry_count, MAX_RETRY_ATTEMPTS, e
                    );
                    
                    if retry_count < MAX_RETRY_ATTEMPTS {
                        sleep(Duration::from_millis(RETRY_DELAY_MS)).await;
                    } else {
                        return Err(e).context("Failed to create gRPC client after max retries");
                    }
                }
            }
        }
        Err(anyhow::anyhow!("Failed to create gRPC client after max retries"))
    }    

    async fn process_frame(
        client: &mut ImageAnalysisServiceClient<Channel>,
        room_frame: RoomFrame,
    ) -> Result<ImageAnalysisResponse> {
        let algorithm = Algorithm {
            algorithm_id: "0".to_string(),
            algorithm_name: "0".to_string(),
            parameters: vec![AlgorithmParam {
                param_key: "threshold".to_string(),
                param_value: "0.5".to_string(),
            }],
        };

        let cameras: Result<Vec<CameraImageData>> = room_frame
            .frames
            .into_iter()
            .map(|(_, frame)| {
                let image_binary = ImageBuffer::<Rgb<u8>, _>::from_raw(
                    frame.width as u32,
                    frame.height as u32,
                    frame.data.to_vec(),
                )
                .ok_or_else(|| {
                    anyhow::anyhow!(
                        "Failed to create image buffer for frame: {}x{}",
                        frame.width,
                        frame.height
                    )
                })
                .and_then(|img| {
                    let mut buffer = std::io::Cursor::new(Vec::new());
                    img.write_to(&mut buffer, ImageFormat::Jpeg)
                        .context("Failed to encode image")?;
                    Ok(buffer.into_inner())
                })?;

                Ok(CameraImageData {
                    camera_id: frame.camera_id,
                    camera_name: frame.camera_name,
                    camera_type: frame.camera_type,
                    image_binary,
                    algorithms: vec![algorithm.clone()],
                })
            })
            .collect();

        let room_data = RoomImageData {
            room_id: room_frame.room_id,
            room_name: room_frame.room_name,
            cameras: cameras?,
        };

        let request = tonic::Request::new(ImageAnalysisRequest {
            rooms: vec![room_data],
        });

        let start_time = std::time::Instant::now();
        let response = client
            .analyze_images(request)
            .await
            .context("Failed to process room frames via gRPC")?;
        let end_time = std::time::Instant::now();
        let duration = end_time.duration_since(start_time);
        
        info!(
            "Received response - duration: {:?}",
            duration
        );

        Ok(response.into_inner())
    }

    pub async fn start(&self) -> Result<()> {
        let queue = Arc::clone(&self.frame_queue);
        let config = Arc::clone(&self.config);
        let semaphore = Arc::new(Semaphore::new(config.rtsp.consumer_thread_count));
        let mut handles = Vec::new();

        for thread_id in 0..config.rtsp.consumer_thread_count {
            let queue = Arc::clone(&queue);
            let semaphore = Arc::clone(&semaphore);
            let grpc_endpoint = self.grpc_endpoint.clone();
            let mut shutdown_rx = self.shutdown_signal.subscribe();
            let config = Arc::clone(&config);

            let handle = tokio::spawn(async move {
                loop {
                    tokio::select! {
                        shutdown_result = shutdown_rx.recv() => {
                            match shutdown_result {
                                Ok(_) => {
                                    info!("Consumer thread {} received shutdown signal", thread_id);
                                    break;
                                }
                                Err(e) => {
                                    error!("Shutdown channel error in thread {}: {}", thread_id, e);
                                    break;
                                }
                            }
                        }
                        _ = async {
                            let _permit = semaphore.acquire().await.map_err(|e| {
                                error!("Failed to acquire semaphore: {}", e);
                            });

                            match Self::create_grpc_client(&config, &grpc_endpoint, thread_id as i32).await {
                                Ok(mut client) => {
                                    if let Some(room_frame) = queue.pop() {
                                        match Self::process_frame(&mut client, room_frame).await {
                                            Ok(response) => {
                                                info!(
                                                    "Successfully processed frame - status: {}, message: {}",
                                                    response.status_code, response.message
                                                );
                                            }
                                            Err(e) => {
                                                error!("Error processing frame: {}", e);
                                            }
                                        }
                                    } else {
                                        tokio::task::yield_now().await;
                                    }
                                }
                                Err(e) => {
                                    error!("Failed to create gRPC client: {}", e);
                                    sleep(Duration::from_millis(RETRY_DELAY_MS)).await;
                                }
                            }
                        } => {}
                    }
                }
                info!("Consumer thread {} shutting down gracefully", thread_id);
            });
            handles.push(handle);
        }

        for handle in handles {
            handle.await.context("Failed to join consumer thread")?;
        }
        Ok(())
    }


    // pub async fn start(&self) -> anyhow::Result<()> {
    //     let queue = Arc::clone(&self.frame_queue);
    //     let config = Arc::clone(&self.config);
    //     let semaphore = Arc::new(Semaphore::new(config.rtsp.consumer_thread_count));
    //     let mut handles = Vec::new();
    //     let consumer_threads = config.rtsp.consumer_thread_count;

    //     for thread_id in 0..consumer_threads {
    //         let queue = Arc::clone(&queue);
    //         let semaphore = Arc::clone(&semaphore);
    //         let grpc_endpoint = self.grpc_endpoint.clone();
    //         let mut shutdown_rx = self.shutdown_signal.subscribe();
    //         let config = Arc::clone(&config);

    //         let handle = tokio::spawn(async move {
    //             loop {
    //                 let grpc_endpoint = grpc_endpoint.clone();
    //                 tokio::select! {
    //                     shutdown_result = shutdown_rx.recv() => {
    //                         match shutdown_result {
    //                             Ok(_) => {
    //                                 info!("Consumer thread {} received shutdown signal", thread_id);
    //                                 break;
    //                             }
    //                             Err(e) => {
    //                                 error!("Shutdown channel error in thread {}: {}", thread_id, e);
    //                                 break;
    //                             }
    //                         }
    //                     }
    //                     _ = async {
    //                         let _permit = semaphore.acquire().await.unwrap();
    //                         let mut client = match Channel::from_shared(grpc_endpoint.clone())
    //                         .unwrap()
    //                         .timeout(std::time::Duration::from_secs(config.grpcConfig.grpc_call_timeout as u64))
    //                         .initial_stream_window_size(config.grpcConfig.initial_stream_window_size * 1024 * 1024)
    //                         .initial_connection_window_size(config.grpcConfig.initial_connection_window_size * 1024 * 1024)
    //                         .connect_timeout(std::time::Duration::from_secs(config.grpcConfig.grpc_connect_timeout as u64))
    //                         .tcp_keepalive(Some(std::time::Duration::from_secs(config.grpcConfig.grpc_keepalive_interval as u64)))
    //                         .connect()
    //                         .await
    //                         {
    //                             Ok(channel) => ImageAnalysisServiceClient::new(channel),
    //                             Err(e) => {
    //                                 error!("Failed to create gRPC client in thread {}: {}", thread_id, e);
    //                                 ImageAnalysisServiceClient::connect(grpc_endpoint).await.unwrap()
    //                             }
    //                         };

    //                         if let Some(room_frame) = queue.pop() {
    //                             // 创建算法配置
    //                             let algorithm = Algorithm {
    //                                 // algorithm_id: "1".to_string(),
    //                                 // algorithm_name: "person_detection".to_string(),
    //                                 algorithm_id: "0".to_string(),
    //                                 algorithm_name: "0".to_string(),
    //                                 parameters: vec![AlgorithmParam {
    //                                     param_key: "threshold".to_string(),
    //                                     param_value: "0.5".to_string(),
    //                                 }],
    //                             };

    //                             // 为房间中的每个摄像头创建CameraImageData
    //                             let cameras: Vec<CameraImageData> = room_frame
    //                                 .frames
    //                                 .into_iter()
    //                                 .map(|(_, frame)| {
    //                                     //以下代码用于将rgb的图像数据压缩转码为Jpg格式去调用
    //                                     let image_encoded = match ImageBuffer::<Rgb<u8>, _>::from_raw(
    //                                         frame.width as u32,
    //                                         frame.height as u32,
    //                                         frame.data.to_vec(),
    //                                     ) {
    //                                         Some(img) => {
    //                                             let mut buffer = std::io::Cursor::new(Vec::new());
    //                                             match img.write_to(&mut buffer, ImageFormat::Jpeg) {
    //                                                 Ok(_) => Ok(buffer.into_inner()),
    //                                                 Err(e) => Err(anyhow::anyhow!(
    //                                                     "Failed to encode image: {}",
    //                                                     e
    //                                                 )),
    //                                             }
    //                                         }
    //                                         None => Err(anyhow::anyhow!(
    //                                             "Failed to create image buffer for frame: {}x{}",
    //                                             frame.width,
    //                                             frame.height
    //                                         )),
    //                                     };

    //                                     let image_binary = match image_encoded {
    //                                         Ok(image_binary) => image_binary,
    //                                         Err(e) => {
    //                                             error!("Failed to encode image: {}", e);
    //                                             Vec::new()
    //                                         }
    //                                     };

    //                                     CameraImageData {
    //                                         camera_id: frame.camera_id,
    //                                         camera_name: frame.camera_name,
    //                                         camera_type: frame.camera_type,
    //                                         image_binary,
    //                                         algorithms: vec![algorithm.clone()],
    //                                     }
    //                                 })
    //                                 .collect();

    //                             // 创建房间数据
    //                             let room_data = RoomImageData {
    //                                 room_id: room_frame.room_id,
    //                                 room_name: room_frame.room_name,
    //                                 cameras,
    //                             };

    //                             // 创建请求
    //                             let request = tonic::Request::new(ImageAnalysisRequest {
    //                                 rooms: vec![room_data],
    //                             });

    //                             let start_time = std::time::Instant::now();
    //                             // 发送请求并处理响应
    //                             match client.analyze_images(request).await {
    //                                 Ok(response) => {
    //                                     let response = response.into_inner();
    //                                     let end_time = std::time::Instant::now();
    //                                     let duration = end_time.duration_since(start_time);
    //                                     error!(
    //                                         "Received response - status: {}, message: {}, duration: {:?}",
    //                                         response.status_code, response.message, duration
    //                                     );
    //                                 }
    //                                 Err(e) => {
    //                                     error!("Failed to process room frames via gRPC: {}", e);
    //                                 }
    //                             }
    //                         } else {
    //                             tokio::task::yield_now().await;
    //                         }
    //                     } => {}
    //                 }
    //             }
    //             info!("Consumer thread {} shutting down gracefully", thread_id);
    //         });

    //         handles.push(handle);
    //     }

    //     // 等待所有消费者线程完成
    //     for handle in handles {
    //         handle.await?;
    //     }

    //     Ok(())
    // }

    // 添加关闭方法
    pub async fn shutdown(&self) {
        info!("Initiating graceful shutdown of GRPC consumer...");
        let _ = self.shutdown_signal.send(());
    }
}
