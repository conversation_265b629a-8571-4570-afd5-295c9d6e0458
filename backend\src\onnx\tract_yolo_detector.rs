//onnx/tract_yolo_detector.rs
use anyhow::Result;
use image::{DynamicImage, RgbImage};
use rayon::prelude::*;
use std::cmp::Ordering;
use std::path::Path;
use std::time::Instant;
use tract_ndarray::{s, ArrayView2, Ix2};
use tract_onnx::prelude::*;
use async_trait::async_trait;
use std::fs;
use log::debug;

use crate::onnx::yolo_detector::ModelConfig;
use crate::onnx::yolo_detector::{DetectionBox, ModelDetectionResult, ObjectDetector};
use crate::onnx::yolo_utils::calculate_iou;
use crate::onnx::yolo_utils::YOLOV8_CLASS_LABELS;

pub struct TractYoloDetector {
    model: SimplePlan<TypedFact, Box<dyn TypedOp>, Graph<TypedFact, Box<dyn TypedOp>>>,
    config: ModelConfig,
}

#[async_trait]
impl ObjectDetector for TractYoloDetector {
    async fn init(config: ModelConfig) -> Result<Self> {
        let model = tract_onnx::onnx()
            .model_for_path(&config.config.rtsp.yolo_model_path)?
            .with_input_fact(
                0,
                f32::fact([1, 3, config.input_width as i64, config.input_height as i64]).into(),
            )?
            .into_optimized()?
            .into_runnable()?;

        Ok(Self { model, config })
    }

    async fn detect_from_memory(&self, image_data: &[u8]) -> Result<ModelDetectionResult> {
        let start_time = Instant::now();
        debug!("开始从内存中检测.....");
        let image = image::load_from_memory(image_data)?;
        let (has_person, boxes) = self.process_single_image_blocking(&image)?;

        Ok(ModelDetectionResult {
            num_persons: boxes.len(),
            boxes,
            processing_time_ms: start_time.elapsed().as_millis(),
            annotated_image: None,
        })
    }

    async fn detect_from_image(&self, image: &DynamicImage) -> Result<ModelDetectionResult> {
        let start_time = Instant::now();
        debug!("开始使用内存图像检测.....");
        let (has_person, boxes) = self.process_single_image_blocking(image)?;
        debug!("检测完成，耗时: {:?}, 检测结果有无人员: {:?}", start_time.elapsed(), has_person);
        Ok(ModelDetectionResult {
            num_persons: boxes.len(),
            boxes,
            processing_time_ms: start_time.elapsed().as_millis(),
            annotated_image: None,
        })
    }

    async fn detect_from_file(&self,image_path: &Path) -> Result<ModelDetectionResult> {
        let start_time = Instant::now();
        let image_data = fs::read(image_path)?;
        let image = image::load_from_memory(&image_data)?;
        let (has_person, boxes) = self.process_single_image_blocking(&image)?;

        Ok(ModelDetectionResult {
            num_persons: boxes.len(),
            boxes,
            processing_time_ms: start_time.elapsed().as_millis(),
            annotated_image: None,
        })
    }

    fn get_config(&self) -> &ModelConfig {
        &self.config
    }
}

impl TractYoloDetector {
    // 初始化检测器
    pub async fn new(config: ModelConfig) -> Result<Self> {
        let model = tract_onnx::onnx()
            .model_for_path(&config.config.rtsp.yolo_model_path)?
            .with_input_fact(0, f32::fact([1, 3, 640, 640]).into())?
            .into_optimized()?            
            .into_runnable()?;

        Ok(Self { model, config })
    }

    // 批量处理图片
    pub fn detect_batch(&self, image_paths: &[String]) -> Vec<Result<ModelDetectionResult>> {
        image_paths
            .par_iter()
            .map(|path| {
                let iter_start = Instant::now();
                let result = self.process_single_image(path);
                let processing_time = iter_start.elapsed();

                result.map(|(has_person, bboxes)| ModelDetectionResult {
                    num_persons: bboxes.len(),
                    boxes: bboxes,
                    processing_time_ms: processing_time.as_millis() as u128,
                    annotated_image: None,
                })
            })
            .collect()
    }

    // 处理单张图片
    pub fn process_single_image(&self, image_path: &str) -> Result<(bool, Vec<DetectionBox>)> {
        let raw_image = image::open(image_path)?;
        let processed_tensor = self.preprocess_image(&raw_image)?;

        let forward = self.model.run(tvec![processed_tensor.into()])?;
        let results = forward[0].to_array_view::<f32>()?;
        let results: ArrayView2<f32> = results.into_dimensionality::<Ix2>()?;

        let mut bboxes = self.process_results(&results, &raw_image)?;
        let filtered_bboxes = self.post_process(bboxes);

        Ok((!filtered_bboxes.is_empty(), filtered_bboxes))
    }

    // 处理单张图片(阻塞方式)
    pub fn process_single_image_blocking(
        &self,
        image: &DynamicImage,
    ) -> Result<(bool, Vec<DetectionBox>)> {
        let start_time = Instant::now();
        let processed_tensor = self.preprocess_image(image)?;

        let forward = self.model.run(tvec![processed_tensor.into()])?;
        let results = forward[0].to_array_view::<f32>()?;
        let results: ArrayView2<f32> = results.into_dimensionality::<Ix2>()?;

        let mut bboxes = self.process_results(&results, image)?;
        let filtered_bboxes = self.post_process(bboxes);
        debug!("处理单张图片，耗时: {:?}, 检测结果: {:?}", start_time.elapsed(), filtered_bboxes);

        //判断label是否为person
        let is_person = filtered_bboxes.iter().any(|bbox| bbox.class_name == "person");

        Ok((is_person, filtered_bboxes))
    }

    // 图片预处理
    pub fn preprocess_image(&self, raw_image: &DynamicImage) -> Result<Tensor> {
        let (width, height) = (raw_image.width() as f32, raw_image.height() as f32);
        let scale = 640.0 / width.max(height);
        let new_width = (width * scale) as u32;
        let new_height = (height * scale) as u32;

        let resized = image::imageops::resize(
            &raw_image.to_rgb8(),
            new_width,
            new_height,
            image::imageops::FilterType::Triangle,
        );

        let mut padded = RgbImage::new(640, 640);
        let offset_x = (640 - new_width) / 2;
        let offset_y = (640 - new_height) / 2;

        image::imageops::replace(&mut padded, &resized, offset_x as i64, offset_y as i64);

        let tensor = tract_ndarray::Array4::from_shape_fn((1, 3, 640, 640), |(_, c, y, x)| {
            padded.get_pixel(x as u32, y as u32)[c] as f32 / 255.0
        });

        Ok(tensor.into())
    }

    // 处理模型输出结果
    pub fn process_results(
        &self,
        results: &ArrayView2<f32>,
        raw_image: &DynamicImage,
    ) -> Result<Vec<DetectionBox>> {
        let mut bbox_vec = Vec::new();
        let num_classes = 80;
        let img_width = raw_image.width() as f32;
        let img_height = raw_image.height() as f32;

        for i in 0..results.shape()[0] {
            let row = results.slice(s![i, ..]);

            let class_scores = row.slice(s![4..4 + num_classes]);
            let (class_id, class_prob) = class_scores
                .iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap_or(Ordering::Equal))
                .unwrap_or((0, &0.0));

            if class_id == 0 && *class_prob >= self.config.confidence_threshold {
                let x = row[0];
                let y = row[1];
                let w = row[2];
                let h = row[3];

                let x1 = (x - w / 2.0) * img_width;
                let y1 = (y - h / 2.0) * img_height;
                let x2 = (x + w / 2.0) * img_width;
                let y2 = (y + h / 2.0) * img_height;

                let bbox = DetectionBox {
                    class_name: YOLOV8_CLASS_LABELS[class_id as usize].to_string(),
                    confidence: *class_prob,
                    bbox: [x1, y1, x2, y2],
                };

                bbox_vec.push(bbox);
            }
        }
        Ok(bbox_vec)
    }

    // 后处理：非极大值抑制
    fn post_process(&self, boxes: Vec<DetectionBox>) -> Vec<DetectionBox> {
        non_maximum_suppression(boxes, self.config.iou_threshold)
    }
}

// 非极大值抑制实现
pub fn non_maximum_suppression(
    mut boxes: Vec<DetectionBox>,
    iou_threshold: f32,
) -> Vec<DetectionBox> {
    boxes.sort_by(|a, b| {
        b.confidence
            .partial_cmp(&a.confidence)
            .unwrap_or(Ordering::Equal)
    });

    let mut keep = Vec::new();
    while !boxes.is_empty() {
        let current = boxes.remove(0);
        keep.push(current.clone());
        boxes.retain(|box_| calculate_iou(&current, box_) <= iou_threshold);
    }
    keep
}