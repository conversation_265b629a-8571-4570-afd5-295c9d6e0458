<!-- src/layouts/Layout.vue -->
<template>
  <div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <aside
      class="fixed left-0 top-0 h-full bg-gray-800 text-white transition-all duration-300"
      :class="[isCollapsed ? 'w-16' : 'w-64']"
    >
      <!-- Logo -->
      <div
        class="h-16 flex items-center justify-start border-b border-gray-700 px-4"
      >
        <img
          src="@/assets/logo.jpg"
          :class="[isCollapsed ? 'w-8' : 'w-12']"
          alt="Logo"
          class="mr-2"
        />
        <span
          v-if="!isCollapsed"
          class="text-white font-medium text-lg whitespace-nowrap overflow-hidden"
        >
          异常行为分析系统
        </span>
      </div>

      <!-- 菜单 -->
      <nav class="mt-4">
        <SideMenu :collapsed="isCollapsed" />
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div
      :class="[isCollapsed ? 'ml-16' : 'ml-64']"
      class="transition-all duration-300"
    >
      <!-- 顶部导航栏 -->
      <header
        class="h-16 bg-white shadow-sm flex items-center justify-between px-4"
      >
      <div class="flex items-center">
        <button
          @click="toggleCollapse"
          class="p-2 rounded-lg hover:bg-gray-100"
        >
          <MenuFoldOutlined v-if="isCollapsed" />
          <MenuUnfoldOutlined v-else />
        </button>

        <!-- 当前路径显示 -->
        <div class="ml-4 flex items-center text-gray-600">
            <span v-if="currentPath.length > 0">
              {{ currentPath.join(' / ') }}
            </span>
        </div>
      </div>
        

        <!-- 用户信息下拉菜单 -->
        <div class="relative">
          <button @click="toggleUserMenu" class="flex items-center space-x-2">
            <!-- Tailwind CSS 头像 -->
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium"
              :class="avatarColorClass"
            >
              {{ userInitials }}
            </div>
            <span>{{ userName }}</span>
          </button>

          <div
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1"
          >
            <a @click="changePassword" class="block px-4 py-2 hover:bg-gray-100"
              >修改密码</a
            >
            <a @click="switchRole" class="block px-4 py-2 hover:bg-gray-100"
              >切换角色</a
            >
            <a @click="logout" class="block px-4 py-2 hover:bg-gray-100"
              >退出登录</a
            >
          </div>
        </div>
      </header>

      <!-- 内容区域 -->
      <main class="p-6">
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed ,watch} from "vue";
import { useUserStore } from "@/stores/user";
import { useRouter,useRoute } from "vue-router";
import SideMenu from "@/components/SideMenu.vue";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons-vue";

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const isCollapsed = ref(false);
const showUserMenu = ref(false);
const currentPath = ref([]);

const userName = computed(() => userStore.userName);

const userInitials = computed(() => {
  const name = userName.value || "";
  return name.charAt(0).toUpperCase();
});

// 随机生成头像背景色
const avatarColorClass = computed(() => {
  const colors = [
    "bg-blue-500",
    "bg-green-500",
    "bg-yellow-500",
    "bg-red-500",
    "bg-purple-500",
    "bg-pink-500",
    "bg-indigo-500",
  ];
  // 根据用户名生成固定的颜色索引，这样同一用户永远是同一个颜色
  const index =
    userName.value
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  return colors[index];
});

// const menus = [
//   {
//     title: "系统配置",
//     icon: "SettingOutlined",
//     children: [
//       { title: "行政区划管理", path: "/system/area" },
//       { title: "组织机构管理", path: "/system/org" },
//       { title: "用户管理", path: "/system/user" },
//       { title: "角色管理", path: "/system/role" },
//       { title: "权限管理", path: "/system/permission" },
//       { title: "系统参数设定", path: "/system/params" },
//       { title: "算法字典维护", path: "/system/algorithm" },
//       { title: "房间用途字典维护", path: "/system/room-usage" },
//     ],
//   },
//   {
//     title: "异常行为分析配置",
//     icon: "ToolOutlined",
//     children: [
//       { title: "楼宇信息管理", path: "/config/building" },
//       { title: "位置信息管理", path: "/config/location" },
//       { title: "摄像头信息管理", path: "/config/camera" },
//     ],
//   },
//   {
//     title: "异常行为分析管理",
//     icon: "AlertOutlined",
//     children: [
//       { title: "异常行为检测规则设定", path: "/analysis/rules" },
//       { title: "历史报警记录", path: "/analysis/history" },
//       { title: "实时预览", path: "/analysis/realtime" },
//       { title: "批量规则定义", path: "/analysis/batch-rules" },
//     ],
//   },
// ];

// 监听路由变化更新面包屑
watch(
  () => route.matched,
  (matched) => {
    currentPath.value = matched
      .filter(route => route.meta?.title)
      .map(route => route.meta.title);
  },
  { immediate: true }
);

// 方法实现
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
};

const changePassword = () => {
  // 实现修改密码逻辑
};

const switchRole = () => {
  // 实现切换角色逻辑
};

const logout = async () => {
  try {
    showUserMenu.value = false;
    localStorage.removeItem("token");
    localStorage.removeItem("userInfo");
    localStorage.removeItem("menus");
    router.push("/login");
  } catch (error) {
    console.error("退出登录失败:", error);
  }
};
</script>

<style lang="scss" scoped>
.min-h-screen {
  min-height: 100vh;
}
</style>