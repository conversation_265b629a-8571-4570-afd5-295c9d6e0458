[package]
name = "rtspcap20241111"
version = "0.1.0"
edition = "2021"

[dependencies]
ffmpeg-next = "7.1.0"
ffmpeg-sys-next = "7.1.0"
tokio = { version = "1.42.0", features = ["full"] }
anyhow = "1.0.94"
futures = "0.3"
image = "0.25.5"
imageproc = "0.25.0"
log4rs = "1.3"
log = "0.4"
chrono = { version = "0.4", features = ["serde"] }
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1.0.215", features = ["derive"] }
serde_json = "1.0.133"
serde_yaml = "0.9"
serde_qs = "0.12"
calamine = "0.26.1"
thiserror = "2.0.4"
num_cpus = "1.16.0"
uuid = { version = "1.11.0", features = ["v4","serde"] }
#jpeg-encoder = { version = "0.6.0", features = ["simd"] }
tonic = "0.12.3"
prost = "0.13.3"
bytes = "1.9.0"
axum = {version="0.7.9",features=["default","multipart","ws"]}
tokio-tungstenite = "0.21.0"
hyper = { version = "1.5.1", features = ["full"] }
tower = "0.5.1"
tower-http = { version = "0.6.2", features = ["full"] }
http = "1.2.0"  
sqlx = { version = "0.8", features = [ "runtime-tokio", "postgres","chrono","uuid", "bigdecimal" ] }
jsonwebtoken = "9.3.0"
time = "0.3"
bcrypt = "0.16"
once_cell = "1.8"
clap = { version = "4.5.23", features = ["derive"] }
rayon = "1.8"
tract-onnx = "0.21.8"
crossbeam-queue = "0.3"
ab_glyph = "0.2"
ndarray = "0.16"
ort = { git = "https://github.com/pykeio/ort", tag = "v2.0.0-rc.9",features=["default","load-dynamic","half","ndarray","tensorrt"]} 
base64 = "0.22.1"
rand = "0.8.5"
futures-util = "0.3"
ftail = "0.1"
threadpool = "1.8"
async-trait = "0.1"
dotenv = "0.15"
argon2 = "0.5.3"
regex = "1.10"
webrtc = "0.12.0"
sysinfo = { version = "0.33.0", features = ["default"] }

[features]
load-dynamic = [ "ort/load-dynamic" ]

[build-dependencies]
tonic-build = "0.12.3"

[target.'cfg(not(windows))'.dependencies]
jemallocator = "0.5.4"

# [profile.release]
# debug = true

[profile.dev]
warnings = false
rpath = true   #ort 需要

[profile.release]
warnings = false
rpath = true   #ort 需要


[target.x86_64-unknown-linux-gnu]
rustflags = [ "-Clink-args=-Wl,-rpath,\\$ORIGIN" ]

[target.x86_64-unknown-linux-musl]
rustflags = ["-C", "target-feature=+crt-static"]