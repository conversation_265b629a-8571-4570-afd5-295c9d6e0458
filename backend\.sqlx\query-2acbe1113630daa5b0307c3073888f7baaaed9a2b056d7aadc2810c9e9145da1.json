{"db_name": "PostgreSQL", "query": "\n                    SELECT code, name, parent_code, level, is_active as \"is_active!\", created_at, updated_at\n                    FROM sys_area_info \n                    WHERE level = 1 AND is_active = true\n                    ORDER BY code\n                    ", "describe": {"columns": [{"ordinal": 0, "name": "code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "parent_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "level", "type_info": "Int4"}, {"ordinal": 4, "name": "is_active!", "type_info": "Bool"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": []}, "nullable": [false, false, true, false, true, true, true]}, "hash": "2acbe1113630daa5b0307c3073888f7baaaed9a2b056d7aadc2810c9e9145da1"}