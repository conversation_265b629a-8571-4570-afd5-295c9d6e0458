// src/web/model/alarm_record.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Deserialize, Serialize, sqlx::FromRow)]
pub struct AlarmRecord {
    pub alarm_id: String,
    pub camera_id: String,
    pub algorithm_id: String,
    pub alarm_time: DateTime<Utc>,
    pub alarm_image_url: Option<String>,
    pub alarm_video_url: Option<String>,
    pub is_pushed: Option<bool>,
    pub push_time: Option<DateTime<Utc>>,
    pub is_confirmed: Option<bool>,
    pub confirm_user_id: Option<String>,
    pub confirm_time: Option<DateTime<Utc>>,
    pub confirm_note: Option<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct AlarmRecordQueryRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub camera_name: Option<String>,
    pub algorithm_id: Option<String>,
    pub is_confirmed: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct AlarmRecordConfirmRequest {
    pub alarm_id: String,
    pub confirm_user_id: String,
    pub confirm_note: Option<String>,
}

#[derive(Debug, Serialize, sqlx::FromRow)]
pub struct AlarmRecordDetailResponse {
    pub area_name: Option<String>,
    pub office_name: Option<String>,
    pub building_name: Option<String>,
    pub location_name: Option<String>,
    pub room_purpose_type_name: Option<String>,
    pub room_name: Option<String>,
    pub camera_name: Option<String>,
    pub algorithm_name: Option<String>,
    pub alarm_id: String,
    pub camera_id: String,
    pub algorithm_id: String,
    pub alarm_time: DateTime<Utc>,
    pub alarm_image_url: Option<String>,
    pub alarm_video_url: Option<String>,
    pub is_pushed: Option<bool>,
    pub push_time: Option<DateTime<Utc>>,
    pub is_confirmed: Option<bool>,
    pub confirm_user_id: Option<String>,
    pub confirm_time: Option<DateTime<Utc>>,
    pub confirm_note: Option<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct PageResponse<T> {
    pub total: i64,
    pub items: Vec<T>,
}
