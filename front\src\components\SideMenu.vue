<!-- src/components/SideMenu.vue -->
<template>
  <el-menu
    :default-active="route.path"
    :default-openeds="Array.from(openedMenus)"
    :collapse="collapsed"
    class="side-menu"
    :class="{ 'w-64': !collapsed, 'w-16': collapsed }"
    :background-color="menuBgColor"
    :text-color="menuTextColor"
    :active-text-color="menuActiveColor"
    router
  >
    <template v-for="menu in userMenus" :key="menu.permission_id">
      <menu-item-recursive :menu-item="menu" :level="0" />
    </template>
  </el-menu>
</template>

<script setup>
import { computed, provide, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useUserStore } from "@/stores/user";
import MenuItemRecursive from "./MenuItemRecursive.vue";
import { ElMenu, ElMenuItem, ElSubMenu, ElIcon } from "element-plus";

// 创建一个 ref 来存储打开的菜单
const openedMenus = ref(new Set());

// 提供给子组件使用
provide("openedMenus", openedMenus);

// 在组件挂载时恢复保存的菜单状态
onMounted(() => {
  const savedMenus = localStorage.getItem("openedMenus");
  if (savedMenus) {
    openedMenus.value = new Set(JSON.parse(savedMenus));
  }
});

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
});

// 菜单主题配置
const menuBgColor = "#1e293b";
const menuTextColor = "#fff";
const menuActiveColor = "#409EFF";

const route = useRoute();
const userStore = useUserStore();

const userMenus = computed(() => {
  const menus = userStore.menus || [];
  return menus.sort(
    (a, b) => (a.sort_order ?? 999999) - (b.sort_order ?? 999999)
  );
});
</script>

<style lang="scss" scoped>
.side-menu {
  @apply h-full border-none overflow-y-auto overflow-x-hidden;

  height: 100%; // 确保菜单占满父容器  
  overflow-y: auto; // 确保可以垂直滚动    
  
  // 美化滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    @apply flex items-center;
    height: 50px;
    line-height: 50px;
  }

  :deep(.el-menu-item.is-active) {
    background-color: rgba(64, 158, 255, 0.1);
  }

  // 添加子菜单样式
  :deep(.el-menu--inline) {
    background-color: rgba(0, 0, 0, 0.1);

    .el-menu-item {
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &.is-active {
        background-color: rgba(64, 158, 255, 0.2);
      }
    }
  }

  // 添加子菜单标题样式
  :deep(.el-sub-menu__title) {
    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }
  }

  // 添加垂直指示线
  :deep(.el-menu--inline) {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 24px;
      top: 0;
      bottom: 0;
      width: 1px;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  // &.el-menu {
  //   overflow: auto !important;
  //   &::-webkit-scrollbar {
  //     width: 6px !important;
  //     display: block !important;
  //   }
  //   &::-webkit-scrollbar-thumb {
  //     background-color: rgba(255, 255, 255, 0.2) !important;
  //     border-radius: 3px !important;
  //   }
  // }
}

// 弹出的子菜单样式
:deep(.submenu-popper) {
  .el-menu--popup {
    background-color: #1e293b;
    padding: 0;

    .el-menu-item {
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &.is-active {
        background-color: rgba(64, 158, 255, 0.2);
      }
    }
  }
}
</style>
