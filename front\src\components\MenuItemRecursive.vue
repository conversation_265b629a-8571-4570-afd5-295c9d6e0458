<!-- src/components/MenuItemRecursive.vue -->
<template>
  <el-sub-menu
    v-if="hasChildren"
    :index="menuItem.path || ''"
    :popper-class="'submenu-popper'"
    :data-level="level"  
    @open="handleSubMenuOpen(menuItem.path)"     
  >
    <template #title>
      <div
        class="menu-item-content"
        :style="{ paddingLeft: `${level * 16}px` }"
      >
        <el-icon class="menu-icon">
          <component :is="menuItem.icon" />
        </el-icon>
        <span class="menu-title">{{ menuItem.permission_name }}</span>
      </div>
    </template>
    <menu-item-recursive
      v-for="child in menuItem.children"
      :key="child.permission_id"
      :menu-item="child"
      :level="level + 1"
    />
  </el-sub-menu>

  <el-menu-item v-else :index="menuItem.path || ''" @click="$emit('menu-click', menuItem.permission_name)">
    <div class="menu-item-content" :style="{ paddingLeft: `${level * 16}px` }">
      <el-icon class="menu-icon">
        <component :is="menuItem.icon" />
      </el-icon>
      <span class="menu-title">{{ menuItem.permission_name }}</span>
    </div>
  </el-menu-item>
</template>

<script setup>
import { computed, inject, ref } from "vue";
import { ElMenuItem, ElSubMenu, ElIcon } from "element-plus";

const props = defineProps({
  menuItem: {
    type: Object,
    required: true,
  },
  level: {
    // 添加层级属性
    type: Number,
    default: 0,
  },
});

const hasChildren = computed(() => {
  return props.menuItem.children && props.menuItem.children.length > 0;
});

// 注入全局展开的菜单ID
const openedMenus = inject("openedMenus", ref(new Set()));

// 处理子菜单展开/折叠
const handleSubMenuOpen = (index) => {
  //console.log("Opening submenu:", index);

  // 如果是一级菜单
  if (props.level === 0) {
    // 获取所有一级菜单的index
    const allFirstLevelMenus = document.querySelectorAll(
      '.el-sub-menu[data-level="0"]'
    );
    allFirstLevelMenus.forEach((menu) => {
      if (menu.getAttribute("index") !== index) {
        // 关闭其他一级菜单
        menu.querySelector(".el-sub-menu__title").click();
      }
    });
  }

  // 保存打开状态到 localStorage
  const openedMenusArray = Array.from(openedMenus.value);
  localStorage.setItem("openedMenus", JSON.stringify(openedMenusArray));
};
</script>

<style lang="scss" scoped>
.menu-item-content {
  @apply flex items-center gap-2;
  width: 100%;
  position: relative;

  // 添加左侧层级指示线
  &::before {
    content: "";
    position: absolute;
    left: 4px;
    top: 50%;
    width: 8px;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.menu-icon {
  @apply flex items-center justify-center;
  width: 24px;
  height: 24px;

  :deep(svg) {
    @apply w-4 h-4;
    width: 16px !important;
    height: 16px !important;
  }
}

.menu-title {
  @apply flex-1 truncate;
  font-size: 14px;
}
</style>
