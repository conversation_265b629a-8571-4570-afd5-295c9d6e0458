// src/web/model/location.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Location {
    pub location_id: String,
    pub location_name: String,
    pub location_type: String,
    pub building_id: Option<String>,
    pub floor: Option<i32>,
    pub purpose_type_id: Option<String>,
    pub area: Option<i32>,
    pub capacity: Option<i32>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct LocationQueryRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub building_id: Option<String>,
    pub search_text: Option<String>,
    pub location_type: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct LocationCreateRequest {
    pub location_name: String,
    pub location_type: String,
    pub building_id: Option<String>,
    pub floor: Option<i32>,
    pub purpose_type_id: Option<String>,
    pub area: Option<i32>,
    pub capacity: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct LocationUpdateRequest {
    pub location_id: String,
    pub location_name: Option<String>,
    pub location_type: Option<String>,
    pub building_id: Option<String>,
    pub floor: Option<i32>,
    pub purpose_type_id: Option<String>,
    pub area: Option<i32>,
    pub capacity: Option<i32>,
    pub is_active: Option<bool>,
}
// 分页响应
#[derive(Debug, Serialize)]
pub struct PageResponse<T> {
    pub total: i64,
    pub items: Vec<T>,
}