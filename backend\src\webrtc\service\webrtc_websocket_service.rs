// //src/webrtc/service/webrtc_websocket_service.rs
// use tokio::time::{Duration, interval};
// use futures::StreamExt;
// use axum::extract::ws::{Message, WebSocket};
// use std::sync::Arc;

// use crate::webrtc::model::webrtc_stream::WebrtcStreamState;
// use crate::webrtc::service::webrtc_stream_service::WebrtcStreamService;

// #[derive(Clone, Debug)]
// pub struct WebrtcWebSocketService {
//     stream_state: Arc<WebrtcStreamState>,
//     stream_service: Arc<WebrtcStreamService>,
// }

// impl WebrtcWebSocketService {
//     pub fn new(stream_state: Arc<WebrtcStreamState>, stream_service: Arc<WebrtcStreamService>) -> Self {
//         Self { stream_state, stream_service }
//     }

//     pub async fn handle_connection(
//         &self,
//         stream_id: String,
//         mut socket: WebSocket,
//     ) {
//         let stream_state = self.stream_state.clone();
//         let mut interval = interval(Duration::from_secs(30));

//         // 订阅视频流
//         let mut rx = {
//             let channels = stream_state.stream_channels.read().await;
//             if let Some(tx) = channels.get(&stream_id) {
//                 tx.subscribe()
//             } else {
//                 return;
//             }
//         };

//         loop {
//             tokio::select! {
//                 // 处理心跳
//                 _ = interval.tick() => {
//                     if socket.send(Message::Ping(vec![])).await.is_err() {
//                         break;
//                     }
//                 }
//                 // 处理视频数据
//                 Ok(data) = rx.recv() => {
//                     if socket.send(Message::Binary(data)).await.is_err() {
//                         break;
//                     }
//                 }
//                 // 处理WebSocket消息
//                 Some(Ok(msg)) = socket.next() => {
//                     match msg {
//                         Message::Close(_) => break,
//                         Message::Pong(_) => continue,
//                         _ => continue,
//                     }
//                 }
//                 else => break,
//             }
//         }

//         // 连接断开，减少观看计数
//         let _ = self.stream_service.stop_stream(&stream_id).await;
//     }
// }
